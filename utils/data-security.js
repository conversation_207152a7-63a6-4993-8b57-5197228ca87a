/**
 * 数据安全与权限控制中间件
 * Data Security & Permission Control Middleware
 * 
 * 为财务模块提供数据安全和访问权限控制
 */

const { getCurrentUserPermissions, PERMISSIONS, FinancePermissionChecker } = require('./role-permission.js');

/**
 * 数据敏感度级别
 */
const DATA_SENSITIVITY_LEVELS = {
  PUBLIC: 'public',           // 公开数据
  INTERNAL: 'internal',       // 内部数据
  CONFIDENTIAL: 'confidential', // 机密数据
  SECRET: 'secret'            // 绝密数据
};

/**
 * 数据访问控制策略
 */
const DATA_ACCESS_POLICIES = {
  [DATA_SENSITIVITY_LEVELS.PUBLIC]: {
    viewRoles: ['*'], // 所有角色可查看
    editRoles: ['ADMIN', 'FINANCE_MANAGER', 'MANAGER'],
    deleteRoles: ['ADMIN', 'FINANCE_MANAGER']
  },
  [DATA_SENSITIVITY_LEVELS.INTERNAL]: {
    viewRoles: ['ADMIN', 'FINANCE_MANAGER', 'FINANCE_STAFF', 'MANAGER', 'SUPERVISOR'],
    editRoles: ['ADMIN', 'FINANCE_MANAGER', 'FINANCE_STAFF'],
    deleteRoles: ['ADMIN', 'FINANCE_MANAGER']
  },
  [DATA_SENSITIVITY_LEVELS.CONFIDENTIAL]: {
    viewRoles: ['ADMIN', 'FINANCE_MANAGER'],
    editRoles: ['ADMIN', 'FINANCE_MANAGER'],
    deleteRoles: ['ADMIN']
  },
  [DATA_SENSITIVITY_LEVELS.SECRET]: {
    viewRoles: ['ADMIN'],
    editRoles: ['ADMIN'],
    deleteRoles: ['ADMIN']
  }
};

/**
 * 财务数据敏感度映射
 */
const FINANCE_DATA_SENSITIVITY = {
  // 收支记录
  'expense_records': DATA_SENSITIVITY_LEVELS.INTERNAL,
  'income_records': DATA_SENSITIVITY_LEVELS.INTERNAL,
  
  // 报表数据
  'financial_reports': DATA_SENSITIVITY_LEVELS.CONFIDENTIAL,
  'profit_reports': DATA_SENSITIVITY_LEVELS.CONFIDENTIAL,
  
  // 预算和成本
  'budget_data': DATA_SENSITIVITY_LEVELS.CONFIDENTIAL,
  'cost_analysis': DATA_SENSITIVITY_LEVELS.CONFIDENTIAL,
  
  // 银行账户信息
  'bank_accounts': DATA_SENSITIVITY_LEVELS.SECRET,
  'payment_methods': DATA_SENSITIVITY_LEVELS.SECRET,
  
  // 税务信息
  'tax_records': DATA_SENSITIVITY_LEVELS.SECRET,
  'audit_logs': DATA_SENSITIVITY_LEVELS.SECRET
};

/**
 * 数据安全控制器
 */
class DataSecurityController {
  constructor() {
    this.userPermissions = null;
    this.auditLogger = new AuditLogger();
  }

  /**
   * 初始化安全控制器
   */
  async init() {
    try {
      this.userPermissions = await getCurrentUserPermissions();
      return true;
    } catch (error) {
      console.error('数据安全控制器初始化失败:', error);
      return false;
    }
  }

  /**
   * 检查数据访问权限
   * @param {string} dataType - 数据类型
   * @param {string} operation - 操作类型：view/edit/delete
   * @param {Object} resourceInfo - 资源信息
   * @returns {Promise<boolean>} 是否有权限
   */
  async checkDataAccess(dataType, operation, resourceInfo = {}) {
    try {
      if (!this.userPermissions) {
        await this.init();
      }

      const sensitivity = FINANCE_DATA_SENSITIVITY[dataType] || DATA_SENSITIVITY_LEVELS.INTERNAL;
      const policy = DATA_ACCESS_POLICIES[sensitivity];
      const userRole = this.userPermissions.role;

      // 检查角色权限
      let allowedRoles = [];
      switch (operation) {
        case 'view':
          allowedRoles = policy.viewRoles;
          break;
        case 'edit':
          allowedRoles = policy.editRoles;
          break;
        case 'delete':
          allowedRoles = policy.deleteRoles;
          break;
        default:
          return false;
      }

      // 通用权限检查
      if (allowedRoles.includes('*') || allowedRoles.includes(userRole)) {
        return true;
      }

      // 特殊情况：自有资源权限
      if (resourceInfo.ownerId === this.userPermissions.userId) {
        return this.checkOwnershipAccess(operation, userRole);
      }

      // 部门权限检查
      if (resourceInfo.departmentId === this.userPermissions.departmentId) {
        return this.checkDepartmentAccess(operation, userRole);
      }

      return false;
    } catch (error) {
      console.error('权限检查失败:', error);
      return false;
    }
  }

  /**
   * 检查所有者权限
   * @param {string} operation - 操作类型
   * @param {string} userRole - 用户角色
   * @returns {boolean} 是否有权限
   */
  checkOwnershipAccess(operation, userRole) {
    const ownershipRules = {
      'EMPLOYEE': {
        view: true,
        edit: false,
        delete: false
      },
      'SUPERVISOR': {
        view: true,
        edit: true,
        delete: false
      },
      'MANAGER': {
        view: true,
        edit: true,
        delete: true
      }
    };

    return ownershipRules[userRole]?.[operation] || false;
  }

  /**
   * 检查部门权限
   * @param {string} operation - 操作类型
   * @param {string} userRole - 用户角色
   * @returns {boolean} 是否有权限
   */
  checkDepartmentAccess(operation, userRole) {
    const departmentRules = {
      'SUPERVISOR': {
        view: true,
        edit: false,
        delete: false
      },
      'MANAGER': {
        view: true,
        edit: true,
        delete: false
      },
      'FINANCE_STAFF': {
        view: true,
        edit: true,
        delete: false
      },
      'FINANCE_MANAGER': {
        view: true,
        edit: true,
        delete: true
      }
    };

    return departmentRules[userRole]?.[operation] || false;
  }

  /**
   * 数据过滤器 - 根据权限过滤数据
   * @param {Array} data - 原始数据数组
   * @param {string} dataType - 数据类型
   * @param {Object} filterOptions - 过滤选项
   * @returns {Promise<Array>} 过滤后的数据
   */
  async filterDataByPermission(data, dataType, filterOptions = {}) {
    if (!Array.isArray(data) || data.length === 0) {
      return [];
    }

    const filteredData = [];
    
    for (const item of data) {
      const hasAccess = await this.checkDataAccess(dataType, 'view', {
        ownerId: item.userId || item.createdBy,
        departmentId: item.departmentId,
        ...filterOptions
      });

      if (hasAccess) {
        // 根据权限级别脱敏数据
        const sanitizedItem = this.sanitizeData(item, dataType);
        filteredData.push(sanitizedItem);
      }
    }

    return filteredData;
  }

  /**
   * 数据脱敏处理
   * @param {Object} data - 原始数据
   * @param {string} dataType - 数据类型
   * @returns {Object} 脱敏后的数据
   */
  sanitizeData(data, dataType) {
    const sensitivity = FINANCE_DATA_SENSITIVITY[dataType] || DATA_SENSITIVITY_LEVELS.INTERNAL;
    const userRole = this.userPermissions.role;

    // 复制数据避免修改原对象
    const sanitizedData = { ...data };

    // 根据敏感度和用户角色进行脱敏
    switch (sensitivity) {
      case DATA_SENSITIVITY_LEVELS.CONFIDENTIAL:
        if (!['ADMIN', 'FINANCE_MANAGER'].includes(userRole)) {
          // 隐藏敏感字段
          delete sanitizedData.bankAccount;
          delete sanitizedData.taxId;
          delete sanitizedData.profitMargin;
        }
        break;

      case DATA_SENSITIVITY_LEVELS.SECRET:
        if (userRole !== 'ADMIN') {
          // 完全隐藏或用星号替代
          if (sanitizedData.bankAccount) {
            sanitizedData.bankAccount = '****' + sanitizedData.bankAccount.slice(-4);
          }
          delete sanitizedData.auditDetails;
        }
        break;

      default:
        // INTERNAL 和 PUBLIC 数据正常显示
        break;
    }

    return sanitizedData;
  }

  /**
   * 记录数据访问日志
   * @param {string} operation - 操作类型
   * @param {string} dataType - 数据类型
   * @param {Object} resourceInfo - 资源信息
   * @param {boolean} success - 是否成功
   */
  async logDataAccess(operation, dataType, resourceInfo, success) {
    try {
      await this.auditLogger.log({
        userId: this.userPermissions.userId,
        userRole: this.userPermissions.role,
        operation,
        dataType,
        resourceId: resourceInfo.id || resourceInfo.resourceId,
        success,
        timestamp: new Date().toISOString(),
        ip: resourceInfo.ip || 'unknown',
        userAgent: resourceInfo.userAgent || 'unknown'
      });
    } catch (error) {
      console.error('记录访问日志失败:', error);
    }
  }

  /**
   * 验证数据完整性
   * @param {Object} data - 数据对象
   * @param {Array} requiredFields - 必需字段
   * @returns {Object} 验证结果
   */
  validateDataIntegrity(data, requiredFields = []) {
    const errors = [];
    const warnings = [];

    // 检查必需字段
    requiredFields.forEach(field => {
      if (!data[field]) {
        errors.push(`缺少必需字段: ${field}`);
      }
    });

    // 检查数据类型和格式
    if (data.amount && (typeof data.amount !== 'number' || data.amount < 0)) {
      errors.push('金额必须为非负数');
    }

    if (data.date && !this.isValidDate(data.date)) {
      errors.push('日期格式不正确');
    }

    // 检查敏感信息泄露
    const sensitivePatterns = [
      /\\b\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}\\b/, // 银行卡号
      /\\b\\d{15,19}\\b/, // 其他银行卡号
      /\\b[A-Z]{2}\\d{2}[A-Z0-9]{4}\\d{7}([A-Z0-9]?){0,16}\\b/ // IBAN
    ];

    const textFields = ['description', 'notes', 'remarks'];
    textFields.forEach(field => {
      if (data[field]) {
        sensitivePatterns.forEach(pattern => {
          if (pattern.test(data[field])) {
            warnings.push(`字段 ${field} 可能包含敏感信息`);
          }
        });
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 验证日期格式
   * @param {string} dateString - 日期字符串
   * @returns {boolean} 是否为有效日期
   */
  isValidDate(dateString) {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
  }

  /**
   * 加密敏感数据
   * @param {string} data - 待加密数据
   * @param {string} key - 加密密钥
   * @returns {string} 加密后的数据
   */
  encryptSensitiveData(data, key = 'default_key') {
    // 简单的加密实现，生产环境应使用更安全的加密算法
    try {
      const encoded = Buffer.from(data).toString('base64');
      return encoded.split('').reverse().join('');
    } catch (error) {
      console.error('数据加密失败:', error);
      return data;
    }
  }

  /**
   * 解密敏感数据
   * @param {string} encryptedData - 加密数据
   * @param {string} key - 解密密钥
   * @returns {string} 解密后的数据
   */
  decryptSensitiveData(encryptedData, key = 'default_key') {
    try {
      const reversed = encryptedData.split('').reverse().join('');
      return Buffer.from(reversed, 'base64').toString();
    } catch (error) {
      console.error('数据解密失败:', error);
      return encryptedData;
    }
  }
}

/**
 * 审计日志记录器
 */
class AuditLogger {
  constructor() {
    this.logs = [];
    this.maxLogs = 1000;
  }

  /**
   * 记录审计日志
   * @param {Object} logEntry - 日志条目
   */
  async log(logEntry) {
    try {
      const entry = {
        ...logEntry,
        id: this.generateLogId(),
        timestamp: logEntry.timestamp || new Date().toISOString()
      };

      this.logs.push(entry);
      
      // 保持日志数量在限制内
      if (this.logs.length > this.maxLogs) {
        this.logs = this.logs.slice(-this.maxLogs);
      }

      // 在生产环境中，这里应该写入到数据库或日志文件
      console.log('审计日志:', entry);
      
      // 可选：发送到后端服务器
      // await this.sendToServer(entry);
      
    } catch (error) {
      console.error('记录审计日志失败:', error);
    }
  }

  /**
   * 生成日志ID
   * @returns {string} 日志ID
   */
  generateLogId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 获取审计日志
   * @param {Object} filters - 过滤条件
   * @returns {Array} 日志列表
   */
  getLogs(filters = {}) {
    let filteredLogs = this.logs;

    if (filters.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
    }

    if (filters.operation) {
      filteredLogs = filteredLogs.filter(log => log.operation === filters.operation);
    }

    if (filters.dataType) {
      filteredLogs = filteredLogs.filter(log => log.dataType === filters.dataType);
    }

    if (filters.startTime) {
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= new Date(filters.startTime));
    }

    if (filters.endTime) {
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= new Date(filters.endTime));
    }

    return filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  }

  /**
   * 清理过期日志
   * @param {number} daysToKeep - 保留天数
   */
  cleanupOldLogs(daysToKeep = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    this.logs = this.logs.filter(log => new Date(log.timestamp) > cutoffDate);
  }
}

// 创建单例实例
const dataSecurityController = new DataSecurityController();
const auditLogger = new AuditLogger();

/**
 * 权限检查中间件函数
 * @param {string} dataType - 数据类型
 * @param {string} operation - 操作类型
 * @returns {Function} 中间件函数
 */
function requirePermission(dataType, operation) {
  return async function(req, res, next) {
    try {
      const hasAccess = await dataSecurityController.checkDataAccess(
        dataType, 
        operation, 
        {
          ownerId: req.body?.userId || req.query?.userId,
          departmentId: req.body?.departmentId || req.query?.departmentId,
          ip: req.ip,
          userAgent: req.get('User-Agent')
        }
      );

      if (hasAccess) {
        // 记录成功访问
        await dataSecurityController.logDataAccess(operation, dataType, req.params, true);
        next();
      } else {
        // 记录失败访问
        await dataSecurityController.logDataAccess(operation, dataType, req.params, false);
        
        return res.status(403).json({
          success: false,
          message: '权限不足',
          code: 'INSUFFICIENT_PERMISSION'
        });
      }
    } catch (error) {
      console.error('权限检查中间件错误:', error);
      return res.status(500).json({
        success: false,
        message: '权限验证失败',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
}

// 导出
module.exports = {
  DataSecurityController,
  AuditLogger,
  dataSecurityController,
  auditLogger,
  requirePermission,
  DATA_SENSITIVITY_LEVELS,
  DATA_ACCESS_POLICIES,
  FINANCE_DATA_SENSITIVITY
};