/**
 * 认证和权限辅助工具
 * 统一处理登录状态检查、权限验证、页面跳转等
 */

const app = getApp();

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
function isLoggedIn() {
  const token = wx.getStorageSync('access_token');
  const userInfo = wx.getStorageSync('user_info');
  return !!(token && userInfo);
}

/**
 * 获取当前用户信息
 * @returns {Object|null} 用户信息
 */
function getCurrentUser() {
  if (!isLoggedIn()) {
    return null;
  }
  return wx.getStorageSync('user_info');
}

/**
 * 获取当前用户的访问令牌
 * @returns {string|null} 访问令牌
 */
function getAccessToken() {
  return wx.getStorageSync('access_token');
}

/**
 * 检查用户是否有指定权限
 * @param {string|Array} permissions 权限代码或权限数组
 * @returns {boolean} 是否有权限
 */
function hasPermission(permissions) {
  const user = getCurrentUser();
  if (!user) {
    return false;
  }

  // 管理员拥有所有权限
  if (user.roleCode === 'admin' || user.role === '管理员') {
    return true;
  }

  // 如果用户有权限列表，检查具体权限
  if (user.permissions && Array.isArray(user.permissions)) {
    const requiredPermissions = Array.isArray(permissions) ? permissions : [permissions];
    return requiredPermissions.some(perm => user.permissions.includes(perm));
  }

  // 基于角色的权限检查 - 统一角色映射
  const userRole = user.roleCode || user.role;
  const rolePermissions = {
    // 管理员角色
    'admin': ['*'], // 管理员拥有所有权限
    '管理员': ['*'],
    'super_admin': ['*'],
    'owner': ['*'],
    
    // 经理角色
    'manager': ['reimbursement:*', 'approval:*', 'purchase:*', 'report:read', 'user:read', 'finance:read'],
    '经理': ['reimbursement:*', 'approval:*', 'purchase:*', 'report:read', 'user:read', 'finance:read'],
    'department_manager': ['reimbursement:*', 'approval:*', 'purchase:*', 'report:read', 'user:read'],
    'finance_manager': ['reimbursement:*', 'approval:*', 'finance:*', 'report:*'],
    
    // 员工角色
    'employee': ['reimbursement:create', 'reimbursement:read', 'purchase:create', 'purchase:read'],
    '员工': ['reimbursement:create', 'reimbursement:read', 'purchase:create', 'purchase:read'],
    'staff': ['reimbursement:create', 'reimbursement:read', 'purchase:create', 'purchase:read'],
    
    // 专门角色
    'hr': ['system:user:manage', 'system:role:manage', 'user:*'],
    'finance': ['reimbursement:*', 'finance:*', 'report:*'],
    '财务': ['reimbursement:*', 'finance:*', 'report:*']
  };

  const userPerms = rolePermissions[userRole] || [];
  const requiredPermissions = Array.isArray(permissions) ? permissions : [permissions];
  
  return requiredPermissions.some(perm => {
    return userPerms.includes('*') || userPerms.includes(perm) || 
           userPerms.some(userPerm => userPerm.endsWith(':*') && perm.startsWith(userPerm.slice(0, -1)));
  });
}

/**
 * 检查用户是否有指定角色
 * @param {string|Array} roles 角色代码或角色数组
 * @returns {boolean} 是否有角色
 */
function hasRole(roles) {
  const user = getCurrentUser();
  if (!user) {
    return false;
  }

  const userRole = user.roleCode || user.role;
  const requiredRoles = Array.isArray(roles) ? roles : [roles];
  
  return requiredRoles.includes(userRole);
}

/**
 * 页面权限检查中间件
 * 在页面onLoad中调用，检查权限并处理跳转
 * @param {Object} options 页面参数
 * @param {string|Array} requiredPermissions 必需的权限
 * @param {Function} callback 权限检查通过后的回调
 */
function checkPagePermission(options, requiredPermissions, callback) {
  // 检查登录状态
  if (!isLoggedIn()) {
    wx.showToast({
      title: '请先登录',
      icon: 'none'
    });
    
    setTimeout(() => {
      wx.reLaunch({
        url: '/pages/login/login'
      });
    }, 1500);
    return false;
  }

  // 检查权限
  if (requiredPermissions && !hasPermission(requiredPermissions)) {
    // 静默跳转，不显示提示
    wx.navigateBack({
      delta: 1
    });
    return false;
  }

  // 权限检查通过，执行回调
  if (typeof callback === 'function') {
    callback(options);
  }
  
  return true;
}

/**
 * 安全的页面跳转
 * 在跳转前检查权限
 * @param {string} url 目标页面URL
 * @param {string|Array} requiredPermissions 必需的权限
 * @param {Object} options 跳转选项
 */
function navigateToPage(url, requiredPermissions, options = {}) {
  // 检查登录状态
  if (!isLoggedIn()) {
    wx.showToast({
      title: '请先登录',
      icon: 'none'
    });
    return;
  }

  // 检查权限
  if (requiredPermissions && !hasPermission(requiredPermissions)) {
    wx.showToast({
      title: '权限不足',
      icon: 'none'
    });
    return;
  }

  // 执行跳转
  const navigateMethod = options.method || 'navigateTo';
  const navigateOptions = {
    url,
    success: options.success,
    fail: options.fail,
    complete: options.complete
  };

  switch (navigateMethod) {
  case 'switchTab':
    wx.switchTab(navigateOptions);
    break;
  case 'reLaunch':
    wx.reLaunch(navigateOptions);
    break;
  case 'redirectTo':
    wx.redirectTo(navigateOptions);
    break;
  case 'navigateBack':
    wx.navigateBack({ delta: options.delta || 1 });
    break;
  default:
    wx.navigateTo(navigateOptions);
  }
}

/**
 * 清除登录状态
 */
function clearLoginState() {
  wx.removeStorageSync('access_token');
  wx.removeStorageSync('refresh_token');
  wx.removeStorageSync('user_info');
  
  // 清除全局数据
  if (app && app.globalData) {
    app.globalData.userInfo = {};
    app.globalData.token = '';
  }
}

/**
 * 处理认证错误
 * @param {Object} error 错误对象
 */
function handleAuthError(error) {
  try { const logger = require('./logger.js'); logger.error && logger.error('认证错误', error); } catch(_) {}
  
  // 如果是401错误，清除登录状态并跳转到登录页
  if (error.statusCode === 401 || error.code === 'UNAUTHORIZED') {
    clearLoginState();
    
    wx.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none'
    });
    
    setTimeout(() => {
      wx.reLaunch({
        url: '/pages/login/login'
      });
    }, 1500);
  } else if (error.statusCode === 403 || error.code === 'FORBIDDEN') {
    wx.showToast({
      title: '权限不足',
      icon: 'none'
    });
  }
}

/**
 * 刷新用户信息
 * @returns {Promise} 刷新结果
 */
function refreshUserInfo() {
  return new Promise((resolve, reject) => {
    const { request } = require('./request.js');
    
    request({
      url: '/auth/user-info',
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.success) {
          const userInfo = res.data.data;
          wx.setStorageSync('user_info', userInfo);
          
          if (app && app.globalData) {
            app.globalData.userInfo = userInfo;
          }
          
          resolve(userInfo);
        } else {
          reject(new Error(res.data?.message || '获取用户信息失败'));
        }
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
}

module.exports = {
  isLoggedIn,
  getCurrentUser,
  getAccessToken,
  hasPermission,
  hasRole,
  checkPagePermission,
  navigateToPage,
  clearLoginState,
  handleAuthError,
  refreshUserInfo
};
