/**
 * 财务报表模拟数据服务
 * 用于生成各种类型的财务报表测试数据
 */

const logger = require('./logger.js');

class FinanceMockDataService {
  
  /**
   * 生成财务总览数据
   * @param {Object} params - 查询参数
   * @param {string} params.timeRange - 时间范围 (week/month/quarter/year/custom)
   * @param {string} params.startDate - 开始日期 (自定义时间范围)
   * @param {string} params.endDate - 结束日期 (自定义时间范围)
   * @returns {Object} 财务总览数据
   */
  static generateOverviewData(params = {}) {
    const { timeRange = 'month' } = params;
    
    // 根据时间范围生成不同规模的数据
    const multipliers = {
      week: 0.25,
      month: 1,
      quarter: 3,
      year: 12,
      custom: 1
    };
    
    const multiplier = multipliers[timeRange] || 1;
    
    // 基础数据
    const baseIncome = 50000;
    const baseExpense = 35000;
    
    const totalIncome = Math.round(baseIncome * multiplier * (0.8 + Math.random() * 0.4));
    const totalExpense = Math.round(baseExpense * multiplier * (0.8 + Math.random() * 0.4));
    const netProfit = totalIncome - totalExpense;
    
    const transactionCount = Math.round(15 * multiplier * (0.8 + Math.random() * 0.4));
    const avgTransactionAmount = transactionCount > 0 ? Math.round((totalIncome + totalExpense) / transactionCount) : 0;
    
    // 增长率（模拟同比数据）
    const incomeGrowth = -10 + Math.random() * 30; // -10% 到 +20%
    const expenseGrowth = -5 + Math.random() * 20;  // -5% 到 +15%
    
    return {
      totalIncome,
      totalExpense,
      netProfit,
      transactionCount,
      avgTransactionAmount,
      incomeGrowth: Math.round(incomeGrowth * 10) / 10,
      expenseGrowth: Math.round(expenseGrowth * 10) / 10
    };
  }
  
  /**
   * 生成分类统计数据
   * @param {Object} params - 查询参数
   * @returns {Array} 分类统计数据
   */
  static generateCategoryStats(params = {}) {
    const { timeRange = 'month' } = params;
    const multiplier = timeRange === 'week' ? 0.25 : timeRange === 'quarter' ? 3 : timeRange === 'year' ? 12 : 1;

    // 定义业务分类，包含收入和支出
    const businessCategories = [
      {
        category: '鹅苗销售',
        icon: '🐣',
        incomeBase: 25000,
        expenseBase: 2000 // 包装、运输等成本
      },
      {
        category: '成鹅销售',
        icon: '🦢',
        incomeBase: 35000,
        expenseBase: 5000 // 屠宰、包装等成本
      },
      {
        category: '鹅蛋销售',
        icon: '🥚',
        incomeBase: 8000,
        expenseBase: 1000 // 包装、保鲜等成本
      },
      {
        category: '羽毛销售',
        icon: '🪶',
        incomeBase: 3000,
        expenseBase: 500 // 处理、包装成本
      },
      {
        category: '饲料采购',
        icon: '🌾',
        incomeBase: 0,
        expenseBase: 15000 // 纯支出项目
      },
      {
        category: '疫苗药品',
        icon: '💊',
        incomeBase: 0,
        expenseBase: 8000 // 纯支出项目
      },
      {
        category: '设备维护',
        icon: '🔧',
        incomeBase: 0,
        expenseBase: 5000 // 纯支出项目
      },
      {
        category: '人工费用',
        icon: '👥',
        incomeBase: 0,
        expenseBase: 12000 // 纯支出项目
      }
    ];

    const stats = [];

    // 为每个分类生成聚合数据
    businessCategories.forEach(cat => {
      const income = cat.incomeBase > 0 ?
        Math.round(cat.incomeBase * multiplier * (0.8 + Math.random() * 0.4)) : 0;
      const expense = cat.expenseBase > 0 ?
        Math.round(cat.expenseBase * multiplier * (0.7 + Math.random() * 0.6)) : 0;
      const net = income - expense;

      // 计算交易次数
      const incomeCount = income > 0 ? Math.round((2 + Math.random() * 4) * multiplier) : 0;
      const expenseCount = expense > 0 ? Math.round((3 + Math.random() * 5) * multiplier) : 0;

      stats.push({
        category: cat.category,
        income,
        expense,
        net,
        icon: cat.icon,
        incomeCount,
        expenseCount,
        totalCount: incomeCount + expenseCount
      });
    });

    // 按净收益排序（从高到低）
    return stats.sort((a, b) => b.net - a.net);
  }
  
  /**
   * 生成趋势分析数据
   * @param {Object} params - 查询参数
   * @returns {Array} 趋势数据
   */
  static generateTrendData(params = {}) {
    const { timeRange = 'month' } = params;
    
    let days = 30;
    let interval = 1; // 天
    
    switch (timeRange) {
      case 'week':
        days = 7;
        interval = 1;
        break;
      case 'month':
        days = 30;
        interval = 1;
        break;
      case 'quarter':
        days = 90;
        interval = 3;
        break;
      case 'year':
        days = 365;
        interval = 30;
        break;
    }
    
    const trendData = [];
    const today = new Date();
    
    for (let i = days - 1; i >= 0; i -= interval) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      // 生成波动的收支数据
      const baseIncome = 1500 + Math.sin(i * 0.1) * 500;
      const baseExpense = 1200 + Math.sin(i * 0.15) * 300;
      
      const income = Math.round(baseIncome * (0.7 + Math.random() * 0.6));
      const expense = Math.round(baseExpense * (0.7 + Math.random() * 0.6));
      const profit = income - expense;
      
      trendData.push({
        date: date.toISOString().split('T')[0],
        income,
        expense,
        profit,
        formattedDate: this.formatDate(date, timeRange)
      });
    }
    
    return trendData;
  }
  
  /**
   * 生成健康财务整合数据
   * @param {Object} params - 查询参数
   * @returns {Object} 健康财务数据
   */
  static generateHealthIntegrationData(params = {}) {
    const { timeRange = 'month' } = params;
    const multiplier = timeRange === 'week' ? 0.25 : timeRange === 'quarter' ? 3 : timeRange === 'year' ? 12 : 1;
    
    const totalHealthExpense = Math.round(15000 * multiplier * (0.8 + Math.random() * 0.4));
    const totalProductionIncome = Math.round(45000 * multiplier * (0.8 + Math.random() * 0.4));
    const netHealthProfit = totalProductionIncome - totalHealthExpense;
    const healthExpenseRatio = totalProductionIncome > 0 ? 
      Math.round((totalHealthExpense / totalProductionIncome) * 1000) / 10 : 0;
    
    const healthCategories = [
      {
        category: '疫苗接种',
        amount: Math.round(8000 * multiplier * (0.8 + Math.random() * 0.4)),
        count: Math.round(5 * multiplier),
        icon: '💉'
      },
      {
        category: '药品采购',
        amount: Math.round(5000 * multiplier * (0.8 + Math.random() * 0.4)),
        count: Math.round(3 * multiplier),
        icon: '💊'
      },
      {
        category: '健康检查',
        amount: Math.round(2000 * multiplier * (0.8 + Math.random() * 0.4)),
        count: Math.round(2 * multiplier),
        icon: '🔬'
      }
    ];
    
    return {
      totalHealthExpense,
      totalProductionIncome,
      netHealthProfit,
      healthExpenseRatio,
      healthCategories
    };
  }
  
  /**
   * 生成对比分析数据
   * @param {Object} params - 查询参数
   * @returns {Object} 对比数据
   */
  static generateComparisonData(params = {}) {
    const currentData = this.generateOverviewData(params);
    
    // 生成上期数据（模拟同比或环比）
    const previousData = {
      totalIncome: Math.round(currentData.totalIncome * (0.85 + Math.random() * 0.3)),
      totalExpense: Math.round(currentData.totalExpense * (0.9 + Math.random() * 0.2)),
      transactionCount: Math.round(currentData.transactionCount * (0.8 + Math.random() * 0.4))
    };
    
    previousData.netProfit = previousData.totalIncome - previousData.totalExpense;
    
    // 计算变化
    const changes = {
      incomeChange: currentData.totalIncome - previousData.totalIncome,
      expenseChange: currentData.totalExpense - previousData.totalExpense,
      profitChange: currentData.netProfit - previousData.netProfit,
      transactionChange: currentData.transactionCount - previousData.transactionCount
    };
    
    // 计算变化率
    const changeRates = {
      incomeChangeRate: previousData.totalIncome > 0 ? 
        Math.round((changes.incomeChange / previousData.totalIncome) * 1000) / 10 : 0,
      expenseChangeRate: previousData.totalExpense > 0 ? 
        Math.round((changes.expenseChange / previousData.totalExpense) * 1000) / 10 : 0,
      profitChangeRate: Math.abs(previousData.netProfit) > 0 ? 
        Math.round((changes.profitChange / Math.abs(previousData.netProfit)) * 1000) / 10 : 0,
      transactionChangeRate: previousData.transactionCount > 0 ? 
        Math.round((changes.transactionChange / previousData.transactionCount) * 1000) / 10 : 0
    };
    
    return {
      current: currentData,
      previous: previousData,
      changes,
      changeRates
    };
  }
  
  /**
   * 格式化日期
   * @param {Date} date - 日期对象
   * @param {string} timeRange - 时间范围
   * @returns {string} 格式化后的日期
   */
  static formatDate(date, timeRange) {
    switch (timeRange) {
      case 'week':
      case 'month':
        return `${date.getMonth() + 1}/${date.getDate()}`;
      case 'quarter':
      case 'year':
        return `${date.getMonth() + 1}月`;
      default:
        return `${date.getMonth() + 1}/${date.getDate()}`;
    }
  }
  
  /**
   * 获取所有报表数据
   * @param {Object} params - 查询参数
   * @returns {Object} 完整的报表数据
   */
  static getAllReportData(params = {}) {
    try {
      return {
        overview: this.generateOverviewData(params),
        categoryStats: this.generateCategoryStats(params),
        trendData: this.generateTrendData(params),
        healthIntegrationStats: this.generateHealthIntegrationData(params),
        comparisonData: this.generateComparisonData(params)
      };
    } catch (error) {
      logger.error('生成模拟数据失败:', error);
      return null;
    }
  }
}

module.exports = FinanceMockDataService;
