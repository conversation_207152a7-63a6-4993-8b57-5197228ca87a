/**
 * 图表辅助工具
 * Chart Helper Utility
 * 
 * 为财务报表提供图表生成和渲染支持
 */

/**
 * 图表配置常量
 */
const CHART_COLORS = [
  '#667eea', '#764ba2', '#f093fb', '#f5576c', 
  '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
  '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'
];

const CHART_THEMES = {
  light: {
    backgroundColor: '#ffffff',
    textColor: '#2c3e50',
    gridColor: '#e8ecf0',
    axisColor: '#95a5a6'
  },
  dark: {
    backgroundColor: '#2c3e50',
    textColor: '#ecf0f1',
    gridColor: '#576574',
    axisColor: '#bdc3c7'
  }
};

/**
 * 图表辅助类
 */
class ChartHelper {
  constructor() {
    this.ctx = null;
    this.canvasId = '';
    this.theme = 'light';
  }

  /**
   * 初始化图表上下文
   * @param {string} canvasId - 画布ID
   * @param {Object} component - 页面组件实例
   * @param {string} theme - 主题：light/dark
   */
  init(canvasId, component, theme = 'light') {
    this.canvasId = canvasId;
    this.theme = theme;
    this.ctx = wx.createCanvasContext(canvasId, component);
    return this;
  }

  /**
   * 绘制折线图
   * @param {Object} config - 图表配置
   * @param {Array} data - 数据数组
   */
  drawLineChart(config, data) {
    if (!this.ctx || !data || data.length === 0) return;

    const {
      width = 300,
      height = 200,
      padding = { top: 30, right: 30, bottom: 50, left: 50 },
      title = '',
      xAxisLabel = '',
      yAxisLabel = ''
    } = config;

    this.clearCanvas(width, height);
    
    const chartArea = {
      x: padding.left,
      y: padding.top,
      width: width - padding.left - padding.right,
      height: height - padding.top - padding.bottom
    };

    // 绘制背景
    this.drawBackground(width, height);
    
    // 绘制标题
    if (title) {
      this.drawTitle(title, width / 2, 20);
    }

    // 计算数据范围
    const values = data.map(item => item.value);
    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);
    const valueRange = maxValue - minValue || 1;

    // 绘制网格和坐标轴
    this.drawGrid(chartArea, data.length - 1, 5);
    this.drawAxes(chartArea, data, minValue, maxValue);

    // 绘制折线
    this.drawLine(chartArea, data, minValue, valueRange);
    
    // 绘制数据点
    this.drawDataPoints(chartArea, data, minValue, valueRange);

    // 绘制轴标签
    if (xAxisLabel) {
      this.drawAxisLabel(xAxisLabel, width / 2, height - 10, 'center');
    }
    if (yAxisLabel) {
      this.drawAxisLabel(yAxisLabel, 15, height / 2, 'center', -90);
    }

    this.ctx.draw();
  }

  /**
   * 绘制柱状图
   * @param {Object} config - 图表配置
   * @param {Array} data - 数据数组
   */
  drawBarChart(config, data) {
    if (!this.ctx || !data || data.length === 0) return;

    const {
      width = 300,
      height = 200,
      padding = { top: 30, right: 30, bottom: 50, left: 50 },
      title = '',
      barWidth = 30
    } = config;

    this.clearCanvas(width, height);
    
    const chartArea = {
      x: padding.left,
      y: padding.top,
      width: width - padding.left - padding.right,
      height: height - padding.top - padding.bottom
    };

    this.drawBackground(width, height);
    
    if (title) {
      this.drawTitle(title, width / 2, 20);
    }

    const values = data.map(item => item.value);
    const maxValue = Math.max(...values) || 1;
    const minValue = Math.min(0, Math.min(...values));
    const valueRange = maxValue - minValue;

    this.drawGrid(chartArea, data.length, 5);
    this.drawAxes(chartArea, data, minValue, maxValue);

    // 绘制柱子
    const actualBarWidth = Math.min(barWidth, chartArea.width / data.length * 0.6);
    data.forEach((item, index) => {
      const x = chartArea.x + (chartArea.width / data.length) * index + 
                (chartArea.width / data.length - actualBarWidth) / 2;
      const barHeight = Math.abs(item.value) / valueRange * chartArea.height;
      const y = item.value >= 0 
        ? chartArea.y + chartArea.height - barHeight
        : chartArea.y + chartArea.height;

      const colorIndex = index % CHART_COLORS.length;
      this.ctx.setFillStyle(CHART_COLORS[colorIndex]);
      this.ctx.fillRect(x, y, actualBarWidth, barHeight);

      // 绘制数值标签
      this.ctx.setFillStyle(CHART_THEMES[this.theme].textColor);
      this.ctx.setFontSize(12);
      this.ctx.setTextAlign('center');
      this.ctx.fillText(
        item.value.toFixed(1), 
        x + actualBarWidth / 2, 
        y - 5
      );
    });

    this.ctx.draw();
  }

  /**
   * 绘制饼图
   * @param {Object} config - 图表配置
   * @param {Array} data - 数据数组
   */
  drawPieChart(config, data) {
    if (!this.ctx || !data || data.length === 0) return;

    const {
      width = 300,
      height = 250,
      title = '',
      radius = 80,
      showLabels = true,
      showLegend = true
    } = config;

    this.clearCanvas(width, height);
    this.drawBackground(width, height);

    if (title) {
      this.drawTitle(title, width / 2, 20);
    }

    const centerX = width / 2;
    const centerY = height / 2 + (title ? 10 : 0);
    const total = data.reduce((sum, item) => sum + item.value, 0);

    let currentAngle = -Math.PI / 2; // 从顶部开始

    data.forEach((item, index) => {
      const sliceAngle = (item.value / total) * 2 * Math.PI;
      const colorIndex = index % CHART_COLORS.length;

      // 绘制扇形
      this.ctx.beginPath();
      this.ctx.moveTo(centerX, centerY);
      this.ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
      this.ctx.closePath();
      this.ctx.setFillStyle(CHART_COLORS[colorIndex]);
      this.ctx.fill();

      // 绘制边框
      this.ctx.beginPath();
      this.ctx.moveTo(centerX, centerY);
      this.ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
      this.ctx.closePath();
      this.ctx.setStrokeStyle('#ffffff');
      this.ctx.setLineWidth(2);
      this.ctx.stroke();

      // 绘制标签
      if (showLabels && item.value > 0) {
        const labelAngle = currentAngle + sliceAngle / 2;
        const labelRadius = radius * 0.7;
        const labelX = centerX + Math.cos(labelAngle) * labelRadius;
        const labelY = centerY + Math.sin(labelAngle) * labelRadius;
        
        const percentage = ((item.value / total) * 100).toFixed(1);
        this.ctx.setFillStyle('#ffffff');
        this.ctx.setFontSize(12);
        this.ctx.setTextAlign('center');
        this.ctx.fillText(`${percentage}%`, labelX, labelY);
      }

      currentAngle += sliceAngle;
    });

    // 绘制图例
    if (showLegend) {
      this.drawPieLegend(data, 20, height - 40);
    }

    this.ctx.draw();
  }

  /**
   * 绘制环形图
   * @param {Object} config - 图表配置
   * @param {Array} data - 数据数组
   */
  drawDoughnutChart(config, data) {
    const { innerRadius = 40 } = config;
    
    // 先绘制饼图
    this.drawPieChart(config, data);

    // 然后在中心绘制白色圆形形成环形效果
    const centerX = config.width / 2;
    const centerY = config.height / 2 + (config.title ? 10 : 0);

    this.ctx.beginPath();
    this.ctx.arc(centerX, centerY, innerRadius, 0, 2 * Math.PI);
    this.ctx.setFillStyle(CHART_THEMES[this.theme].backgroundColor);
    this.ctx.fill();

    this.ctx.draw();
  }

  /**
   * 清空画布
   */
  clearCanvas(width, height) {
    this.ctx.clearRect(0, 0, width, height);
  }

  /**
   * 绘制背景
   */
  drawBackground(width, height) {
    this.ctx.setFillStyle(CHART_THEMES[this.theme].backgroundColor);
    this.ctx.fillRect(0, 0, width, height);
  }

  /**
   * 绘制标题
   */
  drawTitle(title, x, y) {
    this.ctx.setFillStyle(CHART_THEMES[this.theme].textColor);
    this.ctx.setFontSize(16);
    this.ctx.setTextAlign('center');
    this.ctx.fillText(title, x, y);
  }

  /**
   * 绘制网格
   */
  drawGrid(chartArea, xLines, yLines) {
    this.ctx.setStrokeStyle(CHART_THEMES[this.theme].gridColor);
    this.ctx.setLineWidth(1);

    // 垂直网格线
    for (let i = 0; i <= xLines; i++) {
      const x = chartArea.x + (chartArea.width / xLines) * i;
      this.ctx.beginPath();
      this.ctx.moveTo(x, chartArea.y);
      this.ctx.lineTo(x, chartArea.y + chartArea.height);
      this.ctx.stroke();
    }

    // 水平网格线
    for (let i = 0; i <= yLines; i++) {
      const y = chartArea.y + (chartArea.height / yLines) * i;
      this.ctx.beginPath();
      this.ctx.moveTo(chartArea.x, y);
      this.ctx.lineTo(chartArea.x + chartArea.width, y);
      this.ctx.stroke();
    }
  }

  /**
   * 绘制坐标轴
   */
  drawAxes(chartArea, data, minValue, maxValue) {
    this.ctx.setStrokeStyle(CHART_THEMES[this.theme].axisColor);
    this.ctx.setLineWidth(2);

    // X轴
    this.ctx.beginPath();
    this.ctx.moveTo(chartArea.x, chartArea.y + chartArea.height);
    this.ctx.lineTo(chartArea.x + chartArea.width, chartArea.y + chartArea.height);
    this.ctx.stroke();

    // Y轴
    this.ctx.beginPath();
    this.ctx.moveTo(chartArea.x, chartArea.y);
    this.ctx.lineTo(chartArea.x, chartArea.y + chartArea.height);
    this.ctx.stroke();

    // X轴标签
    this.ctx.setFillStyle(CHART_THEMES[this.theme].textColor);
    this.ctx.setFontSize(12);
    this.ctx.setTextAlign('center');
    data.forEach((item, index) => {
      const x = chartArea.x + (chartArea.width / (data.length - 1)) * index;
      this.ctx.fillText(
        item.label || index.toString(), 
        x, 
        chartArea.y + chartArea.height + 20
      );
    });

    // Y轴标签
    this.ctx.setTextAlign('right');
    const ySteps = 5;
    for (let i = 0; i <= ySteps; i++) {
      const value = minValue + (maxValue - minValue) * (ySteps - i) / ySteps;
      const y = chartArea.y + (chartArea.height / ySteps) * i;
      this.ctx.fillText(
        value.toFixed(1), 
        chartArea.x - 10, 
        y + 4
      );
    }
  }

  /**
   * 绘制折线
   */
  drawLine(chartArea, data, minValue, valueRange) {
    if (data.length < 2) return;

    this.ctx.setStrokeStyle(CHART_COLORS[0]);
    this.ctx.setLineWidth(3);
    this.ctx.beginPath();

    data.forEach((item, index) => {
      const x = chartArea.x + (chartArea.width / (data.length - 1)) * index;
      const y = chartArea.y + chartArea.height - 
                ((item.value - minValue) / valueRange) * chartArea.height;
      
      if (index === 0) {
        this.ctx.moveTo(x, y);
      } else {
        this.ctx.lineTo(x, y);
      }
    });

    this.ctx.stroke();
  }

  /**
   * 绘制数据点
   */
  drawDataPoints(chartArea, data, minValue, valueRange) {
    data.forEach((item, index) => {
      const x = chartArea.x + (chartArea.width / (data.length - 1)) * index;
      const y = chartArea.y + chartArea.height - 
                ((item.value - minValue) / valueRange) * chartArea.height;
      
      this.ctx.beginPath();
      this.ctx.arc(x, y, 4, 0, 2 * Math.PI);
      this.ctx.setFillStyle(CHART_COLORS[0]);
      this.ctx.fill();
      
      this.ctx.beginPath();
      this.ctx.arc(x, y, 4, 0, 2 * Math.PI);
      this.ctx.setStrokeStyle('#ffffff');
      this.ctx.setLineWidth(2);
      this.ctx.stroke();
    });
  }

  /**
   * 绘制轴标签
   */
  drawAxisLabel(label, x, y, align = 'center', rotation = 0) {
    this.ctx.setFillStyle(CHART_THEMES[this.theme].textColor);
    this.ctx.setFontSize(14);
    this.ctx.setTextAlign(align);
    
    if (rotation !== 0) {
      this.ctx.save();
      this.ctx.translate(x, y);
      this.ctx.rotate(rotation * Math.PI / 180);
      this.ctx.fillText(label, 0, 0);
      this.ctx.restore();
    } else {
      this.ctx.fillText(label, x, y);
    }
  }

  /**
   * 绘制饼图图例
   */
  drawPieLegend(data, startX, startY) {
    const itemHeight = 20;
    const rectSize = 12;

    data.forEach((item, index) => {
      const y = startY + index * itemHeight;
      const colorIndex = index % CHART_COLORS.length;

      // 绘制颜色方块
      this.ctx.setFillStyle(CHART_COLORS[colorIndex]);
      this.ctx.fillRect(startX, y - rectSize / 2, rectSize, rectSize);

      // 绘制文本
      this.ctx.setFillStyle(CHART_THEMES[this.theme].textColor);
      this.ctx.setFontSize(12);
      this.ctx.setTextAlign('left');
      this.ctx.fillText(
        item.label || `项目 ${index + 1}`, 
        startX + rectSize + 8, 
        y + 4
      );
    });
  }

  /**
   * 设置主题
   */
  setTheme(theme) {
    this.theme = theme;
    return this;
  }

  /**
   * 获取当前主题配置
   */
  getThemeConfig() {
    return CHART_THEMES[this.theme];
  }
}

/**
 * 数据格式化工具
 */
class DataFormatter {
  /**
   * 格式化趋势数据
   * @param {Array} rawData - 原始数据
   * @param {string} timeFormat - 时间格式
   * @returns {Array} 格式化后的数据
   */
  static formatTrendData(rawData, timeFormat = 'MM/DD') {
    return rawData.map(item => ({
      label: this.formatDate(item.date, timeFormat),
      value: parseFloat(item.value || 0),
      date: item.date,
      originalData: item
    }));
  }

  /**
   * 格式化分类数据
   * @param {Array} rawData - 原始数据
   * @returns {Array} 格式化后的数据
   */
  static formatCategoryData(rawData) {
    return rawData.map(item => ({
      label: item.categoryName || item.label,
      value: parseFloat(item.totalAmount || item.value || 0),
      count: parseInt(item.count || 0),
      percentage: parseFloat(item.percentage || 0),
      originalData: item
    }));
  }

  /**
   * 格式化日期
   * @param {string} dateString - 日期字符串
   * @param {string} format - 格式
   * @returns {string} 格式化后的日期
   */
  static formatDate(dateString, format = 'MM/DD') {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const year = date.getFullYear();

    switch (format) {
      case 'MM/DD':
        return `${month}/${day}`;
      case 'YYYY-MM-DD':
        return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
      case 'MM-DD':
        return `${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
      default:
        return `${month}/${day}`;
    }
  }

  /**
   * 生成颜色数组
   * @param {number} count - 需要的颜色数量
   * @returns {Array} 颜色数组
   */
  static generateColors(count) {
    const colors = [];
    for (let i = 0; i < count; i++) {
      colors.push(CHART_COLORS[i % CHART_COLORS.length]);
    }
    return colors;
  }
}

// 导出
module.exports = {
  ChartHelper,
  DataFormatter,
  CHART_COLORS,
  CHART_THEMES
};