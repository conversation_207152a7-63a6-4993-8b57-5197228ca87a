/**
 * 健康管理与财务模块数据关联服务
 * Health-Finance Integration Service
 *
 * 实现健康管理页面的业务数据与财务模块的关联整合
 */

const request = require('./request');
const { API } = require('../constants/index');
const { getCurrentUserPermissions, PERMISSIONS } = require('./role-permission');

/**
 * 数据关联类型映射
 */
const INTEGRATION_TYPES = {
  HEALTH_CHECK: 'health_check',        // 健康检查 -> 医疗费用
  MEDICATION: 'medication',            // 用药记录 -> 医药费用
  VACCINATION: 'vaccination',          // 疫苗接种 -> 疫苗费用
  FEED_PURCHASE: 'feed_purchase',      // 饲料采购 -> 采购费用
  EQUIPMENT: 'equipment',              // 设备购置 -> 设备费用
  MAINTENANCE: 'maintenance',          // 设备维护 -> 维修费用
  PRODUCTION_INCOME: 'production_income', // 生产收入 -> 销售收入
  INVENTORY_COST: 'inventory_cost'     // 库存成本 -> 材料成本
};

/**
 * 财务分类映射
 */
const FINANCE_CATEGORY_MAPPING = {
  [INTEGRATION_TYPES.HEALTH_CHECK]: 'medical',
  [INTEGRATION_TYPES.MEDICATION]: 'medical',
  [INTEGRATION_TYPES.VACCINATION]: 'medical',
  [INTEGRATION_TYPES.FEED_PURCHASE]: 'material',
  [INTEGRATION_TYPES.EQUIPMENT]: 'equipment',
  [INTEGRATION_TYPES.MAINTENANCE]: 'maintenance',
  [INTEGRATION_TYPES.PRODUCTION_INCOME]: 'income',
  [INTEGRATION_TYPES.INVENTORY_COST]: 'material'
};

/**
 * 健康财务数据整合服务类
 */
class HealthFinanceIntegrationService {
  constructor() {
    this.userPermissions = null;
  }

  /**
   * 初始化服务
   */
  async init() {
    try {
      this.userPermissions = await getCurrentUserPermissions();
      return true;
    } catch (error) {
      console.error('健康财务整合服务初始化失败:', error);
      return false;
    }
  }

  /**
   * 获取健康管理相关的财务统计
   * @param {Object} filters 筛选条件
   * @returns {Promise<Object>} 财务统计数据
   */
  async getHealthFinanceStatistics(filters = {}) {
    try {
      // 返回模拟数据
      return this.getEmptyHealthFinanceStats();
    } catch (error) {
      console.error('获取健康财务统计失败:', error);
      return this.getEmptyHealthFinanceStats();
    }
  }

  /**
   * 获取空的健康财务统计数据
   * @returns {Object} 空统计数据
   */
  getEmptyHealthFinanceStats() {
    return {
      totalHealthExpense: 0,
      totalProductionIncome: 0,
      totalPurchaseExpense: 0,
      netHealthProfit: 0,
      categoryBreakdown: {},
      monthlyTrend: [],
      integrationStats: {
        healthRecordsSynced: 0,
        productionRecordsSynced: 0,
        purchaseRecordsSynced: 0,
        lastSyncTime: null
      }
    };
  }
}

// 创建单例实例
const healthFinanceIntegration = new HealthFinanceIntegrationService();

module.exports = {
  HealthFinanceIntegrationService,
  healthFinanceIntegration,
  INTEGRATION_TYPES,
  FINANCE_CATEGORY_MAPPING
};