/**
 * 微信小程序统一Loading管理
 * 严格遵循微信小程序 showLoading 与 hideLoading 必须配对使用的规范
 * 
 * 设计原则：
 * 1. 使用栈结构管理嵌套loading调用
 * 2. 确保每个showLoading都有对应的hideLoading
 * 3. 提供自动清理机制防止loading卡死
 * 4. 全局单例模式，避免多个实例冲突
 */

class UnifiedLoadingManager {
  constructor() {
    this.loadingStack = [];
    this.currentTimer = null;
    this.maxTimeout = 10000; // 10秒最大超时
  }

  /**
   * 显示Loading
   * @param {Object} options 配置选项
   * @param {string} options.title 显示文字
   * @param {boolean} options.mask 是否显示遮罩
   * @returns {string} loading ID，用于后续hide操作
   */
  show(options = {}) {
    const loadingId = this.generateId();
    const config = {
      id: loadingId,
      title: options.title || '加载中...',
      mask: options.mask !== false,
      timestamp: Date.now()
    };

    // 清除之前的定时器
    if (this.currentTimer) {
      clearTimeout(this.currentTimer);
      this.currentTimer = null;
    }

    // 添加到栈顶
    this.loadingStack.push(config);

    // 只有当这是第一个loading时才调用微信API
    if (this.loadingStack.length === 1) {
      try {
        wx.showLoading({
          title: config.title,
          mask: config.mask
        });
      } catch (e) {
        console.error('showLoading failed:', e);
        this.loadingStack.pop(); // 失败时移除
        return null;
      }
    }

    // 设置安全定时器
    this.currentTimer = setTimeout(() => {
      this.forceHideAll();
    }, this.maxTimeout);

    return loadingId;
  }

  /**
   * 隐藏Loading
   * @param {string} loadingId 要隐藏的loading ID
   */
  hide(loadingId) {
    if (!loadingId || this.loadingStack.length === 0) {
      return;
    }

    // 从栈中移除指定的loading
    const index = this.loadingStack.findIndex(item => item.id === loadingId);
    if (index !== -1) {
      this.loadingStack.splice(index, 1);
    }

    // 如果栈为空，隐藏loading
    if (this.loadingStack.length === 0) {
      this.clearTimer();
      try {
        wx.hideLoading();
      } catch (e) {
        console.error('hideLoading failed:', e);
      }
    }
  }

  /**
   * 强制隐藏所有Loading
   */
  forceHideAll() {
    this.loadingStack = [];
    this.clearTimer();
    try {
      wx.hideLoading();
    } catch (e) {
      // 静默处理
    }
  }

  /**
   * 清除定时器
   */
  clearTimer() {
    if (this.currentTimer) {
      clearTimeout(this.currentTimer);
      this.currentTimer = null;
    }
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return `loading_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取当前状态
   */
  getState() {
    return {
      isLoading: this.loadingStack.length > 0,
      count: this.loadingStack.length,
      stack: [...this.loadingStack]
    };
  }
}

// 全局单例实例
const loadingManager = new UnifiedLoadingManager();

/**
 * 简化的API接口 - 自动配对
 */
function showLoading(options = {}) {
  return loadingManager.show(options);
}

function hideLoading(loadingId) {
  loadingManager.hide(loadingId);
}

/**
 * 自动配对的异步包装器
 * @param {Function} asyncFn 异步函数
 * @param {Object} options loading配置
 */
async function withLoading(asyncFn, options = {}) {
  const loadingId = showLoading(options);
  try {
    const result = await asyncFn();
    return result;
  } finally {
    hideLoading(loadingId);
  }
}

/**
 * 页面生命周期钩子
 */
const pageHooks = {
  onShow() {
    // 页面显示时不做任何操作，避免误触发
  },
  
  onPageShow() {
    // 页面显示时不做任何操作，避免误触发
  },
  
  onHide() {
    // 页面隐藏时清理所有loading
    loadingManager.forceHideAll();
  },
  
  onPageHide() {
    // 页面隐藏时清理所有loading
    loadingManager.forceHideAll();
  },
  
  onUnload() {
    // 页面卸载时清理所有loading
    loadingManager.forceHideAll();
  }
};

// 导出API
module.exports = {
  // 主要API
  showLoading,
  hideLoading,
  withLoading,
  
  // 管理API
  forceHideAll: () => loadingManager.forceHideAll(),
  getState: () => loadingManager.getState(),
  
  // 生命周期钩子
  pageHooks,
  
  // 向后兼容
  show: showLoading,
  hide: hideLoading
};