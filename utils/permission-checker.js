/**
 * 基于角色的权限控制系统（RBAC）
 * 权限检查工具函数
 */

// 角色定义
const ROLES = {
  EMPLOYEE: 'employee',     // 普通员工
  FINANCE: 'finance',       // 财务人员
  MANAGER: 'manager',       // 经理
  ADMIN: 'admin'           // 管理员
};

// 权限级别（数字越大权限越高）
const ROLE_LEVELS = {
  [ROLES.EMPLOYEE]: 1,
  [ROLES.FINANCE]: 2,
  [ROLES.MANAGER]: 3,
  [ROLES.ADMIN]: 4
};

// 页面访问权限配置
const PAGE_PERMISSIONS = {
  // 财务概览页面
  'finance/overview': {
    [ROLES.EMPLOYEE]: false,    // 普通员工禁止访问
    [ROLES.FINANCE]: true,      // 财务人员可访问
    [ROLES.MANAGER]: true,      // 经理可访问
    [ROLES.ADMIN]: true         // 管理员可访问
  },
  
  // 财务报表页面
  'finance/reports': {
    [ROLES.EMPLOYEE]: false,
    [ROLES.FINANCE]: true,
    [ROLES.MANAGER]: true,
    [ROLES.ADMIN]: true
  },
  
  // 申请记录页面（所有人都可以访问，但看到的数据不同）
  'expense/records': {
    [ROLES.EMPLOYEE]: true,
    [ROLES.FINANCE]: true,
    [ROLES.MANAGER]: true,
    [ROLES.ADMIN]: true
  },
  
  'payment/records': {
    [ROLES.EMPLOYEE]: true,
    [ROLES.FINANCE]: true,
    [ROLES.MANAGER]: true,
    [ROLES.ADMIN]: true
  },
  
  'contract/records': {
    [ROLES.EMPLOYEE]: true,
    [ROLES.FINANCE]: false,     // 财务人员不能查看合同申请
    [ROLES.MANAGER]: true,
    [ROLES.ADMIN]: true
  },
  
  'activity/records': {
    [ROLES.EMPLOYEE]: true,
    [ROLES.FINANCE]: true,
    [ROLES.MANAGER]: true,
    [ROLES.ADMIN]: true
  },
  
  'reserve/records': {
    [ROLES.EMPLOYEE]: true,
    [ROLES.FINANCE]: true,
    [ROLES.MANAGER]: true,
    [ROLES.ADMIN]: true
  },
  
  'purchase/records': {
    [ROLES.EMPLOYEE]: true,
    [ROLES.FINANCE]: false,     // 财务人员不能查看采购申请
    [ROLES.MANAGER]: true,
    [ROLES.ADMIN]: true
  }
};

// 操作权限配置
const OPERATION_PERMISSIONS = {
  // 查看操作
  'view': {
    'own': [ROLES.EMPLOYEE, ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN],
    'all': [ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN],
    'finance_related': [ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN]
  },
  
  // 编辑操作
  'edit': {
    'own_draft': [ROLES.EMPLOYEE, ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN],
    'own_rejected': [ROLES.EMPLOYEE, ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN],
    'all': [ROLES.MANAGER, ROLES.ADMIN]
  },
  
  // 删除操作
  'delete': {
    'own_draft': [ROLES.EMPLOYEE, ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN],
    'all': [ROLES.ADMIN]
  },
  
  // 审批操作
  'approve': {
    'finance_related': [ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN],
    'all': [ROLES.MANAGER, ROLES.ADMIN]
  },
  
  // 退回操作
  'reject': {
    'finance_related': [ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN],
    'all': [ROLES.MANAGER, ROLES.ADMIN]
  }
};

// 财务相关的申请类型
const FINANCE_RELATED_TYPES = ['expense', 'payment', 'activity', 'reserve'];

/**
 * 获取当前用户信息
 */
function getCurrentUser() {
  try {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.role) {
      return userInfo;
    }
    
    // 如果没有角色信息，默认为普通员工
    return {
      ...userInfo,
      role: ROLES.EMPLOYEE
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      role: ROLES.EMPLOYEE,
      id: null
    };
  }
}

/**
 * 检查页面访问权限
 * @param {string} pageName - 页面名称，如 'finance/overview'
 * @param {string} userRole - 用户角色，可选，不传则自动获取
 * @returns {boolean} 是否有访问权限
 */
function checkPageAccess(pageName, userRole = null) {
  const role = userRole || getCurrentUser().role;
  
  if (!PAGE_PERMISSIONS[pageName]) {
    console.warn(`页面 ${pageName} 未配置权限规则，默认允许访问`);
    return true;
  }
  
  return PAGE_PERMISSIONS[pageName][role] === true;
}

/**
 * 检查操作权限
 * @param {string} operation - 操作类型，如 'view', 'edit', 'delete', 'approve', 'reject'
 * @param {string} scope - 权限范围，如 'own', 'all', 'finance_related'
 * @param {string} userRole - 用户角色，可选
 * @param {object} resource - 资源对象，包含 owner_id, type 等信息
 * @returns {boolean} 是否有操作权限
 */
function checkOperationPermission(operation, scope, userRole = null, resource = null) {
  const currentUser = getCurrentUser();
  const role = userRole || currentUser.role;
  
  if (!OPERATION_PERMISSIONS[operation] || !OPERATION_PERMISSIONS[operation][scope]) {
    console.warn(`操作 ${operation}:${scope} 未配置权限规则，默认拒绝访问`);
    return false;
  }
  
  const allowedRoles = OPERATION_PERMISSIONS[operation][scope];
  
  // 检查角色是否在允许列表中
  if (!allowedRoles.includes(role)) {
    return false;
  }
  
  // 如果是针对自己的资源，需要验证所有权
  if (scope.includes('own') && resource && resource.owner_id) {
    return resource.owner_id === currentUser.id;
  }
  
  // 如果是财务相关的权限，需要验证申请类型
  if (scope === 'finance_related' && resource && resource.type) {
    return FINANCE_RELATED_TYPES.includes(resource.type);
  }
  
  return true;
}

/**
 * 检查用户是否有指定角色或更高权限
 * @param {string} requiredRole - 需要的最低角色
 * @param {string} userRole - 用户角色，可选
 * @returns {boolean} 是否满足角色要求
 */
function hasRoleOrHigher(requiredRole, userRole = null) {
  const role = userRole || getCurrentUser().role;
  const userLevel = ROLE_LEVELS[role] || 0;
  const requiredLevel = ROLE_LEVELS[requiredRole] || 0;
  
  return userLevel >= requiredLevel;
}

/**
 * 获取用户可访问的申请类型列表
 * @param {string} userRole - 用户角色，可选
 * @returns {Array} 可访问的申请类型列表
 */
function getAccessibleApplicationTypes(userRole = null) {
  const role = userRole || getCurrentUser().role;
  const types = [];
  
  // 所有角色都可以访问的类型
  types.push('expense', 'payment', 'activity', 'reserve');
  
  // 财务人员不能访问合同和采购申请
  if (role !== ROLES.FINANCE) {
    types.push('contract', 'purchase');
  }
  
  return types;
}

/**
 * 检查是否可以查看指定用户的数据
 * @param {string} targetUserId - 目标用户ID
 * @param {string} userRole - 当前用户角色，可选
 * @returns {boolean} 是否可以查看
 */
function canViewUserData(targetUserId, userRole = null) {
  const currentUser = getCurrentUser();
  const role = userRole || currentUser.role;
  
  // 可以查看自己的数据
  if (targetUserId === currentUser.id) {
    return true;
  }
  
  // 财务人员、经理和管理员可以查看其他用户的数据
  return hasRoleOrHigher(ROLES.FINANCE, role);
}

/**
 * 获取数据查询的过滤条件
 * @param {string} applicationType - 申请类型
 * @param {string} userRole - 用户角色，可选
 * @returns {object} 查询过滤条件
 */
function getDataFilter(applicationType, userRole = null) {
  const currentUser = getCurrentUser();
  const role = userRole || currentUser.role;
  
  const filter = {};
  
  // 普通员工只能查看自己的数据
  if (role === ROLES.EMPLOYEE) {
    filter.user_id = currentUser.id;
  }
  
  // 财务人员只能查看财务相关的申请
  if (role === ROLES.FINANCE && !FINANCE_RELATED_TYPES.includes(applicationType)) {
    // 返回一个永远不会匹配的条件
    filter.user_id = -1;
  }
  
  // 经理和管理员可以查看所有数据，不添加过滤条件
  
  return filter;
}

/**
 * 权限检查失败时的处理（静默处理）
 * @param {string} reason - 失败原因
 */
function handlePermissionDenied(reason = '权限不足') {
  console.warn('权限检查失败:', reason);

  // 静默跳转到工作台首页，不显示任何提示
  const pages = getCurrentPages();
  if (pages.length > 1) {
    wx.navigateBack();
  } else {
    wx.switchTab({
      url: '/pages/workspace/workspace'
    });
  }
}

module.exports = {
  ROLES,
  ROLE_LEVELS,
  getCurrentUser,
  checkPageAccess,
  checkOperationPermission,
  hasRoleOrHigher,
  getAccessibleApplicationTypes,
  canViewUserData,
  getDataFilter,
  handlePermissionDenied
};
