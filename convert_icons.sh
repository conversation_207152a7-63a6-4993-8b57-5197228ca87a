#!/bin/bash

# 微信小程序 TabBar 图标转换脚本
# 将 SVG 图标转换为符合微信小程序规范的 PNG 图标

ICON_SIZE=64
ASSETS_DIR="/Volumes/DATA/千问/智慧养鹅全栈/assets/icons"

echo "🔄 开始转换 TabBar 图标..."

# 检查是否安装了转换工具
if command -v convert >/dev/null 2>&1; then
    echo "✅ 使用 ImageMagick 进行转换..."
    
    # 转换首页图标
    convert -background transparent -size ${ICON_SIZE}x${ICON_SIZE} "${ASSETS_DIR}/home_new.svg" "${ASSETS_DIR}/home.png"
    convert -background transparent -size ${ICON_SIZE}x${ICON_SIZE} "${ASSETS_DIR}/home_selected_new.svg" "${ASSETS_DIR}/home_selected.png"
    
    # 转换健康图标
    convert -background transparent -size ${ICON_SIZE}x${ICON_SIZE} "${ASSETS_DIR}/health_new.svg" "${ASSETS_DIR}/health.png"
    convert -background transparent -size ${ICON_SIZE}x${ICON_SIZE} "${ASSETS_DIR}/health_selected_new.svg" "${ASSETS_DIR}/health_selected.png"
    
    # 转换商城图标
    convert -background transparent -size ${ICON_SIZE}x${ICON_SIZE} "${ASSETS_DIR}/shop_new.svg" "${ASSETS_DIR}/shop.png"
    convert -background transparent -size ${ICON_SIZE}x${ICON_SIZE} "${ASSETS_DIR}/shop_selected_new.svg" "${ASSETS_DIR}/shop_selected.png"
    
    # 转换个人资料图标
    convert -background transparent -size ${ICON_SIZE}x${ICON_SIZE} "${ASSETS_DIR}/profile_new.svg" "${ASSETS_DIR}/profile.png"
    convert -background transparent -size ${ICON_SIZE}x${ICON_SIZE} "${ASSETS_DIR}/profile_selected_new.svg" "${ASSETS_DIR}/profile_selected.png"
    
    # 转换生产图标 (如果app.json中有配置)
    if [ -f "${ASSETS_DIR}/production_new.svg" ]; then
        convert -background transparent -size ${ICON_SIZE}x${ICON_SIZE} "${ASSETS_DIR}/production_new.svg" "${ASSETS_DIR}/production.png"
        convert -background transparent -size ${ICON_SIZE}x${ICON_SIZE} "${ASSETS_DIR}/production_selected_new.svg" "${ASSETS_DIR}/production_selected.png"
    fi
    
    echo "✅ 转换完成！"
    
elif command -v rsvg-convert >/dev/null 2>&1; then
    echo "✅ 使用 librsvg 进行转换..."
    
    # 使用 rsvg-convert 转换
    rsvg-convert -w ${ICON_SIZE} -h ${ICON_SIZE} "${ASSETS_DIR}/home_new.svg" > "${ASSETS_DIR}/home.png"
    rsvg-convert -w ${ICON_SIZE} -h ${ICON_SIZE} "${ASSETS_DIR}/home_selected_new.svg" > "${ASSETS_DIR}/home_selected.png"
    rsvg-convert -w ${ICON_SIZE} -h ${ICON_SIZE} "${ASSETS_DIR}/health_new.svg" > "${ASSETS_DIR}/health.png"
    rsvg-convert -w ${ICON_SIZE} -h ${ICON_SIZE} "${ASSETS_DIR}/health_selected_new.svg" > "${ASSETS_DIR}/health_selected.png"
    rsvg-convert -w ${ICON_SIZE} -h ${ICON_SIZE} "${ASSETS_DIR}/shop_new.svg" > "${ASSETS_DIR}/shop.png"
    rsvg-convert -w ${ICON_SIZE} -h ${ICON_SIZE} "${ASSETS_DIR}/shop_selected_new.svg" > "${ASSETS_DIR}/shop_selected.png"
    rsvg-convert -w ${ICON_SIZE} -h ${ICON_SIZE} "${ASSETS_DIR}/profile_new.svg" > "${ASSETS_DIR}/profile.png"
    rsvg-convert -w ${ICON_SIZE} -h ${ICON_SIZE} "${ASSETS_DIR}/profile_selected_new.svg" > "${ASSETS_DIR}/profile_selected.png"
    
    if [ -f "${ASSETS_DIR}/production_new.svg" ]; then
        rsvg-convert -w ${ICON_SIZE} -h ${ICON_SIZE} "${ASSETS_DIR}/production_new.svg" > "${ASSETS_DIR}/production.png"
        rsvg-convert -w ${ICON_SIZE} -h ${ICON_SIZE} "${ASSETS_DIR}/production_selected_new.svg" > "${ASSETS_DIR}/production_selected.png"
    fi
    
    echo "✅ 转换完成！"
    
elif command -v inkscape >/dev/null 2>&1; then
    echo "✅ 使用 Inkscape 进行转换..."
    
    # 使用 Inkscape 转换
    inkscape --export-png="${ASSETS_DIR}/home.png" --export-width=${ICON_SIZE} --export-height=${ICON_SIZE} "${ASSETS_DIR}/home_new.svg"
    inkscape --export-png="${ASSETS_DIR}/home_selected.png" --export-width=${ICON_SIZE} --export-height=${ICON_SIZE} "${ASSETS_DIR}/home_selected_new.svg"
    inkscape --export-png="${ASSETS_DIR}/health.png" --export-width=${ICON_SIZE} --export-height=${ICON_SIZE} "${ASSETS_DIR}/health_new.svg"
    inkscape --export-png="${ASSETS_DIR}/health_selected.png" --export-width=${ICON_SIZE} --export-height=${ICON_SIZE} "${ASSETS_DIR}/health_selected_new.svg"
    inkscape --export-png="${ASSETS_DIR}/shop.png" --export-width=${ICON_SIZE} --export-height=${ICON_SIZE} "${ASSETS_DIR}/shop_new.svg"
    inkscape --export-png="${ASSETS_DIR}/shop_selected.png" --export-width=${ICON_SIZE} --export-height=${ICON_SIZE} "${ASSETS_DIR}/shop_selected_new.svg"
    inkscape --export-png="${ASSETS_DIR}/profile.png" --export-width=${ICON_SIZE} --export-height=${ICON_SIZE} "${ASSETS_DIR}/profile_new.svg"
    inkscape --export-png="${ASSETS_DIR}/profile_selected.png" --export-width=${ICON_SIZE} --export-height=${ICON_SIZE} "${ASSETS_DIR}/profile_selected_new.svg"
    
    if [ -f "${ASSETS_DIR}/production_new.svg" ]; then
        inkscape --export-png="${ASSETS_DIR}/production.png" --export-width=${ICON_SIZE} --export-height=${ICON_SIZE} "${ASSETS_DIR}/production_new.svg"
        inkscape --export-png="${ASSETS_DIR}/production_selected.png" --export-width=${ICON_SIZE} --export-height=${ICON_SIZE} "${ASSETS_DIR}/production_selected_new.svg"
    fi
    
    echo "✅ 转换完成！"
    
else
    echo "❌ 未找到 SVG 转换工具！"
    echo ""
    echo "🔧 请安装以下工具之一："
    echo "   brew install imagemagick"
    echo "   brew install librsvg"
    echo "   brew install --cask inkscape"
    echo ""
    echo "🌐 或者使用在线转换工具："
    echo "   1. https://convertio.co/svg-png/"
    echo "   2. https://cloudconvert.com/svg-to-png"
    echo "   3. https://svg2png.com/"
    echo ""
    echo "📐 转换设置："
    echo "   - 宽度：64px"
    echo "   - 高度：64px"
    echo "   - 背景：透明"
    echo ""
    echo "📂 SVG 文件位置："
    echo "   ${ASSETS_DIR}/*_new.svg"
    echo "   ${ASSETS_DIR}/*_selected_new.svg"
    echo ""
    echo "🎯 转换完成后，图标将符合微信小程序 TabBar 规范"
    exit 1
fi

# 验证转换结果
echo ""
echo "📊 转换结果验证："
for icon in home health shop profile; do
    if [ -f "${ASSETS_DIR}/${icon}.png" ] && [ -f "${ASSETS_DIR}/${icon}_selected.png" ]; then
        echo "✅ ${icon} 图标转换成功"
    else
        echo "❌ ${icon} 图标转换失败"
    fi
done

echo ""
echo "🎉 TabBar 图标更新完成！"
echo "📱 现在可以在微信小程序中使用新的图标了"