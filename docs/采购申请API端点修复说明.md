# 采购申请API端点修复说明

## 🐛 问题描述

在采购申请列表页面 (`pages/workspace/purchase/list/list.js`) 中出现以下错误：

```
TypeError: Cannot read property 'LIST' of undefined
at _callee$ (list.js? [sm]:101)
```

错误发生在第101行：
```javascript
const response = await request.get(API.API_ENDPOINTS.WORKSPACE.PURCHASE.LIST, params);
```

## 🔍 问题分析

### 根本原因
代码尝试访问 `API.API_ENDPOINTS.WORKSPACE.PURCHASE.LIST`，但在API常量配置中缺少 `WORKSPACE.PURCHASE` 部分的定义。

### 代码结构分析
1. **采购申请列表页面** 使用：`API.API_ENDPOINTS.WORKSPACE.PURCHASE.LIST`
2. **统一API配置** 中只有：`API_ENDPOINTS.OA.PURCHASE.LIST`
3. **缺少映射**：WORKSPACE.PURCHASE → OA.PURCHASE

## 🔧 修复方案

### 修复内容
在 `constants/api-unified.constants.js` 文件的 `WORKSPACE` 部分添加 `PURCHASE` 模块定义：

```javascript
// 采购管理 - 工作台模块
PURCHASE: {
  LIST: buildApiUrl('/oa/purchase/requests'),
  DETAIL: (id) => buildApiUrl(`/oa/purchase/requests/${id}`),
  CREATE: buildApiUrl('/oa/purchase/requests'),
  UPDATE: (id) => buildApiUrl(`/oa/purchase/requests/${id}`),
  DELETE: (id) => buildApiUrl(`/oa/purchase/requests/${id}`),
  CANCEL: (id) => buildApiUrl(`/oa/purchase/requests/${id}/cancel`),
  STATISTICS: buildApiUrl('/oa/purchase/statistics'),
  MY_REQUESTS: buildApiUrl('/oa/purchase/my-requests'),
  PENDING_APPROVALS: buildApiUrl('/oa/purchase/pending-approvals'),
  APPROVE: (id) => buildApiUrl(`/oa/purchase/requests/${id}/approve`),
  SUPPLIERS: buildApiUrl('/oa/purchase/suppliers')
}
```

### 修复位置
- **文件**：`constants/api-unified.constants.js`
- **位置**：第127-157行，WORKSPACE 部分
- **添加内容**：完整的 PURCHASE 模块定义

## ✅ 修复验证

### 测试脚本
创建了 `scripts/test-purchase-api-fix.js` 测试脚本，验证结果：

```
✅ WORKSPACE.PURCHASE 端点存在
✅ PURCHASE.LIST: http://localhost:3001/api/v1/oa/purchase/requests
✅ PURCHASE.CREATE: http://localhost:3001/api/v1/oa/purchase/requests
✅ PURCHASE.UPDATE: http://localhost:3001/api/v1/oa/purchase/requests/test-id
✅ PURCHASE.DELETE: http://localhost:3001/api/v1/oa/purchase/requests/test-id
✅ PURCHASE.DETAIL: http://localhost:3001/api/v1/oa/purchase/requests/test-id
```

### 功能端点测试
所有函数端点都正常工作：
- `DETAIL('test-123')` → `/oa/purchase/requests/test-123`
- `UPDATE('test-123')` → `/oa/purchase/requests/test-123`
- `DELETE('test-123')` → `/oa/purchase/requests/test-123`
- `CANCEL('test-123')` → `/oa/purchase/requests/test-123/cancel`
- `APPROVE('test-123')` → `/oa/purchase/requests/test-123/approve`

## 📋 涉及的文件

### 修改的文件
1. **constants/api-unified.constants.js** - 添加 WORKSPACE.PURCHASE 定义

### 相关文件
1. **pages/workspace/purchase/list/list.js** - 使用修复后的API端点
2. **pages/workspace/purchase/apply/apply.js** - 同样使用WORKSPACE.PURCHASE端点
3. **pages/workspace/purchase/detail/detail.js** - 详情页面相关端点

## 🎯 修复效果

### 解决的问题
1. ✅ 修复了 `Cannot read property 'LIST' of undefined` 错误
2. ✅ 采购申请列表页面可以正常加载数据
3. ✅ 所有采购相关的CRUD操作端点都可用
4. ✅ 保持了代码的一致性和可维护性

### API端点映射
- `API.API_ENDPOINTS.WORKSPACE.PURCHASE.LIST` → `/api/v1/oa/purchase/requests`
- `API.API_ENDPOINTS.WORKSPACE.PURCHASE.CREATE` → `/api/v1/oa/purchase/requests`
- `API.API_ENDPOINTS.WORKSPACE.PURCHASE.DETAIL(id)` → `/api/v1/oa/purchase/requests/{id}`
- `API.API_ENDPOINTS.WORKSPACE.PURCHASE.UPDATE(id)` → `/api/v1/oa/purchase/requests/{id}`
- `API.API_ENDPOINTS.WORKSPACE.PURCHASE.DELETE(id)` → `/api/v1/oa/purchase/requests/{id}`

## 🔄 后续建议

### 代码一致性
1. **统一命名规范**：确保所有工作台模块都使用 `WORKSPACE.*` 命名空间
2. **API端点验证**：定期运行测试脚本验证API端点的完整性
3. **文档更新**：更新API文档，说明工作台模块的端点结构

### 预防措施
1. **添加单元测试**：为API常量配置添加自动化测试
2. **代码审查**：在添加新的API端点时，确保在正确的命名空间下定义
3. **类型检查**：考虑使用TypeScript或JSDoc来提供更好的类型安全

## 📝 总结

本次修复成功解决了采购申请列表页面的API端点未定义问题，通过在统一API配置中添加 `WORKSPACE.PURCHASE` 模块定义，确保了代码的一致性和功能的正常运行。修复后，所有采购相关的页面都可以正常访问对应的API端点。
