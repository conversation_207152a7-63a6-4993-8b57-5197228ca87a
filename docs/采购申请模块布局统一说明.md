# 采购申请模块布局统一说明

## 🎯 统一目标

将采购申请模块的页面布局与其他财务模块（费用报销、付款申请等）保持一致，提供统一的用户体验和视觉风格。

## 📋 统一前后对比

### 统一前的问题
1. **列表页面**：使用了不同的头部结构和筛选器布局
2. **详情页面**：页面内容几乎为空，缺少完整的详情展示
3. **申请页面**：表单布局与其他模块差异较大
4. **样式风格**：颜色主题、间距、字体大小不统一

### 统一后的改进
1. **统一的页面结构**：与费用报销等模块保持一致的布局
2. **完整的功能实现**：详情页面提供完整的信息展示
3. **一致的视觉风格**：统一的颜色主题、间距和交互效果
4. **响应式设计**：适配不同屏幕尺寸的设备

## 🔧 具体修改内容

### 1. 列表页面 (list.wxml & list.wxss)

#### 布局结构统一
```xml
<!-- 统一前 -->
<view class="page-header">
  <text class="page-title">采购申请</text>
  <button class="create-btn">新建申请</button>
</view>
<view class="filters-section">
  <!-- 筛选器 -->
</view>

<!-- 统一后 -->
<view class="header">
  <text class="title">采购申请</text>
  <text class="subtitle">管理和查看采购申请记录</text>
</view>
<view class="create-btn-container">
  <button class="create-btn">+ 新建采购申请</button>
</view>
```

#### 样式主题统一
- **主题色**：从蓝色 (#1890ff) 改为绿色 (#52c41a)，与采购申请的品牌色保持一致
- **按钮样式**：使用渐变背景和阴影效果
- **列表项**：统一的卡片样式、间距和交互效果

### 2. 详情页面 (detail.wxml & detail.wxss)

#### 从空页面到完整功能
```xml
<!-- 统一前 -->
<text>pages/workspace/purchase/detail/detail.wxml</text>

<!-- 统一后 -->
<view class="container">
  <!-- 基本信息卡片 -->
  <view class="info-card">
    <view class="card-header">
      <view class="title-section">
        <text class="purchase-title">{{purchaseDetail.title}}</text>
        <view class="status-tag">{{statusMap[purchaseDetail.status]}}</view>
      </view>
      <text class="purchase-amount">¥{{purchaseDetail.totalAmount}}</text>
    </view>
    <!-- 详细信息 -->
  </view>
  
  <!-- 采购项目明细 -->
  <view class="info-card">
    <!-- 项目列表 -->
  </view>
  
  <!-- 审批流程 -->
  <view class="info-card">
    <!-- 审批步骤 -->
  </view>
</view>
```

#### 新增功能模块
1. **基本信息展示**：申请标题、状态、金额、申请人等
2. **采购项目明细**：详细的项目列表，包含规格、数量、单价等
3. **附件管理**：支持查看和预览相关附件
4. **审批流程**：完整的审批步骤和状态展示
5. **操作按钮**：编辑和提交功能

### 3. 申请页面 (apply.wxml & apply.wxss)

#### 表单结构统一
```xml
<!-- 统一前 -->
<view class="form-item">
  <text class="form-label">采购标题 *</text>
  <input class="form-input" />
</view>

<!-- 统一后 -->
<view class="form-item">
  <view class="item-label">
    <text class="label-text">采购标题</text>
    <text class="required">*</text>
  </view>
  <input class="form-input" />
</view>
```

#### 样式规范统一
- **表单项间距**：统一32rpx的间距
- **输入框样式**：统一的边框、圆角、内边距
- **按钮设计**：固定底部的提交按钮组
- **错误提示**：统一的错误状态和提示样式

## 🎨 视觉设计统一

### 颜色主题
- **主色调**：#52c41a (绿色) - 采购申请专用色
- **成功色**：#52c41a (绿色)
- **警告色**：#fa8c16 (橙色)
- **错误色**：#ff4d4f (红色)
- **文字色**：#333 (主要文字)、#666 (次要文字)、#999 (辅助文字)

### 间距规范
- **页面边距**：32rpx
- **卡片间距**：32rpx
- **表单项间距**：32rpx
- **内容内边距**：32rpx

### 字体规范
- **标题字体**：48rpx (页面标题)、36rpx (卡片标题)、32rpx (表单标题)
- **正文字体**：28rpx (主要内容)、26rpx (次要内容)、24rpx (辅助内容)
- **字重**：700 (主标题)、600 (副标题)、500 (标签)、400 (正文)

## 📱 响应式适配

### 大屏幕设备
- **列表布局**：标准卡片布局，充分利用屏幕宽度
- **表单布局**：单列布局，表单项宽度适中
- **按钮尺寸**：标准尺寸，便于点击

### 小屏幕设备
- **自动适配**：内容自动调整以适应小屏幕
- **触摸优化**：按钮和交互区域保持足够大小
- **滚动优化**：长内容支持流畅滚动

## 🔄 保留的特色功能

### 采购项目管理
采购申请模块保留了其特有的采购项目管理功能：
- **项目列表**：支持添加多个采购项目
- **项目详情**：包含名称、规格、数量、单价等信息
- **动态计算**：自动计算小计和总金额
- **弹窗编辑**：使用模态框进行项目编辑

### 供应商信息
- **供应商管理**：支持填写供应商信息和联系方式
- **紧急程度**：支持设置采购的紧急程度
- **期望日期**：支持设置期望完成日期

## ✅ 统一效果

### 用户体验提升
1. **一致性**：所有财务模块页面具有相同的操作逻辑和视觉风格
2. **易用性**：用户在不同模块间切换时无需重新学习界面
3. **专业性**：统一的设计语言提升了应用的专业度

### 开发维护优化
1. **代码复用**：样式和组件可以在不同模块间复用
2. **维护成本**：统一的代码结构降低了维护成本
3. **扩展性**：新增模块可以快速复用现有的设计规范

## 🎯 总结

通过本次布局统一工作，采购申请模块已经与其他财务模块保持了高度一致的用户体验：

1. **✅ 页面结构统一**：头部、内容区、操作区布局一致
2. **✅ 视觉风格统一**：颜色、字体、间距、圆角等设计元素一致
3. **✅ 交互体验统一**：按钮效果、加载状态、错误提示等交互一致
4. **✅ 功能完整性**：详情页面从空白到功能完整
5. **✅ 响应式适配**：支持不同屏幕尺寸的设备

采购申请模块现在已经成为财务管理系统中一个完整、统一、专业的组成部分。
