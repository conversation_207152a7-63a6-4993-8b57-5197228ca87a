/**
 * 工作台权限配置
 * 定义不同角色的权限映射
 */

const WORKSPACE_PERMISSIONS = {
  // 基础权限
  workspace_access: {
    description: '工作台访问权限',
    roles: ['admin', 'manager', 'finance', 'employee']
  },
  
  // 申请相关权限
  application_create: {
    description: '创建申请权限',
    roles: ['admin', 'manager', 'finance', 'employee']
  },
  
  application_view: {
    description: '查看申请权限',
    roles: ['admin', 'manager', 'finance', 'employee']
  },
  
  application_edit: {
    description: '编辑申请权限',
    roles: ['admin', 'manager', 'finance', 'employee']
  },
  
  application_cancel: {
    description: '取消申请权限',
    roles: ['admin', 'manager', 'finance', 'employee']
  },
  
  // 审批相关权限
  approval_view: {
    description: '查看审批权限',
    roles: ['admin', 'manager', 'finance']
  },
  
  approval_process: {
    description: '处理审批权限',
    roles: ['admin', 'manager', 'finance']
  },
  
  approval_batch: {
    description: '批量审批权限',
    roles: ['admin', 'manager']
  },
  
  // 财务相关权限
  finance_view: {
    description: '查看财务数据权限',
    roles: ['admin', 'manager', 'finance']
  },
  
  finance_export: {
    description: '导出财务数据权限',
    roles: ['admin', 'manager', 'finance']
  },
  
  finance_report: {
    description: '查看财务报表权限',
    roles: ['admin', 'manager', 'finance']
  },
  
  finance_sync: {
    description: '同步财务数据权限',
    roles: ['admin', 'manager', 'finance']
  },
  
  // 管理相关权限
  user_manage: {
    description: '用户管理权限',
    roles: ['admin', 'manager']
  },
  
  system_config: {
    description: '系统配置权限',
    roles: ['admin']
  }
};

/**
 * 角色权限映射
 */
const ROLE_PERMISSIONS = {
  admin: [
    'workspace_access',
    'application_create', 'application_view', 'application_edit', 'application_cancel',
    'approval_view', 'approval_process', 'approval_batch',
    'finance_view', 'finance_export', 'finance_report', 'finance_sync',
    'user_manage', 'system_config'
  ],
  
  manager: [
    'workspace_access',
    'application_create', 'application_view', 'application_edit', 'application_cancel',
    'approval_view', 'approval_process', 'approval_batch',
    'finance_view', 'finance_export', 'finance_report', 'finance_sync',
    'user_manage'
  ],
  
  finance: [
    'workspace_access',
    'application_create', 'application_view', 'application_edit', 'application_cancel',
    'approval_view', 'approval_process',
    'finance_view', 'finance_export', 'finance_report', 'finance_sync'
  ],
  
  employee: [
    'workspace_access',
    'application_create', 'application_view', 'application_edit', 'application_cancel'
  ]
};

/**
 * 检查用户是否有指定权限
 */
function hasPermission(userRole, permission) {
  if (!userRole || !permission) {
    return false;
  }
  
  const rolePermissions = ROLE_PERMISSIONS[userRole];
  if (!rolePermissions) {
    return false;
  }
  
  return rolePermissions.includes(permission);
}

/**
 * 获取用户所有权限
 */
function getUserPermissions(userRole) {
  if (!userRole) {
    return [];
  }
  
  return ROLE_PERMISSIONS[userRole] || [];
}

/**
 * 获取权限描述
 */
function getPermissionDescription(permission) {
  const permissionConfig = WORKSPACE_PERMISSIONS[permission];
  return permissionConfig ? permissionConfig.description : permission;
}

/**
 * 获取角色可用的权限列表
 */
function getRolePermissions(role) {
  return ROLE_PERMISSIONS[role] || [];
}

/**
 * 验证权限配置的完整性
 */
function validatePermissionConfig() {
  const errors = [];
  
  // 检查角色权限是否都在权限定义中
  Object.keys(ROLE_PERMISSIONS).forEach(role => {
    const permissions = ROLE_PERMISSIONS[role];
    permissions.forEach(permission => {
      if (!WORKSPACE_PERMISSIONS[permission]) {
        errors.push(`角色 ${role} 包含未定义的权限: ${permission}`);
      }
    });
  });
  
  // 检查权限定义中的角色是否都存在
  Object.keys(WORKSPACE_PERMISSIONS).forEach(permission => {
    const config = WORKSPACE_PERMISSIONS[permission];
    if (config.roles) {
      config.roles.forEach(role => {
        if (!ROLE_PERMISSIONS[role]) {
          errors.push(`权限 ${permission} 引用了未定义的角色: ${role}`);
        }
      });
    }
  });
  
  return errors;
}

module.exports = {
  WORKSPACE_PERMISSIONS,
  ROLE_PERMISSIONS,
  hasPermission,
  getUserPermissions,
  getPermissionDescription,
  getRolePermissions,
  validatePermissionConfig
};
