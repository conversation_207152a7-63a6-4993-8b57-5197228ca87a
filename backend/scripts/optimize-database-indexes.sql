-- 数据库索引优化脚本
-- 执行时间: 2025/8/17 08:02:13

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- 健康记录表索引
CREATE INDEX IF NOT EXISTS idx_health_records_tenant_id ON health_records(tenant_id);
CREATE INDEX IF NOT EXISTS idx_health_records_flock_id ON health_records(flock_id);
CREATE INDEX IF NOT EXISTS idx_health_records_record_date ON health_records(record_date);

-- 生产记录表索引
CREATE INDEX IF NOT EXISTS idx_production_records_tenant_id ON production_records(tenant_id);
CREATE INDEX IF NOT EXISTS idx_production_records_record_date ON production_records(record_date);

-- 鹅群表索引
CREATE INDEX IF NOT EXISTS idx_flocks_tenant_id ON flocks(tenant_id);
CREATE INDEX IF NOT EXISTS idx_flocks_status ON flocks(status);

-- 订单表索引
CREATE INDEX IF NOT EXISTS idx_orders_tenant_id ON orders(tenant_id);
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);

-- 分析表统计信息
ANALYZE TABLE users, health_records, production_records, flocks, orders;