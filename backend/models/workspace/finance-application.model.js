const { DataTypes } = require('sequelize');
const { sequelize } = require('../../config/database');

/**
 * 财务申请模型
 * 统一管理所有类型的财务申请
 */
const FinanceApplication = sequelize.define(
  'FinanceApplication',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '申请ID'
    },
    applicationNo: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      field: 'application_no',
      comment: '申请编号'
    },
    type: {
      type: DataTypes.ENUM(
        'expense',      // 费用报销
        'payment',      // 付款申请
        'contract',     // 合同申请
        'activity',     // 活动经费
        'reserve'       // 备用金
      ),
      allowNull: false,
      comment: '申请类型'
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: '申请标题'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '申请描述'
    },
    amount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      comment: '申请金额'
    },
    currency: {
      type: DataTypes.STRING(10),
      defaultValue: 'CNY',
      comment: '货币类型'
    },
    applicantId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'applicant_id',
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '申请人ID'
    },
    departmentId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'department_id',
      comment: '部门ID'
    },
    status: {
      type: DataTypes.ENUM(
        'draft',        // 草稿
        'pending',      // 待审批
        'approved',     // 已批准
        'rejected',     // 已拒绝
        'cancelled',    // 已取消
        'processing',   // 处理中
        'completed'     // 已完成
      ),
      defaultValue: 'draft',
      comment: '申请状态'
    },
    priority: {
      type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
      defaultValue: 'normal',
      comment: '优先级'
    },
    applicationDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      field: 'application_date',
      comment: '申请日期'
    },
    expectedDate: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      field: 'expected_date',
      comment: '期望完成日期'
    },
    approvedDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'approved_date',
      comment: '批准日期'
    },
    approvedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'approved_by',
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '批准人ID'
    },
    attachments: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '附件信息'
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '扩展元数据'
    },
    remarks: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '备注信息'
    }
  },
  {
    tableName: 'finance_applications',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    comment: '财务申请表',
    indexes: [
      { fields: ['application_no'] },
      { fields: ['type'] },
      { fields: ['applicant_id'] },
      { fields: ['status'] },
      { fields: ['application_date'] },
      { fields: ['created_at'] }
    ],
    hooks: {
      beforeCreate: async (instance) => {
        // 自动生成申请编号
        const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
        const typePrefix = {
          expense: 'EXP',
          purchase: 'PUR',
          payment: 'PAY',
          contract: 'CON',
          activity: 'ACT',
          reserve: 'RES'
        };
        const prefix = typePrefix[instance.type] || 'APP';
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        instance.applicationNo = `${prefix}${date}${random}`;
        
        // 设置申请日期
        if (!instance.applicationDate) {
          instance.applicationDate = new Date();
        }
      }
    }
  }
);

module.exports = FinanceApplication;
