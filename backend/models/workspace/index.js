/**
 * 工作台模块数据模型索引
 */

const FinanceApplication = require('./finance-application.model');
const { ApprovalWorkflow, ApprovalTemplate } = require('./approval-workflow.model');
const FinanceRecord = require('./finance-record.model');

/**
 * 定义模型关联关系
 */
function defineAssociations() {
  // 财务申请与审批流程的关联
  FinanceApplication.hasMany(ApprovalWorkflow, {
    foreignKey: 'application_id',
    as: 'workflows'
  });
  ApprovalWorkflow.belongsTo(FinanceApplication, {
    foreignKey: 'application_id',
    as: 'application'
  });

  // 财务申请与财务记录的关联
  FinanceApplication.hasMany(FinanceRecord, {
    foreignKey: 'related_application_id',
    as: 'financeRecords'
  });
  FinanceRecord.belongsTo(FinanceApplication, {
    foreignKey: 'related_application_id',
    as: 'application'
  });

  // 用户关联（需要确保User模型已定义）
  // FinanceApplication.belongsTo(User, { foreignKey: 'applicant_id', as: 'applicant' });
  // ApprovalWorkflow.belongsTo(User, { foreignKey: 'approver_id', as: 'approver' });
  // FinanceRecord.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

  // 鹅群关联（需要确保Flock模型已定义）
  // FinanceRecord.belongsTo(Flock, { foreignKey: 'related_flock_id', as: 'flock' });
}

/**
 * 同步数据库表
 */
async function syncTables(options = {}) {
  try {
    await FinanceApplication.sync(options);
    await ApprovalWorkflow.sync(options);
    await ApprovalTemplate.sync(options);
    await FinanceRecord.sync(options);
    
    console.log('工作台模块数据表同步完成');
  } catch (error) {
    console.error('工作台模块数据表同步失败:', error);
    throw error;
  }
}

/**
 * 初始化默认数据
 */
async function initializeDefaultData() {
  try {
    // 创建默认审批流程模板
    const defaultTemplates = [
      {
        name: '费用报销审批流程',
        applicationType: 'expense',
        amountRange: { min: 0, max: 50000 },
        steps: [
          { step: 1, name: '直接上级审批', role: 'manager' },
          { step: 2, name: '财务审批', role: 'finance' },
          { step: 3, name: '管理员最终审批', role: 'admin' }
        ],
        description: '标准费用报销审批流程'
      },
      {
        name: '采购申请审批流程',
        applicationType: 'purchase',
        amountRange: { min: 0, max: 100000 },
        steps: [
          { step: 1, name: '部门经理审批', role: 'manager' },
          { step: 2, name: '财务审批', role: 'finance' },
          { step: 3, name: '管理员审批', role: 'admin' }
        ],
        description: '标准采购申请审批流程'
      },
      {
        name: '付款申请审批流程',
        applicationType: 'payment',
        amountRange: { min: 0, max: 200000 },
        steps: [
          { step: 1, name: '财务初审', role: 'finance' },
          { step: 2, name: '管理员审批', role: 'admin' }
        ],
        description: '付款申请审批流程'
      },
      {
        name: '合同申请审批流程',
        applicationType: 'contract',
        amountRange: { min: 0, max: 1000000 },
        steps: [
          { step: 1, name: '部门经理审批', role: 'manager' },
          { step: 2, name: '法务审批', role: 'legal' },
          { step: 3, name: '财务审批', role: 'finance' },
          { step: 4, name: '管理员最终审批', role: 'admin' }
        ],
        description: '合同申请审批流程'
      },
      {
        name: '活动经费审批流程',
        applicationType: 'activity',
        amountRange: { min: 0, max: 30000 },
        steps: [
          { step: 1, name: '部门经理审批', role: 'manager' },
          { step: 2, name: '财务审批', role: 'finance' }
        ],
        description: '活动经费申请审批流程'
      },
      {
        name: '备用金申请审批流程',
        applicationType: 'reserve',
        amountRange: { min: 0, max: 20000 },
        steps: [
          { step: 1, name: '直接上级审批', role: 'manager' },
          { step: 2, name: '财务审批', role: 'finance' }
        ],
        description: '备用金申请审批流程'
      }
    ];

    for (const template of defaultTemplates) {
      await ApprovalTemplate.findOrCreate({
        where: { 
          name: template.name,
          applicationType: template.applicationType 
        },
        defaults: template
      });
    }

    console.log('默认审批流程模板初始化完成');
  } catch (error) {
    console.error('默认数据初始化失败:', error);
    throw error;
  }
}

module.exports = {
  FinanceApplication,
  ApprovalWorkflow,
  ApprovalTemplate,
  FinanceRecord,
  defineAssociations,
  syncTables,
  initializeDefaultData
};
