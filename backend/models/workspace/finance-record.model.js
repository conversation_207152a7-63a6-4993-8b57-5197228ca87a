const { DataTypes } = require('sequelize');
const { sequelize } = require('../../config/database');

/**
 * 财务记录模型
 * 统一记录所有财务数据，包括来自健康模块的数据
 */
const FinanceRecord = sequelize.define(
  'FinanceRecord',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '记录ID'
    },
    recordNo: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      field: 'record_no',
      comment: '记录编号'
    },
    type: {
      type: DataTypes.ENUM('income', 'expense'),
      allowNull: false,
      comment: '记录类型：收入/支出'
    },
    category: {
      type: DataTypes.ENUM(
        // 支出类别
        'feed_cost',        // 饲料成本
        'medicine_cost',    // 药品费用
        'treatment_cost',   // 治疗费用
        'vaccine_cost',     // 疫苗费用
        'equipment_cost',   // 设备费用
        'labor_cost',       // 人工费用
        'utilities_cost',   // 水电费用
        'maintenance_cost', // 维护费用
        'other_expense',    // 其他支出
        
        // 收入类别
        'sales_income',     // 销售收入
        'product_income',   // 产品收入
        'service_income',   // 服务收入
        'other_income'      // 其他收入
      ),
      allowNull: false,
      comment: '财务类别'
    },
    amount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      comment: '金额'
    },
    currency: {
      type: DataTypes.STRING(10),
      defaultValue: 'CNY',
      comment: '货币类型'
    },
    recordDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      field: 'record_date',
      comment: '记录日期'
    },
    description: {
      type: DataTypes.STRING(500),
      allowNull: false,
      comment: '描述'
    },
    sourceType: {
      type: DataTypes.ENUM(
        'manual',           // 手动录入
        'health_module',    // 健康模块
        'production_module',// 生产模块
        'application',      // 申请审批
        'system'           // 系统自动
      ),
      allowNull: false,
      field: 'source_type',
      comment: '数据来源'
    },
    sourceId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'source_id',
      comment: '来源记录ID'
    },
    relatedFlockId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'related_flock_id',
      references: {
        model: 'flocks',
        key: 'id'
      },
      comment: '关联鹅群ID'
    },
    relatedApplicationId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'related_application_id',
      references: {
        model: 'finance_applications',
        key: 'id'
      },
      comment: '关联申请ID'
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '记录人ID'
    },
    departmentId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'department_id',
      comment: '部门ID'
    },
    status: {
      type: DataTypes.ENUM('pending', 'confirmed', 'cancelled'),
      defaultValue: 'confirmed',
      comment: '记录状态'
    },
    tags: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '标签'
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '扩展数据'
    },
    attachments: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '附件信息'
    },
    remarks: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '备注'
    }
  },
  {
    tableName: 'finance_records',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    comment: '财务记录表',
    indexes: [
      { fields: ['record_no'] },
      { fields: ['type'] },
      { fields: ['category'] },
      { fields: ['source_type'] },
      { fields: ['source_id'] },
      { fields: ['related_flock_id'] },
      { fields: ['user_id'] },
      { fields: ['record_date'] },
      { fields: ['created_at'] }
    ],
    hooks: {
      beforeCreate: async (instance) => {
        // 自动生成记录编号
        const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
        const typePrefix = instance.type === 'income' ? 'INC' : 'EXP';
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        instance.recordNo = `${typePrefix}${date}${random}`;
        
        // 设置记录日期
        if (!instance.recordDate) {
          instance.recordDate = new Date();
        }
      }
    }
  }
);

module.exports = FinanceRecord;
