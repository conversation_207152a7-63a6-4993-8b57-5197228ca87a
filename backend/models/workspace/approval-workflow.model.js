const { DataTypes } = require('sequelize');
const { sequelize } = require('../../config/database');

/**
 * 审批流程模型
 * 记录每个申请的审批流程和状态
 */
const ApprovalWorkflow = sequelize.define(
  'ApprovalWorkflow',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '流程ID'
    },
    applicationId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'application_id',
      references: {
        model: 'finance_applications',
        key: 'id'
      },
      comment: '申请ID'
    },
    step: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '审批步骤'
    },
    stepName: {
      type: DataTypes.STRING(100),
      allowNull: false,
      field: 'step_name',
      comment: '步骤名称'
    },
    approverId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'approver_id',
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '审批人ID'
    },
    status: {
      type: DataTypes.ENUM(
        'pending',      // 待审批
        'approved',     // 已批准
        'rejected',     // 已拒绝
        'skipped'       // 已跳过
      ),
      defaultValue: 'pending',
      comment: '审批状态'
    },
    approvalDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'approval_date',
      comment: '审批日期'
    },
    comments: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '审批意见'
    },
    attachments: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '审批附件'
    },
    nextApproverId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'next_approver_id',
      references: {
        model: 'users',
        key: 'id'
      },
      comment: '下一审批人ID'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active',
      comment: '是否为当前活跃步骤'
    }
  },
  {
    tableName: 'approval_workflows',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    comment: '审批流程表',
    indexes: [
      { fields: ['application_id'] },
      { fields: ['approver_id'] },
      { fields: ['status'] },
      { fields: ['is_active'] },
      { fields: ['created_at'] }
    ]
  }
);

/**
 * 审批流程模板模型
 * 定义不同类型申请的审批流程模板
 */
const ApprovalTemplate = sequelize.define(
  'ApprovalTemplate',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '模板ID'
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '模板名称'
    },
    applicationType: {
      type: DataTypes.ENUM(
        'expense',
        'purchase',
        'payment',
        'contract',
        'activity',
        'reserve'
      ),
      allowNull: false,
      field: 'application_type',
      comment: '申请类型'
    },
    amountRange: {
      type: DataTypes.JSON,
      allowNull: true,
      field: 'amount_range',
      comment: '金额范围 {min: 0, max: 10000}'
    },
    steps: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: '审批步骤配置'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active',
      comment: '是否启用'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '模板描述'
    }
  },
  {
    tableName: 'approval_templates',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    comment: '审批流程模板表',
    indexes: [
      { fields: ['application_type'] },
      { fields: ['is_active'] }
    ]
  }
);

module.exports = {
  ApprovalWorkflow,
  ApprovalTemplate
};
