/**
 * 基于角色的权限控制中间件（RBAC）
 * Role-Based Access Control Middleware
 */

const jwt = require('jsonwebtoken');
const { generateErrorResponse } = require('../utils/response');

// 角色定义
const ROLES = {
  EMPLOYEE: 'employee',     // 普通员工
  FINANCE: 'finance',       // 财务人员
  MANAGER: 'manager',       // 经理
  ADMIN: 'admin'           // 管理员
};

// 权限级别（数字越大权限越高）
const ROLE_LEVELS = {
  [ROLES.EMPLOYEE]: 1,
  [ROLES.FINANCE]: 2,
  [ROLES.MANAGER]: 3,
  [ROLES.ADMIN]: 4
};

// 财务相关的申请类型
const FINANCE_RELATED_TYPES = ['expense', 'payment', 'activity', 'reserve'];

/**
 * 验证JWT Token并获取用户信息
 */
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json(generateErrorResponse('访问令牌缺失'));
  }

  jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', (err, user) => {
    if (err) {
      return res.status(403).json(generateErrorResponse('访问令牌无效'));
    }
    
    // 确保用户有角色信息，默认为普通员工
    if (!user.role) {
      user.role = ROLES.EMPLOYEE;
    }
    
    req.user = user;
    next();
  });
};

/**
 * 检查用户是否有指定角色或更高权限
 * @param {string} requiredRole - 需要的最低角色
 * @returns {Function} 中间件函数
 */
const requireRole = (requiredRole) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json(generateErrorResponse('用户未认证'));
    }

    const userLevel = ROLE_LEVELS[req.user.role] || 0;
    const requiredLevel = ROLE_LEVELS[requiredRole] || 0;

    if (userLevel < requiredLevel) {
      return res.status(403).json(generateErrorResponse('权限不足'));
    }

    next();
  };
};

/**
 * 检查用户是否有指定角色列表中的任一角色
 * @param {Array} allowedRoles - 允许的角色列表
 * @returns {Function} 中间件函数
 */
const requireAnyRole = (allowedRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json(generateErrorResponse('用户未认证'));
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json(generateErrorResponse('权限不足'));
    }

    next();
  };
};

/**
 * 检查财务相关权限
 * 财务人员、经理和管理员可以访问财务相关功能
 */
const requireFinanceAccess = requireAnyRole([ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN]);

/**
 * 检查管理权限
 * 只有经理和管理员可以访问管理功能
 */
const requireManagementAccess = requireAnyRole([ROLES.MANAGER, ROLES.ADMIN]);

/**
 * 检查管理员权限
 * 只有管理员可以访问
 */
const requireAdminAccess = requireRole(ROLES.ADMIN);

/**
 * 数据访问权限过滤中间件
 * 根据用户角色过滤可访问的数据
 */
const filterDataAccess = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json(generateErrorResponse('用户未认证'));
  }

  const { role, id: userId } = req.user;
  
  // 为查询添加权限过滤条件
  req.dataFilter = {};
  
  // 普通员工只能访问自己的数据
  if (role === ROLES.EMPLOYEE) {
    req.dataFilter.user_id = userId;
  }
  
  // 财务人员可以访问所有财务相关数据，但不能访问合同和采购申请
  if (role === ROLES.FINANCE) {
    // 如果是申请相关的路由，需要检查申请类型
    const path = req.path;
    if (path.includes('/contract') || path.includes('/purchase')) {
      return res.status(403).json(generateErrorResponse('财务人员无权访问此类申请'));
    }
  }
  
  // 经理和管理员可以访问所有数据，不添加过滤条件
  
  next();
};

/**
 * 检查资源所有权
 * 验证用户是否可以操作指定资源
 */
const checkResourceOwnership = (resourceIdParam = 'id') => {
  return async (req, res, next) => {
    if (!req.user) {
      return res.status(401).json(generateErrorResponse('用户未认证'));
    }

    const { role, id: userId } = req.user;
    const resourceId = req.params[resourceIdParam];

    // 管理员可以操作所有资源
    if (role === ROLES.ADMIN) {
      return next();
    }

    // 经理可以操作大部分资源
    if (role === ROLES.MANAGER) {
      return next();
    }

    // 财务人员可以操作财务相关资源
    if (role === ROLES.FINANCE) {
      // 这里需要根据具体业务逻辑判断资源类型
      // 暂时允许财务人员操作，具体检查在业务逻辑中实现
      return next();
    }

    // 普通员工只能操作自己的资源
    // 这里需要查询数据库验证资源所有权
    // 具体实现需要在各个控制器中处理
    req.requireOwnershipCheck = true;
    next();
  };
};

/**
 * 申请类型权限检查
 * 检查用户是否可以访问指定类型的申请
 */
const checkApplicationTypeAccess = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json(generateErrorResponse('用户未认证'));
  }

  const { role } = req.user;
  const applicationType = req.params.type || req.body.type || req.query.type;

  if (!applicationType) {
    return next(); // 如果没有指定类型，继续执行
  }

  // 财务人员不能访问合同和采购申请
  if (role === ROLES.FINANCE && !FINANCE_RELATED_TYPES.includes(applicationType)) {
    return res.status(403).json(generateErrorResponse('财务人员无权访问此类申请'));
  }

  next();
};

/**
 * 操作权限检查中间件
 * @param {string} operation - 操作类型 ('view', 'create', 'update', 'delete', 'approve', 'reject')
 * @param {string} scope - 权限范围 ('own', 'all', 'finance_related')
 */
const checkOperationPermission = (operation, scope) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json(generateErrorResponse('用户未认证'));
    }

    const { role, id: userId } = req.user;

    // 定义操作权限规则
    const operationRules = {
      'view': {
        'own': [ROLES.EMPLOYEE, ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN],
        'all': [ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN],
        'finance_related': [ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN]
      },
      'create': {
        'own': [ROLES.EMPLOYEE, ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN],
        'all': [ROLES.MANAGER, ROLES.ADMIN]
      },
      'update': {
        'own': [ROLES.EMPLOYEE, ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN],
        'all': [ROLES.MANAGER, ROLES.ADMIN]
      },
      'delete': {
        'own': [ROLES.EMPLOYEE, ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN],
        'all': [ROLES.ADMIN]
      },
      'approve': {
        'finance_related': [ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN],
        'all': [ROLES.MANAGER, ROLES.ADMIN]
      },
      'reject': {
        'finance_related': [ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN],
        'all': [ROLES.MANAGER, ROLES.ADMIN]
      }
    };

    const allowedRoles = operationRules[operation]?.[scope];
    
    if (!allowedRoles || !allowedRoles.includes(role)) {
      return res.status(403).json(generateErrorResponse('权限不足'));
    }

    // 如果是针对自己的操作，需要在业务逻辑中验证所有权
    if (scope === 'own') {
      req.requireOwnershipCheck = true;
    }

    next();
  };
};

/**
 * 日志记录中间件
 * 记录权限相关的操作
 */
const logPermissionAction = (req, res, next) => {
  if (req.user) {
    console.log(`[RBAC] User ${req.user.id} (${req.user.role}) accessing ${req.method} ${req.path}`);
  }
  next();
};

module.exports = {
  ROLES,
  ROLE_LEVELS,
  FINANCE_RELATED_TYPES,
  authenticateToken,
  requireRole,
  requireAnyRole,
  requireFinanceAccess,
  requireManagementAccess,
  requireAdminAccess,
  filterDataAccess,
  checkResourceOwnership,
  checkApplicationTypeAccess,
  checkOperationPermission,
  logPermissionAction
};
