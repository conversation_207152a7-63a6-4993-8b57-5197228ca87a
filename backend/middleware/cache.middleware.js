/**
 * 简单缓存中间件
 */

const NodeCache = require('node-cache');

const cacheMiddleware = (duration = 300) => {
  return (req, res, next) => {
    if (req.method !== 'GET') {
      return next();
    }
    
    const key = req.originalUrl + (req.tenant ? req.tenant.tenantCode : '');
    const cachedResponse = cache.get(key);
    
    if (cachedResponse) {
      return res.json(cachedResponse);
    }
    
    const originalJson = res.json;
    res.json = function(data) {
      if (res.statusCode === 200) {
        cache.set(key, data, duration);
      }
      return originalJson.call(this, data);
    };
    
    next();
  };
};

module.exports = { cacheMiddleware, cache };