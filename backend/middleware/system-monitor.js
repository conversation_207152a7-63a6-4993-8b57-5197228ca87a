/**
 * 系统健康监控中间件
 * 监控API响应时间、错误率、内存使用等指标
 */

const os = require('os');
const { performanceLogger } = require('./production-logger');

class SystemMonitor {
  constructor() {
    this.startTime = Date.now();
    this.requestCount = 0;
    this.errorCount = 0;
    this.responseTimeSum = 0;
    this.responseTimeCount = 0;
  }

  // 请求监控中间件
  requestMonitor() {
    return (req, res, next) => {
      const startTime = Date.now();
      this.requestCount++;

      // 记录响应时间
      res.on('finish', () => {
        const responseTime = Date.now() - startTime;
        this.responseTimeSum += responseTime;
        this.responseTimeCount++;

        // 记录错误
        if (res.statusCode >= 400) {
          this.errorCount++;
        }

        // 记录慢查询 (>1秒)
        if (responseTime > 1000) {
          performanceLogger.warn('Slow Request', {
            method: req.method,
            url: req.originalUrl,
            responseTime: `${responseTime}ms`,
            statusCode: res.statusCode
          });
        }
      });

      next();
    };
  }

  // 获取系统状态
  getSystemStatus() {
    const now = Date.now();
    const uptime = now - this.startTime;
    const avgResponseTime = this.responseTimeCount > 0 
      ? Math.round(this.responseTimeSum / this.responseTimeCount) 
      : 0;
    const errorRate = this.requestCount > 0 
      ? Math.round((this.errorCount / this.requestCount) * 100 * 100) / 100 
      : 0;

    return {
      status: 'healthy',
      uptime: `${Math.round(uptime / 1000)}s`,
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        system: Math.round(os.totalmem() / 1024 / 1024),
        free: Math.round(os.freemem() / 1024 / 1024)
      },
      cpu: {
        usage: Math.round(process.cpuUsage().user / 1000000),
        cores: os.cpus().length
      },
      requests: {
        total: this.requestCount,
        errors: this.errorCount,
        errorRate: `${errorRate}%`,
        avgResponseTime: `${avgResponseTime}ms`
      },
      timestamp: new Date().toISOString()
    };
  }

  // 定期记录系统状态
  startPeriodicLogging(intervalMs = 60000) {
    setInterval(() => {
      const status = this.getSystemStatus();
      performanceLogger.info('System Status', status);

      // 内存使用超过800MB时警告
      if (status.memory.used > 800) {
        performanceLogger.warn('High Memory Usage', {
          memoryUsed: `${status.memory.used}MB`,
          memoryTotal: `${status.memory.total}MB`
        });
      }

      // 错误率超过5%时警告
      if (parseFloat(status.requests.errorRate) > 5) {
        performanceLogger.warn('High Error Rate', {
          errorRate: status.requests.errorRate,
          totalRequests: status.requests.total,
          totalErrors: status.requests.errors
        });
      }
    }, intervalMs);
  }
}

const systemMonitor = new SystemMonitor();

module.exports = {
  SystemMonitor,
  systemMonitor,
  
  // 导出中间件
  requestMonitor: systemMonitor.requestMonitor.bind(systemMonitor),
  
  // 健康检查端点
  healthCheckHandler: (req, res) => {
    const status = systemMonitor.getSystemStatus();
    res.json(status);
  }
};