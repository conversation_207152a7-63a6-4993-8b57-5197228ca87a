// backend/middleware/permission.middleware.js

/**
 * 权限验证中间件
 * 用于验证用户是否具有特定操作的权限
 * 
 * 整合了数据安全控制和权限验证功能
 */

const db = require('../config/database');
const { generateErrorResponse } = require('../utils/response-helper');
const { dataSecurityController, FINANCE_DATA_SENSITIVITY, DATA_SENSITIVITY_LEVELS } = require('../utils/data-security');

/**
 * 权限定义
 * 定义系统中所有可用的权限
 */
const PERMISSIONS = {
  // OA系统基础权限
  OA_ACCESS: 'oa_access', // OA系统访问权限
  OA_ADMIN: 'oa_admin', // OA系统管理权限

  // 财务管理权限 - 增强版
  FINANCE_VIEW: 'finance_view', // 财务查看权限
  FINANCE_CREATE: 'finance_create', // 财务创建权限
  FINANCE_EDIT: 'finance_edit', // 财务编辑权限
  FINANCE_DELETE: 'finance_delete', // 财务删除权限
  FINANCE_APPROVE: 'finance_approve', // 财务审批权限
  FINANCE_EXPORT: 'finance_export', // 财务导出权限
  FINANCE_REPORTS: 'finance_reports', // 财务报表权限
  FINANCE_ADMIN: 'finance_admin', // 财务管理权限
  FINANCE_AUDIT: 'finance_audit', // 财务审计权限

  // 健康财务整合权限
  HEALTH_FINANCE_VIEW: 'health_finance_view', // 健康财务查看权限
  HEALTH_FINANCE_SYNC: 'health_finance_sync', // 健康财务同步权限
  HEALTH_FINANCE_MANAGE: 'health_finance_manage', // 健康财务管理权限

  // 采购管理权限
  PURCHASE_VIEW: 'purchase_view', // 采购查看权限
  PURCHASE_CREATE: 'purchase_create', // 采购申请权限
  PURCHASE_EDIT: 'purchase_edit', // 采购编辑权限
  PURCHASE_DELETE: 'purchase_delete', // 采购删除权限
  PURCHASE_APPROVE: 'purchase_approve', // 采购审批权限
  PURCHASE_CANCEL: 'purchase_cancel', // 采购取消权限

  // 报销管理权限
  REIMBURSEMENT_VIEW: 'reimbursement_view', // 报销查看权限
  REIMBURSEMENT_CREATE: 'reimbursement_create', // 报销创建权限
  REIMBURSEMENT_EDIT: 'reimbursement_edit', // 报销编辑权限
  REIMBURSEMENT_DELETE: 'reimbursement_delete', // 报销删除权限
  REIMBURSEMENT_APPROVE: 'reimbursement_approve', // 报销审批权限

  // 审批管理权限
  APPROVAL_VIEW: 'approval_view', // 审批查看权限
  APPROVAL_PROCESS: 'approval_process', // 审批处理权限
  APPROVAL_DELEGATE: 'approval_delegate', // 审批委托权限
  APPROVAL_WORKFLOW_MANAGE: 'approval_workflow_manage', // 审批流程管理权限

  // 任务管理权限
  TASK_VIEW: 'task_view', // 任务查看权限
  TASK_CREATE: 'task_create', // 任务创建权限
  TASK_EDIT: 'task_edit', // 任务编辑权限
  TASK_DELETE: 'task_delete', // 任务删除权限
  TASK_ASSIGN: 'task_assign', // 任务分配权限

  // 数据安全权限
  DATA_VIEW_CONFIDENTIAL: 'data_view_confidential', // 机密数据查看权限
  DATA_VIEW_SECRET: 'data_view_secret', // 绝密数据查看权限
  DATA_AUDIT_LOG: 'data_audit_log', // 数据审计日志权限

  // 系统管理权限
  SYSTEM_CONFIG: 'system_config', // 系统配置权限
  USER_MANAGE: 'user_manage', // 用户管理权限
  ROLE_MANAGE: 'role_manage' // 角色管理权限
};

/**
 * 角色权限映射 - 增强版
 * 定义不同角色的默认权限
 */
const ROLE_PERMISSIONS = {
  // 超级管理员 - 拥有所有权限
  super_admin: Object.values(PERMISSIONS),

  // 系统管理员 - 拥有系统管理和部分业务权限
  admin: [
    PERMISSIONS.OA_ACCESS,
    PERMISSIONS.OA_ADMIN,
    PERMISSIONS.FINANCE_VIEW,
    PERMISSIONS.FINANCE_REPORTS,
    PERMISSIONS.FINANCE_EXPORT,
    PERMISSIONS.FINANCE_AUDIT,
    PERMISSIONS.HEALTH_FINANCE_VIEW,
    PERMISSIONS.PURCHASE_VIEW,
    PERMISSIONS.REIMBURSEMENT_VIEW,
    PERMISSIONS.APPROVAL_VIEW,
    PERMISSIONS.APPROVAL_WORKFLOW_MANAGE,
    PERMISSIONS.TASK_VIEW,
    PERMISSIONS.TASK_CREATE,
    PERMISSIONS.TASK_ASSIGN,
    PERMISSIONS.DATA_VIEW_CONFIDENTIAL,
    PERMISSIONS.DATA_AUDIT_LOG,
    PERMISSIONS.SYSTEM_CONFIG,
    PERMISSIONS.USER_MANAGE,
    PERMISSIONS.ROLE_MANAGE
  ],

  // 财务经理 - 拥有财务相关的全部权限
  finance_manager: [
    PERMISSIONS.OA_ACCESS,
    PERMISSIONS.FINANCE_VIEW,
    PERMISSIONS.FINANCE_CREATE,
    PERMISSIONS.FINANCE_EDIT,
    PERMISSIONS.FINANCE_DELETE,
    PERMISSIONS.FINANCE_APPROVE,
    PERMISSIONS.FINANCE_EXPORT,
    PERMISSIONS.FINANCE_REPORTS,
    PERMISSIONS.FINANCE_ADMIN,
    PERMISSIONS.HEALTH_FINANCE_VIEW,
    PERMISSIONS.HEALTH_FINANCE_SYNC,
    PERMISSIONS.HEALTH_FINANCE_MANAGE,
    PERMISSIONS.PURCHASE_VIEW,
    PERMISSIONS.PURCHASE_APPROVE,
    PERMISSIONS.REIMBURSEMENT_VIEW,
    PERMISSIONS.REIMBURSEMENT_APPROVE,
    PERMISSIONS.APPROVAL_VIEW,
    PERMISSIONS.APPROVAL_PROCESS,
    PERMISSIONS.TASK_VIEW,
    PERMISSIONS.DATA_VIEW_CONFIDENTIAL
  ],

  // 财务专员 - 拥有财务相关的操作权限
  finance_staff: [
    PERMISSIONS.OA_ACCESS,
    PERMISSIONS.FINANCE_VIEW,
    PERMISSIONS.FINANCE_CREATE,
    PERMISSIONS.FINANCE_EDIT,
    PERMISSIONS.FINANCE_EXPORT,
    PERMISSIONS.FINANCE_REPORTS,
    PERMISSIONS.HEALTH_FINANCE_VIEW,
    PERMISSIONS.HEALTH_FINANCE_SYNC,
    PERMISSIONS.PURCHASE_VIEW,
    PERMISSIONS.REIMBURSEMENT_VIEW,
    PERMISSIONS.APPROVAL_VIEW,
    PERMISSIONS.TASK_VIEW
  ],

  // 部门经理 - 拥有部门相关的审批权限
  department_manager: [
    PERMISSIONS.OA_ACCESS,
    PERMISSIONS.FINANCE_VIEW,
    PERMISSIONS.HEALTH_FINANCE_VIEW,
    PERMISSIONS.PURCHASE_VIEW,
    PERMISSIONS.PURCHASE_CREATE,
    PERMISSIONS.PURCHASE_APPROVE,
    PERMISSIONS.REIMBURSEMENT_VIEW,
    PERMISSIONS.REIMBURSEMENT_APPROVE,
    PERMISSIONS.APPROVAL_VIEW,
    PERMISSIONS.APPROVAL_PROCESS,
    PERMISSIONS.TASK_VIEW,
    PERMISSIONS.TASK_CREATE,
    PERMISSIONS.TASK_EDIT,
    PERMISSIONS.TASK_ASSIGN
  ],

  // 采购专员 - 拥有采购相关的操作权限
  purchase_staff: [
    PERMISSIONS.OA_ACCESS,
    PERMISSIONS.FINANCE_VIEW,
    PERMISSIONS.PURCHASE_VIEW,
    PERMISSIONS.PURCHASE_CREATE,
    PERMISSIONS.PURCHASE_EDIT,
    PERMISSIONS.REIMBURSEMENT_VIEW,
    PERMISSIONS.REIMBURSEMENT_CREATE,
    PERMISSIONS.APPROVAL_VIEW,
    PERMISSIONS.TASK_VIEW,
    PERMISSIONS.TASK_CREATE
  ],

  // 普通员工 - 拥有基本的查看和创建权限
  employee: [
    PERMISSIONS.OA_ACCESS,
    PERMISSIONS.FINANCE_VIEW,
    PERMISSIONS.PURCHASE_VIEW,
    PERMISSIONS.PURCHASE_CREATE,
    PERMISSIONS.PURCHASE_EDIT,
    PERMISSIONS.PURCHASE_CANCEL,
    PERMISSIONS.REIMBURSEMENT_VIEW,
    PERMISSIONS.REIMBURSEMENT_CREATE,
    PERMISSIONS.REIMBURSEMENT_EDIT,
    PERMISSIONS.APPROVAL_VIEW,
    PERMISSIONS.TASK_VIEW,
    PERMISSIONS.TASK_CREATE,
    PERMISSIONS.TASK_EDIT
  ],

  // 审计员 - 拥有数据查看和审计权限
  auditor: [
    PERMISSIONS.OA_ACCESS,
    PERMISSIONS.FINANCE_VIEW,
    PERMISSIONS.FINANCE_AUDIT,
    PERMISSIONS.FINANCE_REPORTS,
    PERMISSIONS.HEALTH_FINANCE_VIEW,
    PERMISSIONS.PURCHASE_VIEW,
    PERMISSIONS.REIMBURSEMENT_VIEW,
    PERMISSIONS.APPROVAL_VIEW,
    PERMISSIONS.DATA_VIEW_CONFIDENTIAL,
    PERMISSIONS.DATA_VIEW_SECRET,
    PERMISSIONS.DATA_AUDIT_LOG
  ]
};

/**
 * 数据安全检查中间件
 * 结合数据安全控制器进行权限验证
 * @param {string} dataType - 数据类型
 * @param {string} operation - 操作类型
 * @returns {Function} - Express中间件函数
 */
function validateDataSecurity(dataType, operation) {
  return async (req, res, next) => {
    try {
      if (!req.user || !req.user.id) {
        return res.status(401).json(generateErrorResponse('用户未认证'));
      }

      // 初始化数据安全控制器
      await dataSecurityController.init();

      // 检查数据访问权限
      const hasAccess = await dataSecurityController.checkDataAccess(
        dataType,
        operation,
        {
          ownerId: req.body?.userId || req.query?.userId,
          departmentId: req.body?.departmentId || req.query?.departmentId,
          resourceId: req.params?.id,
          ip: req.ip,
          userAgent: req.get('User-Agent')
        }
      );

      if (!hasAccess) {
        // 记录访问失败
        await dataSecurityController.logDataAccess(
          operation,
          dataType,
          { id: req.params?.id, ip: req.ip, userAgent: req.get('User-Agent') },
          false
        );

        return res.status(403).json(
          generateErrorResponse('数据访问权限不足', {
            dataType,
            operation,
            userId: req.user.id
          })
        );
      }

      // 记录访问成功
      await dataSecurityController.logDataAccess(
        operation,
        dataType,
        { id: req.params?.id, ip: req.ip, userAgent: req.get('User-Agent') },
        true
      );

      next();
    } catch (error) {
      console.error('数据安全验证失败:', error);
      res.status(500).json(generateErrorResponse('数据安全验证失败'));
    }
  };
}

/**
 * 数据过滤中间件
 * 根据用户权限过滤返回的数据
 * @param {string} dataType - 数据类型
 * @returns {Function} - Express中间件函数
 */
function applyDataFilter(dataType) {
  return async (req, res, next) => {
    try {
      // 保存原始的 res.json 方法
      const originalJson = res.json;

      // 重写 res.json 方法以拦截响应数据
      res.json = async function(data) {
        if (data && data.success && data.data) {
          // 如果数据是数组，进行批量过滤
          if (Array.isArray(data.data)) {
            data.data = await dataSecurityController.filterDataByPermission(
              data.data,
              dataType,
              {
                userId: req.user?.id,
                departmentId: req.user?.departmentId
              }
            );
          } else {
            // 如果是单个对象，进行脱敏处理
            data.data = dataSecurityController.sanitizeData(data.data, dataType);
          }
        }

        // 调用原始的 json 方法
        originalJson.call(this, data);
      };

      next();
    } catch (error) {
      console.error('数据过滤中间件失败:', error);
      next();
    }
  };
}

/**
 * 数据完整性验证中间件
 * @param {Array} requiredFields - 必需字段列表
 * @returns {Function} - Express中间件函数
 */
function validateDataIntegrity(requiredFields = []) {
  return async (req, res, next) => {
    try {
      await dataSecurityController.init();

      const validation = dataSecurityController.validateDataIntegrity(
        req.body,
        requiredFields
      );

      if (!validation.isValid) {
        return res.status(400).json(
          generateErrorResponse('数据验证失败', {
            errors: validation.errors,
            warnings: validation.warnings
          })
        );
      }

      // 如果有警告，记录到日志
      if (validation.warnings.length > 0) {
        console.warn('数据完整性警告:', validation.warnings);
      }

      next();
    } catch (error) {
      console.error('数据完整性验证失败:', error);
      res.status(500).json(generateErrorResponse('数据完整性验证失败'));
    }
  };
}
async function checkUserPermission(userId, permission) {
  try {
    // 查询用户权限（通过角色）
    const [permissions] = await db.query(
      `
      SELECT DISTINCT p.code
      FROM oa_permissions p
      INNER JOIN oa_role_permissions rp ON p.id = rp.permission_id
      INNER JOIN oa_roles r ON rp.role_id = r.id
      INNER JOIN oa_user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = ?
        AND ur.is_active = 1
        AND r.is_active = 1
        AND p.is_active = 1
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    `,
      [userId]
    );

    const userPermissions = permissions.map((p) => p.code);
    return userPermissions.includes(permission);
  } catch (error) {
    console.error('检查用户权限失败:', error);
    return false;
  }
}
  try {
    // 查询用户权限（通过角色）
    const [permissions] = await db.query(
      `
      SELECT DISTINCT p.code
      FROM oa_permissions p
      INNER JOIN oa_role_permissions rp ON p.id = rp.permission_id
      INNER JOIN oa_roles r ON rp.role_id = r.id
      INNER JOIN oa_user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = ?
        AND ur.is_active = 1
        AND r.is_active = 1
        AND p.is_active = 1
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    `,
      [userId]
    );

    const userPermissions = permissions.map((p) => p.code);
    return userPermissions.includes(permission);
  } catch (error) {
    console.error('检查用户权限失败:', error);
    return false;
  }
}

/**
 * 获取用户完整权限信息
 * @param {number} userId - 用户ID
 * @returns {Promise<Object>} - 用户权限信息
 */
async function getUserPermissionInfo(userId) {
  try {
    // 查询用户权限和数据权限范围
    const [userPermissions] = await db.query(
      `
      SELECT DISTINCT
        p.code,
        r.data_scope,
        r.level,
        ur.user_id,
        u.department_id
      FROM oa_permissions p
      INNER JOIN oa_role_permissions rp ON p.id = rp.permission_id
      INNER JOIN oa_roles r ON rp.role_id = r.id
      INNER JOIN oa_user_roles ur ON r.id = ur.role_id
      INNER JOIN users u ON ur.user_id = u.id
      WHERE ur.user_id = ?
        AND ur.is_active = 1
        AND r.is_active = 1
        AND p.is_active = 1
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    `,
      [userId]
    );

    if (userPermissions.length === 0) {
      return {
        permissions: [],
        dataScope: 'self',
        departmentId: null,
        level: 999
      };
    }

    const permissionCodes = userPermissions.map((p) => p.code);
    const dataScopes = userPermissions.map((p) => p.data_scope);

    // 获取最高级别的数据权限
    let userDataScope = 'self';
    if (dataScopes.includes('all')) {
      userDataScope = 'all';
    } else if (dataScopes.includes('department')) {
      userDataScope = 'department';
    }

    return {
      permissions: permissionCodes,
      dataScope: userDataScope,
      departmentId: userPermissions[0].department_id,
      level: Math.min(...userPermissions.map((p) => p.level))
    };
  } catch (error) {
    console.error('获取用户权限信息失败:', error);
    return {
      permissions: [],
      dataScope: 'self',
      departmentId: null,
      level: 999
    };
  }
}

/**
 * 权限验证中间件
 * @param {string} requiredPermission - 所需权限代码
 * @returns {Function} - Express中间件函数
 */
function validatePermission(requiredPermission) {
  return async (req, res, next) => {
    try {
      // 检查用户是否已认证
      if (!req.user || !req.user.id) {
        return res.status(401).json(generateErrorResponse('用户未认证'));
      }

      // 获取用户权限信息
      const userPermissionInfo = await getUserPermissionInfo(req.user.id);

      // 检查用户权限
      if (!userPermissionInfo.permissions.includes(requiredPermission)) {
        return res.status(403).json(
          generateErrorResponse('权限不足', {
            required_permission: requiredPermission,
            user_id: req.user.id
          })
        );
      }

      // 将权限信息附加到请求对象
      req.userPermissions = userPermissionInfo;

      next();
    } catch (error) {
      console.error('权限验证失败:', error);
      res.status(500).json(generateErrorResponse('权限验证失败'));
    }
  };
}

/**
 * 批量权限验证中间件
 * 用户需要拥有任意一个权限即可通过
 * @param {string[]} permissions - 权限代码列表
 * @returns {Function} - Express中间件函数
 */
function validateAnyPermission(permissions) {
  return async (req, res, next) => {
    try {
      if (!req.user || !req.user.id) {
        return res.status(401).json(generateErrorResponse('用户未认证'));
      }

      // 获取用户权限信息
      const userPermissionInfo = await getUserPermissionInfo(req.user.id);

      // 检查是否拥有任意一个权限
      const hasAnyPermission = permissions.some((permission) =>
        userPermissionInfo.permissions.includes(permission)
      );

      if (!hasAnyPermission) {
        return res.status(403).json(
          generateErrorResponse('权限不足', {
            required_permissions: permissions,
            user_id: req.user.id
          })
        );
      }

      // 将权限信息附加到请求对象
      req.userPermissions = userPermissionInfo;

      next();
    } catch (error) {
      console.error('批量权限验证失败:', error);
      res.status(500).json(generateErrorResponse('权限验证失败'));
    }
  };
}

/**
 * 数据权限过滤中间件
 * 根据用户的数据权限范围过滤查询条件
 */
const applyDataScope = (
  tableName = '',
  userIdField = 'user_id',
  departmentIdField = 'department_id'
) => {
  return (req, res, next) => {
    const userPermissions = req.userPermissions;

    if (!userPermissions) {
      return res.status(403).json(generateErrorResponse('权限信息缺失'));
    }

    const { dataScope, departmentId } = userPermissions;
    const userId = req.user.id;

    // 构建数据权限查询条件
    let dataScopeCondition = '';
    let dataScopeParams = [];

    switch (dataScope) {
    case 'all':
      // 全部数据权限，不添加额外条件
      break;
    case 'department':
      // 部门数据权限
      if (tableName && departmentIdField) {
        dataScopeCondition = `${tableName}.${departmentIdField} = ?`;
        dataScopeParams = [departmentId];
      }
      break;
    case 'self':
    default:
      // 个人数据权限
      if (tableName && userIdField) {
        dataScopeCondition = `${tableName}.${userIdField} = ?`;
        dataScopeParams = [userId];
      }
      break;
    }

    // 将数据权限条件附加到请求对象
    req.dataScopeCondition = dataScopeCondition;
    req.dataScopeParams = dataScopeParams;

    next();
  };
};

/**
 * 资源所有权验证中间件
 * 验证用户是否有权限访问特定资源
 * @param {string} resourceType - 资源类型
 * @param {string} resourceIdParam - 资源ID参数名
 * @returns {Function} - Express中间件函数
 */
function validateResourceOwnership(resourceType, resourceIdParam = 'id') {
  return async (req, res, next) => {
    try {
      if (!req.user || !req.user.id) {
        return res.status(401).json(generateErrorResponse('用户未认证'));
      }

      const resourceId = req.params[resourceIdParam];
      if (!resourceId) {
        return res.status(400).json(generateErrorResponse('缺少资源ID'));
      }

      // 获取用户权限信息
      const userPermissionInfo = await getUserPermissionInfo(req.user.id);

      // 如果有全部数据权限，直接通过
      if (userPermissionInfo.dataScope === 'all') {
        req.userPermissions = userPermissionInfo;
        return next();
      }

      // 根据资源类型查询所有权
      let tableName = '';
      let ownerField = 'user_id';
      let departmentField = 'department_id';

      switch (resourceType) {
      case 'purchase':
        tableName = 'oa_purchase_requests';
        break;
      case 'reimbursement':
        tableName = 'oa_reimbursements';
        break;
      case 'leave':
        tableName = 'oa_leaves';
        break;
      default:
        return res.status(400).json(generateErrorResponse('无效的资源类型'));
      }

      const [rows] = await db.query(
        `SELECT ${ownerField}, ${departmentField} FROM ${tableName} WHERE id = ?`,
        [resourceId]
      );

      if (!rows || rows.length === 0) {
        return res.status(404).json(generateErrorResponse('资源不存在'));
      }

      const resource = rows[0];
      const userId = req.user.id;

      // 检查访问权限
      let hasAccess = false;

      if (userPermissionInfo.dataScope === 'self') {
        // 个人数据权限：只能访问自己的数据
        hasAccess = resource[ownerField] === userId;
      } else if (userPermissionInfo.dataScope === 'department') {
        // 部门数据权限：可以访问同部门的数据
        hasAccess =
          resource[ownerField] === userId ||
          resource[departmentField] === userPermissionInfo.departmentId;
      }

      if (!hasAccess) {
        return res.status(403).json(generateErrorResponse('无权限访问此资源'));
      }

      req.userPermissions = userPermissionInfo;
      next();
    } catch (error) {
      console.error('资源所有权验证失败:', error);
      res.status(500).json(generateErrorResponse('权限验证失败'));
    }
  };
}

/**
 * 检查用户是否有特定角色
 */
const hasRole = (roleCode) => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json(generateErrorResponse('用户未登录'));
      }

      // 查询用户是否有指定角色
      const [roles] = await db.query(
        `
        SELECT r.code
        FROM oa_roles r
        INNER JOIN oa_user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
          AND ur.is_active = 1
          AND r.is_active = 1
          AND r.code = ?
          AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
      `,
        [userId, roleCode]
      );

      if (roles.length === 0) {
        return res.status(403).json(generateErrorResponse('角色权限不足'));
      }

      next();
    } catch (error) {
      console.error('角色验证失败:', error);
      res.status(500).json(generateErrorResponse('角色验证失败'));
    }
  };
};

/**
 * 检查用户级别
 */
const checkUserLevel = (requiredLevel) => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json(generateErrorResponse('用户未登录'));
      }

      // 查询用户最高级别
      const [roles] = await db.query(
        `
        SELECT MIN(r.level) as userLevel
        FROM oa_roles r
        INNER JOIN oa_user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = ?
          AND ur.is_active = 1
          AND r.is_active = 1
          AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
      `,
        [userId]
      );

      const userLevel = roles[0]?.userLevel || 999;

      // 级别数字越小权限越高
      if (userLevel > requiredLevel) {
        return res.status(403).json(generateErrorResponse('用户级别不足'));
      }

      next();
    } catch (error) {
      console.error('用户级别验证失败:', error);
      res.status(500).json(generateErrorResponse('用户级别验证失败'));
    }
  };
};

module.exports = {
  PERMISSIONS,
  ROLE_PERMISSIONS,
  validatePermission,
  validateAnyPermission,
  validateResourceOwnership,
  applyDataScope,
  hasRole,
  checkUserLevel,
  checkUserPermission,
  getUserPermissionInfo,
  // 新增的数据安全相关中间件
  validateDataSecurity,
  applyDataFilter,
  validateDataIntegrity
};
