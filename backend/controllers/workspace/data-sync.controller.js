const { Op } = require('sequelize');
const { FinanceRecord } = require('../../models/workspace');
const HealthRecord = require('../../models/health-record.model');

/**
 * 数据同步控制器
 * 负责将健康模块的数据同步到工作台财务系统
 */

/**
 * 同步健康模块数据
 */
exports.syncHealthData = async (req, res) => {
  try {
    const { startDate, endDate, forceSync = false } = req.body;
    
    // 构建查询条件
    const whereCondition = {};
    if (startDate && endDate) {
      whereCondition.checkDate = {
        [Op.between]: [startDate, endDate]
      };
    } else {
      // 默认同步最近30天的数据
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      whereCondition.checkDate = {
        [Op.gte]: thirtyDaysAgo
      };
    }
    
    // 查询健康记录
    const healthRecords = await HealthRecord.findAll({
      where: whereCondition,
      order: [['checkDate', 'DESC']]
    });
    
    let syncedCount = 0;
    let skippedCount = 0;
    const errors = [];
    
    for (const record of healthRecords) {
      try {
        // 检查是否已经同步过
        if (!forceSync) {
          const existingRecord = await FinanceRecord.findOne({
            where: {
              sourceType: 'health_module',
              sourceId: record.id
            }
          });
          
          if (existingRecord) {
            skippedCount++;
            continue;
          }
        }
        
        // 计算费用
        const amount = calculateHealthCost(record);
        
        if (amount > 0) {
          // 创建财务记录
          await FinanceRecord.create({
            type: 'expense',
            category: getCostCategory(record),
            amount: amount,
            recordDate: record.checkDate,
            description: generateDescription(record),
            sourceType: 'health_module',
            sourceId: record.id,
            relatedFlockId: record.flockId,
            userId: record.userId || 1, // 默认用户ID
            status: 'confirmed',
            metadata: {
              healthRecordId: record.id,
              checkType: record.checkType,
              healthStatus: record.healthStatus,
              affectedCount: record.affectedCount
            }
          });
          
          syncedCount++;
        } else {
          skippedCount++;
        }
      } catch (error) {
        console.error(`同步健康记录 ${record.id} 失败:`, error);
        errors.push({
          recordId: record.id,
          error: error.message
        });
      }
    }
    
    res.json({
      success: true,
      message: '健康数据同步完成',
      data: {
        totalRecords: healthRecords.length,
        syncedCount,
        skippedCount,
        errorCount: errors.length,
        errors: errors.slice(0, 10) // 只返回前10个错误
      }
    });
  } catch (error) {
    console.error('同步健康数据失败:', error);
    res.status(500).json({
      success: false,
      message: '同步健康数据失败',
      error: error.message
    });
  }
};

/**
 * 获取同步统计信息
 */
exports.getSyncStatistics = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    // 构建查询条件
    const whereCondition = {
      sourceType: 'health_module'
    };
    
    if (startDate && endDate) {
      whereCondition.recordDate = {
        [Op.between]: [startDate, endDate]
      };
    }
    
    // 统计数据
    const [totalRecords, totalAmount, categoryStats] = await Promise.all([
      // 总记录数
      FinanceRecord.count({
        where: whereCondition
      }),
      
      // 总金额
      FinanceRecord.sum('amount', {
        where: whereCondition
      }),
      
      // 分类统计
      FinanceRecord.findAll({
        attributes: [
          'category',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
          [sequelize.fn('SUM', sequelize.col('amount')), 'total']
        ],
        where: whereCondition,
        group: ['category'],
        order: [[sequelize.fn('SUM', sequelize.col('amount')), 'DESC']]
      })
    ]);
    
    res.json({
      success: true,
      data: {
        totalRecords: totalRecords || 0,
        totalAmount: Math.round(totalAmount || 0),
        categoryStats: categoryStats || [],
        lastSyncTime: await getLastSyncTime()
      }
    });
  } catch (error) {
    console.error('获取同步统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取同步统计失败',
      error: error.message
    });
  }
};

/**
 * 计算健康记录的费用
 */
function calculateHealthCost(record) {
  let cost = 0;
  
  // 根据检查类型计算基础费用
  const baseCosts = {
    routine: 50,      // 常规检查
    vaccination: 100, // 疫苗接种
    treatment: 200,   // 治疗
    emergency: 500    // 紧急处理
  };
  
  cost += baseCosts[record.checkType] || 50;
  
  // 根据受影响数量调整费用
  if (record.affectedCount > 0) {
    cost *= Math.min(record.affectedCount, 10); // 最多按10只计算
  }
  
  // 根据健康状态调整费用
  const statusMultipliers = {
    healthy: 1,
    warning: 1.5,
    sick: 2,
    critical: 3
  };
  
  cost *= statusMultipliers[record.healthStatus] || 1;
  
  // 如果有用药，增加药品费用
  if (record.medication) {
    cost += 100; // 基础药品费用
  }
  
  return Math.round(cost);
}

/**
 * 获取费用类别
 */
function getCostCategory(record) {
  switch (record.checkType) {
    case 'vaccination':
      return 'vaccine_cost';
    case 'treatment':
      return 'treatment_cost';
    case 'emergency':
      return 'treatment_cost';
    default:
      return 'medicine_cost';
  }
}

/**
 * 生成描述
 */
function generateDescription(record) {
  const typeNames = {
    routine: '常规检查',
    vaccination: '疫苗接种',
    treatment: '治疗',
    emergency: '紧急处理'
  };
  
  let description = `${typeNames[record.checkType] || '健康检查'}`;
  
  if (record.affectedCount > 0) {
    description += ` - 涉及${record.affectedCount}只`;
  }
  
  if (record.symptoms) {
    description += ` - ${record.symptoms.substring(0, 50)}`;
  }
  
  return description;
}

/**
 * 获取最后同步时间
 */
async function getLastSyncTime() {
  try {
    const lastRecord = await FinanceRecord.findOne({
      where: { sourceType: 'health_module' },
      order: [['created_at', 'DESC']],
      attributes: ['created_at']
    });
    
    return lastRecord ? lastRecord.created_at : null;
  } catch (error) {
    return null;
  }
}
