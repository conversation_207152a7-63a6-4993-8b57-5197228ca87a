const { Op } = require('sequelize');
const { 
  FinanceApplication, 
  ApprovalWorkflow, 
  FinanceRecord 
} = require('../../models/workspace');

/**
 * 获取工作台首页数据
 */
exports.getDashboard = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    // 并行查询各种统计数据
    const [
      myApplications,
      pendingApprovals,
      monthlyExpense,
      monthlyIncome,
      recentActivities
    ] = await Promise.all([
      // 我的申请统计
      FinanceApplication.count({
        where: { applicantId: userId }
      }),

      // 待我审批的申请
      ApprovalWorkflow.count({
        where: {
          approverId: userId,
          status: 'pending',
          isActive: true
        }
      }),

      // 本月支出
      FinanceRecord.sum('amount', {
        where: {
          type: 'expense',
          recordDate: {
            [Op.gte]: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          },
          status: 'confirmed'
        }
      }),

      // 本月收入
      FinanceRecord.sum('amount', {
        where: {
          type: 'income',
          recordDate: {
            [Op.gte]: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          },
          status: 'confirmed'
        }
      }),

      // 最近活动
      getRecentActivities(userId, userRole)
    ]);

    // 根据用户角色返回不同的统计数据
    const dashboardData = {
      statistics: {
        myApplications: myApplications || 0,
        pendingApprovals: pendingApprovals || 0,
        monthlyExpense: Math.round(monthlyExpense || 0),
        monthlyIncome: Math.round(monthlyIncome || 0),
        netIncome: Math.round((monthlyIncome || 0) - (monthlyExpense || 0))
      },
      recentActivities: recentActivities || [],
      quickActions: getQuickActions(userRole),
      permissions: getUserPermissions(userRole)
    };

    res.json({
      success: true,
      data: dashboardData
    });
  } catch (error) {
    console.error('获取工作台数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取工作台数据失败',
      error: error.message
    });
  }
};

/**
 * 获取财务统计数据
 */
exports.getFinanceStatistics = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const { startDate, endDate, type } = req.query;

    // 构建查询条件
    const whereCondition = {
      status: 'confirmed'
    };

    // 根据用户角色限制数据访问
    if (!['admin', 'manager', 'finance'].includes(userRole)) {
      whereCondition.userId = userId;
    }

    // 日期范围过滤
    if (startDate && endDate) {
      whereCondition.recordDate = {
        [Op.between]: [startDate, endDate]
      };
    }

    // 类型过滤
    if (type && ['income', 'expense'].includes(type)) {
      whereCondition.type = type;
    }

    // 查询统计数据
    const [totalIncome, totalExpense, categoryStats, monthlyTrend] = await Promise.all([
      // 总收入
      FinanceRecord.sum('amount', {
        where: { ...whereCondition, type: 'income' }
      }),

      // 总支出
      FinanceRecord.sum('amount', {
        where: { ...whereCondition, type: 'expense' }
      }),

      // 分类统计
      FinanceRecord.findAll({
        attributes: [
          'category',
          'type',
          [sequelize.fn('SUM', sequelize.col('amount')), 'total'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        where: whereCondition,
        group: ['category', 'type'],
        order: [[sequelize.fn('SUM', sequelize.col('amount')), 'DESC']]
      }),

      // 月度趋势
      getMonthlyTrend(whereCondition)
    ]);

    res.json({
      success: true,
      data: {
        summary: {
          totalIncome: Math.round(totalIncome || 0),
          totalExpense: Math.round(totalExpense || 0),
          netIncome: Math.round((totalIncome || 0) - (totalExpense || 0))
        },
        categoryStats: categoryStats || [],
        monthlyTrend: monthlyTrend || [],
        permissions: getUserPermissions(userRole)
      }
    });
  } catch (error) {
    console.error('获取财务统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取财务统计失败',
      error: error.message
    });
  }
};

/**
 * 获取待审批事项
 */
exports.getPendingApprovals = async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 10 } = req.query;

    const offset = (page - 1) * limit;

    const { count, rows } = await ApprovalWorkflow.findAndCountAll({
      where: {
        approverId: userId,
        status: 'pending',
        isActive: true
      },
      include: [{
        model: FinanceApplication,
        as: 'application',
        attributes: ['id', 'applicationNo', 'type', 'title', 'amount', 'applicantId', 'applicationDate']
      }],
      order: [['created_at', 'ASC']],
      limit: parseInt(limit),
      offset: offset
    });

    res.json({
      success: true,
      data: {
        total: count,
        items: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取待审批事项失败:', error);
    res.status(500).json({
      success: false,
      message: '获取待审批事项失败',
      error: error.message
    });
  }
};

/**
 * 获取最近活动
 */
async function getRecentActivities(userId, userRole) {
  try {
    const whereCondition = ['admin', 'manager', 'finance'].includes(userRole) 
      ? {} 
      : { applicantId: userId };

    const recentApplications = await FinanceApplication.findAll({
      where: whereCondition,
      order: [['updated_at', 'DESC']],
      limit: 10,
      attributes: ['id', 'applicationNo', 'type', 'title', 'status', 'amount', 'updated_at']
    });

    return recentApplications.map(app => ({
      id: app.id,
      type: 'application',
      title: app.title,
      description: `${app.applicationNo} - ${getApplicationTypeText(app.type)}`,
      status: app.status,
      amount: app.amount,
      time: app.updated_at
    }));
  } catch (error) {
    console.error('获取最近活动失败:', error);
    return [];
  }
}

/**
 * 获取快速操作 - 移除财务相关快捷操作，统一通过财务管理模块访问
 */
function getQuickActions(userRole) {
  // 返回空数组，所有财务功能通过财务管理模块统一访问
  return [];
}

/**
 * 获取用户权限
 */
function getUserPermissions(userRole) {
  const permissions = {
    canViewAll: ['admin', 'manager', 'finance'].includes(userRole),
    canApprove: ['admin', 'manager', 'finance'].includes(userRole),
    canExport: ['admin', 'manager', 'finance'].includes(userRole),
    canManageUsers: ['admin', 'manager'].includes(userRole),
    canViewFinanceData: true,
    canCreateApplication: true
  };

  return permissions;
}

/**
 * 获取申请类型文本
 */
function getApplicationTypeText(type) {
  const typeMap = {
    expense: '费用报销',
    purchase: '采购申请',
    payment: '付款申请',
    contract: '合同申请',
    activity: '活动经费',
    reserve: '备用金'
  };
  return typeMap[type] || type;
}

/**
 * 获取月度趋势数据
 */
async function getMonthlyTrend(whereCondition) {
  // 这里可以实现月度趋势查询逻辑
  // 暂时返回空数组，后续可以扩展
  return [];
}
