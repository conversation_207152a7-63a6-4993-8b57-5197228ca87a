/**
 * 财务管理控制器
 * 处理财务概览、统计和报表相关的API
 */

const { Op } = require('sequelize');
const { 
  FinanceApplication, 
  FinanceRecord,
  ApprovalWorkflow 
} = require('../../models/workspace');
const { 
  ROLES, 
  ROLE_LEVELS, 
  FINANCE_RELATED_TYPES,
  requireFinanceAccess,
  filterDataAccess
} = require('../../middleware/rbac');

/**
 * 获取财务概览统计数据
 * 需要财务权限或更高权限
 */
exports.getFinanceStatistics = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role || ROLES.EMPLOYEE;

    // 权限检查
    if (userRole === ROLES.EMPLOYEE) {
      return res.status(403).json({
        success: false,
        message: '普通员工无权访问财务统计数据'
      });
    }

    const currentDate = new Date();
    const currentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const nextMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);

    // 构建查询条件
    let whereCondition = {};
    
    // 财务人员只能查看财务相关数据
    if (userRole === ROLES.FINANCE) {
      whereCondition.type = {
        [Op.in]: FINANCE_RELATED_TYPES
      };
    }

    // 获取总收入和支出
    const totalStats = await FinanceApplication.findAll({
      where: {
        ...whereCondition,
        status: 'approved'
      },
      attributes: [
        [sequelize.fn('SUM', sequelize.col('amount')), 'totalAmount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalCount']
      ],
      raw: true
    });

    // 获取本月统计
    const monthlyStats = await FinanceApplication.findAll({
      where: {
        ...whereCondition,
        status: 'approved',
        applicationDate: {
          [Op.gte]: currentMonth,
          [Op.lt]: nextMonth
        }
      },
      attributes: [
        [sequelize.fn('SUM', sequelize.col('amount')), 'monthlyAmount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'monthlyCount']
      ],
      raw: true
    });

    // 获取待审批金额
    const pendingStats = await FinanceApplication.findAll({
      where: {
        ...whereCondition,
        status: 'pending'
      },
      attributes: [
        [sequelize.fn('SUM', sequelize.col('amount')), 'pendingAmount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'pendingCount']
      ],
      raw: true
    });

    // 获取已批准金额
    const approvedStats = await FinanceApplication.findAll({
      where: {
        ...whereCondition,
        status: 'approved'
      },
      attributes: [
        [sequelize.fn('SUM', sequelize.col('amount')), 'approvedAmount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'approvedCount']
      ],
      raw: true
    });

    const statistics = {
      totalIncome: totalStats[0]?.totalAmount || 0,
      totalExpense: totalStats[0]?.totalAmount || 0, // 这里需要根据业务逻辑区分收入和支出
      monthlyIncome: monthlyStats[0]?.monthlyAmount || 0,
      monthlyExpense: monthlyStats[0]?.monthlyAmount || 0,
      pendingAmount: pendingStats[0]?.pendingAmount || 0,
      approvedAmount: approvedStats[0]?.approvedAmount || 0,
      totalCount: totalStats[0]?.totalCount || 0,
      monthlyCount: monthlyStats[0]?.monthlyCount || 0,
      pendingCount: pendingStats[0]?.pendingCount || 0,
      approvedCount: approvedStats[0]?.approvedCount || 0
    };

    res.json({
      success: true,
      data: statistics
    });

  } catch (error) {
    console.error('获取财务统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取财务统计失败'
    });
  }
};

/**
 * 获取个人财务摘要（普通员工）
 */
exports.getPersonalSummary = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role || ROLES.EMPLOYEE;

    // 只有普通员工才能访问个人摘要
    if (userRole !== ROLES.EMPLOYEE) {
      return res.status(403).json({
        success: false,
        message: '此接口仅供普通员工使用'
      });
    }

    // 获取个人申请统计
    const personalStats = await FinanceApplication.findAll({
      where: {
        applicantId: userId
      },
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalApplications'],
        [sequelize.fn('COUNT', sequelize.literal("CASE WHEN status = 'pending' THEN 1 END")), 'pendingApplications'],
        [sequelize.fn('COUNT', sequelize.literal("CASE WHEN status = 'rejected' THEN 1 END")), 'rejectedCount'],
        [sequelize.fn('SUM', sequelize.literal("CASE WHEN status = 'approved' THEN amount ELSE 0 END")), 'approvedAmount']
      ],
      raw: true
    });

    const summary = {
      totalApplications: personalStats[0]?.totalApplications || 0,
      pendingApplications: personalStats[0]?.pendingApplications || 0,
      rejectedCount: personalStats[0]?.rejectedCount || 0,
      approvedAmount: personalStats[0]?.approvedAmount || 0
    };

    res.json({
      success: true,
      data: summary
    });

  } catch (error) {
    console.error('获取个人财务摘要失败:', error);
    res.status(500).json({
      success: false,
      message: '获取个人财务摘要失败'
    });
  }
};

/**
 * 获取最近财务记录
 */
exports.getRecentRecords = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role || ROLES.EMPLOYEE;
    const { limit = 10 } = req.query;

    // 权限检查
    if (userRole === ROLES.EMPLOYEE) {
      return res.status(403).json({
        success: false,
        message: '普通员工无权访问财务记录'
      });
    }

    // 构建查询条件
    let whereCondition = {};
    
    // 财务人员只能查看财务相关数据
    if (userRole === ROLES.FINANCE) {
      whereCondition.type = {
        [Op.in]: FINANCE_RELATED_TYPES
      };
    }

    const records = await FinanceApplication.findAll({
      where: whereCondition,
      include: [{
        model: User,
        as: 'applicant',
        attributes: ['id', 'name', 'email']
      }],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: {
        list: records,
        total: records.length
      }
    });

  } catch (error) {
    console.error('获取最近财务记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取最近财务记录失败'
    });
  }
};

/**
 * 获取月度趋势数据
 */
exports.getMonthlyTrend = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role || ROLES.EMPLOYEE;
    const { months = 6 } = req.query;

    // 权限检查
    if (userRole === ROLES.EMPLOYEE) {
      return res.status(403).json({
        success: false,
        message: '普通员工无权访问趋势数据'
      });
    }

    // 构建查询条件
    let whereCondition = {};
    
    // 财务人员只能查看财务相关数据
    if (userRole === ROLES.FINANCE) {
      whereCondition.type = {
        [Op.in]: FINANCE_RELATED_TYPES
      };
    }

    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - parseInt(months));

    const trendData = await FinanceApplication.findAll({
      where: {
        ...whereCondition,
        applicationDate: {
          [Op.gte]: startDate,
          [Op.lte]: endDate
        }
      },
      attributes: [
        [sequelize.fn('DATE_FORMAT', sequelize.col('applicationDate'), '%Y-%m'), 'month'],
        [sequelize.fn('SUM', sequelize.col('amount')), 'totalAmount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalCount']
      ],
      group: [sequelize.fn('DATE_FORMAT', sequelize.col('applicationDate'), '%Y-%m')],
      order: [[sequelize.fn('DATE_FORMAT', sequelize.col('applicationDate'), '%Y-%m'), 'ASC']],
      raw: true
    });

    res.json({
      success: true,
      data: trendData
    });

  } catch (error) {
    console.error('获取月度趋势失败:', error);
    res.status(500).json({
      success: false,
      message: '获取月度趋势失败'
    });
  }
};

/**
 * 获取业务类型统计
 */
exports.getBusinessTypeStats = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role || ROLES.EMPLOYEE;

    // 权限检查
    if (userRole === ROLES.EMPLOYEE) {
      return res.status(403).json({
        success: false,
        message: '普通员工无权访问业务统计'
      });
    }

    // 构建查询条件
    let whereCondition = {};
    
    // 财务人员只能查看财务相关数据
    if (userRole === ROLES.FINANCE) {
      whereCondition.type = {
        [Op.in]: FINANCE_RELATED_TYPES
      };
    }

    const stats = await FinanceApplication.findAll({
      where: whereCondition,
      attributes: [
        'type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('SUM', sequelize.col('amount')), 'totalAmount']
      ],
      group: ['type'],
      raw: true
    });

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('获取业务类型统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取业务类型统计失败'
    });
  }
};

module.exports = exports;
