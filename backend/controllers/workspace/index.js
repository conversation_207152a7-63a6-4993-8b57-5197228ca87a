/**
 * 工作台控制器索引文件
 * 统一导出所有工作台相关控制器
 */

const workspaceController = require('./workspace.controller');
const applicationController = require('./application.controller');
const approvalController = require('./approval.controller');
const dataSyncController = require('./data-sync.controller');

module.exports = {
  // 工作台主控制器
  ...workspaceController,

  // 申请管理控制器
  ...applicationController,

  // 审批管理控制器
  ...approvalController,

  // 数据同步控制器
  ...dataSyncController
};
