const { Op } = require('sequelize');
const { 
  FinanceApplication, 
  ApprovalWorkflow, 
  FinanceRecord 
} = require('../../models/workspace');

/**
 * 审批申请
 */
exports.approveApplication = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { action, comments, attachments } = req.body;

    // 验证操作类型
    if (!['approve', 'reject'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: '无效的审批操作'
      });
    }

    // 查找当前用户的审批任务
    const workflow = await ApprovalWorkflow.findOne({
      where: {
        applicationId: id,
        approverId: userId,
        status: 'pending',
        isActive: true
      },
      include: [{
        model: FinanceApplication,
        as: 'application'
      }]
    });

    if (!workflow) {
      return res.status(404).json({
        success: false,
        message: '未找到待审批的任务'
      });
    }

    const application = workflow.application;

    // 更新审批记录
    await workflow.update({
      status: action === 'approve' ? 'approved' : 'rejected',
      approvalDate: new Date(),
      comments,
      attachments,
      isActive: false
    });

    if (action === 'approve') {
      // 查找下一个审批步骤
      const nextWorkflow = await ApprovalWorkflow.findOne({
        where: {
          applicationId: id,
          step: workflow.step + 1,
          status: 'pending'
        }
      });

      if (nextWorkflow) {
        // 激活下一个审批步骤
        await nextWorkflow.update({ isActive: true });
        
        // 更新申请状态为处理中
        await application.update({ status: 'processing' });
      } else {
        // 所有审批步骤完成，更新申请状态为已批准
        await application.update({
          status: 'approved',
          approvedDate: new Date(),
          approvedBy: userId
        });

        // 创建财务记录
        await createFinanceRecord(application);
      }
    } else {
      // 拒绝申请
      await application.update({ status: 'rejected' });
      
      // 取消后续所有审批步骤
      await ApprovalWorkflow.update(
        { status: 'skipped', isActive: false },
        {
          where: {
            applicationId: id,
            step: { [Op.gt]: workflow.step },
            status: 'pending'
          }
        }
      );
    }

    res.json({
      success: true,
      message: action === 'approve' ? '审批通过' : '审批拒绝',
      data: {
        applicationId: id,
        status: application.status,
        action
      }
    });
  } catch (error) {
    console.error('审批操作失败:', error);
    res.status(500).json({
      success: false,
      message: '审批操作失败',
      error: error.message
    });
  }
};

/**
 * 批量审批
 */
exports.batchApprove = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const { applicationIds, action, comments } = req.body;

    // 权限检查：只有管理员和经理可以批量审批
    if (!['admin', 'manager'].includes(userRole)) {
      return res.status(403).json({
        success: false,
        message: '无权进行批量审批'
      });
    }

    if (!Array.isArray(applicationIds) || applicationIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请选择要审批的申请'
      });
    }

    const results = [];

    for (const applicationId of applicationIds) {
      try {
        // 查找当前用户的审批任务
        const workflow = await ApprovalWorkflow.findOne({
          where: {
            applicationId,
            approverId: userId,
            status: 'pending',
            isActive: true
          }
        });

        if (workflow) {
          // 执行审批操作
          await workflow.update({
            status: action === 'approve' ? 'approved' : 'rejected',
            approvalDate: new Date(),
            comments,
            isActive: false
          });

          results.push({
            applicationId,
            success: true,
            message: action === 'approve' ? '审批通过' : '审批拒绝'
          });

          // 处理后续流程（简化版本）
          if (action === 'approve') {
            await processNextApprovalStep(applicationId, workflow.step);
          } else {
            await rejectApplication(applicationId, workflow.step);
          }
        } else {
          results.push({
            applicationId,
            success: false,
            message: '未找到待审批的任务'
          });
        }
      } catch (error) {
        results.push({
          applicationId,
          success: false,
          message: error.message
        });
      }
    }

    res.json({
      success: true,
      message: '批量审批完成',
      data: results
    });
  } catch (error) {
    console.error('批量审批失败:', error);
    res.status(500).json({
      success: false,
      message: '批量审批失败',
      error: error.message
    });
  }
};

/**
 * 获取审批历史
 */
exports.getApprovalHistory = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    // 权限检查
    const application = await FinanceApplication.findByPk(id);
    if (!application) {
      return res.status(404).json({
        success: false,
        message: '申请不存在'
      });
    }

    if (!['admin', 'manager', 'finance'].includes(userRole) && 
        application.applicantId !== userId) {
      return res.status(403).json({
        success: false,
        message: '无权查看审批历史'
      });
    }

    const workflows = await ApprovalWorkflow.findAll({
      where: { applicationId: id },
      order: [['step', 'ASC']],
      // 这里应该包含用户信息，暂时省略
      // include: [{ model: User, as: 'approver', attributes: ['id', 'name'] }]
    });

    res.json({
      success: true,
      data: workflows
    });
  } catch (error) {
    console.error('获取审批历史失败:', error);
    res.status(500).json({
      success: false,
      message: '获取审批历史失败',
      error: error.message
    });
  }
};

/**
 * 获取我的审批任务
 */
exports.getMyApprovalTasks = async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 10, status = 'pending' } = req.query;

    const offset = (page - 1) * limit;

    const { count, rows } = await ApprovalWorkflow.findAndCountAll({
      where: {
        approverId: userId,
        status,
        ...(status === 'pending' ? { isActive: true } : {})
      },
      include: [{
        model: FinanceApplication,
        as: 'application',
        attributes: ['id', 'applicationNo', 'type', 'title', 'amount', 'applicantId', 'applicationDate', 'status']
      }],
      order: [['created_at', 'ASC']],
      limit: parseInt(limit),
      offset: offset
    });

    res.json({
      success: true,
      data: {
        total: count,
        items: rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取审批任务失败:', error);
    res.status(500).json({
      success: false,
      message: '获取审批任务失败',
      error: error.message
    });
  }
};

/**
 * 创建财务记录
 */
async function createFinanceRecord(application) {
  try {
    // 根据申请类型确定财务记录类别
    const categoryMap = {
      expense: 'other_expense',
      purchase: 'equipment_cost',
      payment: 'other_expense',
      contract: 'other_expense',
      activity: 'other_expense',
      reserve: 'other_expense'
    };

    await FinanceRecord.create({
      type: 'expense', // 大部分申请都是支出
      category: categoryMap[application.type],
      amount: application.amount,
      recordDate: new Date(),
      description: `${application.title} - ${application.applicationNo}`,
      sourceType: 'application',
      sourceId: application.id,
      relatedApplicationId: application.id,
      userId: application.applicantId,
      status: 'confirmed'
    });
  } catch (error) {
    console.error('创建财务记录失败:', error);
    // 不抛出错误，避免影响审批流程
  }
}

/**
 * 处理下一个审批步骤
 */
async function processNextApprovalStep(applicationId, currentStep) {
  const nextWorkflow = await ApprovalWorkflow.findOne({
    where: {
      applicationId,
      step: currentStep + 1,
      status: 'pending'
    }
  });

  if (nextWorkflow) {
    await nextWorkflow.update({ isActive: true });
    await FinanceApplication.update(
      { status: 'processing' },
      { where: { id: applicationId } }
    );
  } else {
    // 所有步骤完成
    const application = await FinanceApplication.findByPk(applicationId);
    await application.update({
      status: 'approved',
      approvedDate: new Date()
    });
    await createFinanceRecord(application);
  }
}

/**
 * 拒绝申请
 */
async function rejectApplication(applicationId, currentStep) {
  await FinanceApplication.update(
    { status: 'rejected' },
    { where: { id: applicationId } }
  );

  await ApprovalWorkflow.update(
    { status: 'skipped', isActive: false },
    {
      where: {
        applicationId,
        step: { [Op.gt]: currentStep },
        status: 'pending'
      }
    }
  );
}
