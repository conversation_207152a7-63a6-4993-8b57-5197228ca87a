// backend/controllers/workspace/purchase.controller.js
const ResponseHelper = require('../../utils/response-helper.js');

/**
 * 获取采购申请列表
 */
const getPurchaseList = async (req, res) => {
  try {
    const { page = 1, limit = 20, status, dateRange } = req.query;
    
    // 模拟数据
    const mockData = {
      list: [
        {
          id: 'purchase_001',
          title: '办公用品采购申请',
          description: '采购打印纸、文具等办公用品',
          totalAmount: 2500.00,
          status: 'pending',
          urgencyLevel: 'normal',
          expectedDate: '2024-01-15',
          supplier: '办公用品供应商',
          supplierContact: '13800138001',
          applicantName: '张三',
          applicantId: 'user_001',
          createdAt: '2024-01-08T10:00:00Z',
          updatedAt: '2024-01-08T10:00:00Z',
          items: [
            {
              id: 'item_001',
              name: 'A4打印纸',
              specification: '70g 500张/包',
              quantity: 20,
              unit: '包',
              unitPrice: 25.00,
              subtotal: 500.00,
              remark: '白色复印纸'
            },
            {
              id: 'item_002',
              name: '签字笔',
              specification: '0.5mm 黑色',
              quantity: 100,
              unit: '支',
              unitPrice: 2.00,
              subtotal: 200.00,
              remark: ''
            }
          ]
        },
        {
          id: 'purchase_002',
          title: '设备维修材料采购',
          description: '采购设备维修所需的零配件和工具',
          totalAmount: 8800.00,
          status: 'approved',
          urgencyLevel: 'high',
          expectedDate: '2024-01-12',
          supplier: '机械设备公司',
          supplierContact: '13900139001',
          applicantName: '李四',
          applicantId: 'user_002',
          createdAt: '2024-01-05T14:30:00Z',
          updatedAt: '2024-01-06T09:15:00Z',
          items: [
            {
              id: 'item_003',
              name: '电机轴承',
              specification: '6205-2RS',
              quantity: 4,
              unit: '个',
              unitPrice: 150.00,
              subtotal: 600.00,
              remark: '进口轴承'
            },
            {
              id: 'item_004',
              name: '液压油',
              specification: '46号抗磨液压油',
              quantity: 20,
              unit: '升',
              unitPrice: 35.00,
              subtotal: 700.00,
              remark: '品牌液压油'
            }
          ]
        }
      ],
      total: 15,
      page: parseInt(page),
      limit: parseInt(limit),
      hasMore: page * limit < 15
    };

    ResponseHelper.success(res, mockData);
  } catch (error) {
    console.error('获取采购申请列表失败:', error);
    ResponseHelper.error(res, '获取采购申请列表失败', 500);
  }
};

/**
 * 获取采购申请详情
 */
const getPurchaseDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 模拟数据
    const mockData = {
      id: id,
      title: '办公用品采购申请',
      description: '采购打印纸、文具等办公用品，用于日常办公使用',
      totalAmount: 2500.00,
      status: 'pending',
      urgencyLevel: 'normal',
      expectedDate: '2024-01-15',
      supplier: '办公用品供应商',
      supplierContact: '13800138001',
      applicantName: '张三',
      applicantId: 'user_001',
      createdAt: '2024-01-08T10:00:00Z',
      updatedAt: '2024-01-08T10:00:00Z',
      items: [
        {
          id: 'item_001',
          name: 'A4打印纸',
          specification: '70g 500张/包',
          quantity: 20,
          unit: '包',
          unitPrice: 25.00,
          subtotal: 500.00,
          remark: '白色复印纸'
        },
        {
          id: 'item_002',
          name: '签字笔',
          specification: '0.5mm 黑色',
          quantity: 100,
          unit: '支',
          unitPrice: 2.00,
          subtotal: 200.00,
          remark: ''
        }
      ],
      approvalHistory: [
        {
          id: 'approval_001',
          approverName: '王经理',
          approverId: 'manager_001',
          action: 'pending',
          comment: '',
          createdAt: '2024-01-08T10:00:00Z'
        }
      ]
    };

    ResponseHelper.success(res, mockData);
  } catch (error) {
    console.error('获取采购申请详情失败:', error);
    ResponseHelper.error(res, '获取采购申请详情失败', 500);
  }
};

/**
 * 创建采购申请
 */
const createPurchase = async (req, res) => {
  try {
    const purchaseData = req.body;
    
    // 模拟创建逻辑
    const newPurchase = {
      id: `purchase_${Date.now()}`,
      ...purchaseData,
      applicantName: '当前用户',
      applicantId: 'current_user',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    ResponseHelper.success(res, newPurchase, '采购申请创建成功');
  } catch (error) {
    console.error('创建采购申请失败:', error);
    ResponseHelper.error(res, '创建采购申请失败', 500);
  }
};

/**
 * 更新采购申请
 */
const updatePurchase = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    // 模拟更新逻辑
    const updatedPurchase = {
      id,
      ...updateData,
      updatedAt: new Date().toISOString()
    };

    ResponseHelper.success(res, updatedPurchase, '采购申请更新成功');
  } catch (error) {
    console.error('更新采购申请失败:', error);
    ResponseHelper.error(res, '更新采购申请失败', 500);
  }
};

/**
 * 删除采购申请
 */
const deletePurchase = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 模拟删除逻辑
    ResponseHelper.success(res, null, '采购申请删除成功');
  } catch (error) {
    console.error('删除采购申请失败:', error);
    ResponseHelper.error(res, '删除采购申请失败', 500);
  }
};

/**
 * 获取采购统计数据
 */
const getPurchaseStatistics = async (req, res) => {
  try {
    const mockData = {
      totalRequests: 45,
      pendingRequests: 8,
      approvedRequests: 32,
      rejectedRequests: 5,
      totalAmount: 125600.00,
      monthlyAmount: 28900.00,
      avgProcessingTime: 2.5, // 天
      topSuppliers: [
        { name: '办公用品供应商', amount: 15600.00, count: 12 },
        { name: '机械设备公司', amount: 45200.00, count: 8 },
        { name: '电子产品供应商', amount: 32800.00, count: 15 }
      ]
    };

    ResponseHelper.success(res, mockData);
  } catch (error) {
    console.error('获取采购统计数据失败:', error);
    ResponseHelper.error(res, '获取采购统计数据失败', 500);
  }
};

/**
 * 获取供应商列表
 */
const getSuppliers = async (req, res) => {
  try {
    const mockData = [
      {
        id: 'supplier_001',
        name: '办公用品供应商',
        contact: '13800138001',
        email: '<EMAIL>',
        address: '北京市朝阳区办公大厦',
        category: '办公用品',
        rating: 4.5,
        cooperationCount: 25
      },
      {
        id: 'supplier_002',
        name: '机械设备公司',
        contact: '13900139001',
        email: '<EMAIL>',
        address: '上海市浦东新区工业园',
        category: '机械设备',
        rating: 4.8,
        cooperationCount: 18
      }
    ];

    ResponseHelper.success(res, mockData);
  } catch (error) {
    console.error('获取供应商列表失败:', error);
    ResponseHelper.error(res, '获取供应商列表失败', 500);
  }
};

module.exports = {
  getPurchaseList,
  getPurchaseDetail,
  createPurchase,
  updatePurchase,
  deletePurchase,
  getPurchaseStatistics,
  getSuppliers
};
