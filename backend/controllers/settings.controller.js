/**
 * 系统设置控制器
 * Settings Controller
 */

const { generateSuccessResponse, generateErrorResponse } = require('../utils/response-helper');

/**
 * 获取系统设置
 */
const getSystemSettings = async (req, res) => {
  try {
    const settings = {
      system: {
        appName: '智慧养鹅SAAS平台',
        appVersion: '2.0.0',
        company: '智慧农业科技有限公司',
        copyright: '© 2024 Smart Goose SAAS Platform',
        contactEmail: '<EMAIL>',
        contactPhone: '************'
      },
      features: {
        aiDiagnosis: true,
        healthManagement: true,
        productionManagement: true,
        oaSystem: true,
        shopSystem: true,
        multiTenant: true
      },
      limits: {
        maxFlocks: 100,
        maxUsers: 50,
        maxStorage: '10GB',
        apiCallsPerHour: 1000
      },
      ui: {
        theme: 'default',
        primaryColor: '#1890ff',
        logoUrl: '/assets/images/logo.png',
        favicon: '/assets/images/favicon.ico'
      }
    };

    res.json(generateSuccessResponse(settings));
  } catch (error) {
    console.error('获取系统设置失败:', error);
    res.status(500).json(generateErrorResponse('获取系统设置失败'));
  }
};

/**
 * 获取版本信息
 */
const getVersion = async (req, res) => {
  try {
    const version = {
      current: '2.0.0',
      buildNumber: '20240115001',
      buildDate: '2024-01-15T10:00:00Z',
      environment: process.env.NODE_ENV || 'development',
      nodeVersion: process.version,
      platform: process.platform,
      changelog: [
        {
          version: '2.0.0',
          date: '2024-01-15',
          changes: [
            '新增AI智能诊断功能',
            '优化OA审批流程',
            '增强数据安全性',
            '改进用户界面体验'
          ]
        },
        {
          version: '1.9.5',
          date: '2024-01-10',
          changes: [
            '修复生产记录统计问题',
            '优化数据库性能',
            '更新帮助文档'
          ]
        }
      ]
    };

    res.json(generateSuccessResponse(version));
  } catch (error) {
    console.error('获取版本信息失败:', error);
    res.status(500).json(generateErrorResponse('获取版本信息失败'));
  }
};

/**
 * 获取配置信息
 */
const getConfig = async (req, res) => {
  try {
    const config = {
      api: {
        baseUrl: process.env.API_BASE_URL || 'http://localhost:3001',
        version: 'v1',
        timeout: 10000
      },
      upload: {
        maxFileSize: '10MB',
        allowedTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'],
        uploadPath: '/uploads'
      },
      cache: {
        enabled: true,
        ttl: 300, // 5分钟
        maxSize: 100 // 最大缓存项数
      },
      security: {
        jwtExpiresIn: '24h',
        passwordMinLength: 6,
        maxLoginAttempts: 5,
        lockoutDuration: 900 // 15分钟
      },
      notification: {
        enabled: true,
        types: ['system', 'approval', 'health', 'order'],
        retentionDays: 30
      },
      ai: {
        enabled: true,
        modelVersion: 'v2.1',
        confidence: 0.85,
        maxRequests: 100
      }
    };

    res.json(generateSuccessResponse(config));
  } catch (error) {
    console.error('获取配置信息失败:', error);
    res.status(500).json(generateErrorResponse('获取配置信息失败'));
  }
};

/**
 * 更新系统设置（管理员权限）
 */
const updateSystemSettings = async (req, res) => {
  try {
    const { settings } = req.body;
    
    if (!settings) {
      return res.status(400).json(generateErrorResponse('设置内容不能为空'));
    }

    // 这里应该验证并保存设置到数据库
    console.log('更新系统设置:', settings);

    res.json(generateSuccessResponse({ 
      message: '系统设置更新成功',
      updatedAt: new Date().toISOString()
    }));
  } catch (error) {
    console.error('更新系统设置失败:', error);
    res.status(500).json(generateErrorResponse('更新系统设置失败'));
  }
};

/**
 * 获取系统状态
 */
const getSystemStatus = async (req, res) => {
  try {
    const status = {
      server: {
        status: 'running',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
        version: process.version,
        platform: process.platform
      },
      database: {
        status: 'connected',
        host: process.env.DB_HOST || 'localhost',
        database: process.env.DB_NAME || 'smart_goose_saas',
        connectionPool: {
          active: 5,
          idle: 3,
          total: 8
        }
      },
      services: {
        api: 'running',
        auth: 'running',
        notification: 'running',
        upload: 'running',
        ai: 'running'
      },
      health: {
        overall: 'healthy',
        lastCheck: new Date().toISOString(),
        issues: []
      }
    };

    res.json(generateSuccessResponse(status));
  } catch (error) {
    console.error('获取系统状态失败:', error);
    res.status(500).json(generateErrorResponse('获取系统状态失败'));
  }
};

module.exports = {
  getSystemSettings,
  getVersion,
  getConfig,
  updateSystemSettings,
  getSystemStatus
};