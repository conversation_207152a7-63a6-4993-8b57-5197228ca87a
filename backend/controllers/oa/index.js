/**
 * OA控制器索引文件
 * 统一导出所有OA相关控制器
 */

const oaAuth = require('./oa-auth.controller');
const oaWorkflow = require('./oa-workflow.controller');
const oaApproval = require('./oa-approval.controller');
const oaFinance = require('./oa-finance.controller');
const oaNotification = require('./oa-notification.controller');

const oaControllers = {
  ...oaAuth,
  ...oaWorkflow,
  ...oaApproval,
  ...oaFinance,
  ...oaNotification
};

module.exports = oaControllers;
