/**
 * 首页控制器
 * Home Controller
 */

const { generateSuccessResponse, generateErrorResponse } = require('../utils/response-helper');

/**
 * 获取首页统计数据
 */
const getHomeStatistics = async (req, res) => {
  try {
    // 模拟首页统计数据
    const statistics = {
      overview: {
        totalFlocks: 150,
        totalGeese: 12500,
        healthyRate: 98.5,
        productionRate: 95.2
      },
      recentActivity: {
        newHealthRecords: 25,
        pendingApprovals: 8,
        urgentTasks: 3,
        completedOrders: 15
      },
      trends: {
        healthTrend: '+2.3%',
        productionTrend: '+5.1%',
        financeTrend: '+8.7%',
        efficiencyTrend: '+3.2%'
      }
    };

    res.json(generateSuccessResponse(statistics));
  } catch (error) {
    console.error('获取首页统计失败:', error);
    res.status(500).json(generateErrorResponse('获取首页统计失败'));
  }
};

/**
 * 获取首页公告
 */
const getAnnouncements = async (req, res) => {
  try {
    const announcements = [
      {
        id: 1,
        title: '系统维护通知',
        content: '系统将于今晚23:00-01:00进行维护升级',
        type: 'system',
        priority: 'high',
        createTime: new Date().toISOString()
      },
      {
        id: 2,
        title: '新功能上线',
        content: 'AI智能诊断功能正式上线，欢迎体验',
        type: 'feature',
        priority: 'medium',
        createTime: new Date().toISOString()
      }
    ];

    res.json(generateSuccessResponse(announcements));
  } catch (error) {
    console.error('获取公告失败:', error);
    res.status(500).json(generateErrorResponse('获取公告失败'));
  }
};

/**
 * 获取天气信息
 */
const getWeather = async (req, res) => {
  try {
    const weather = {
      location: '杭州市',
      temperature: 22,
      condition: '晴转多云',
      humidity: 65,
      windSpeed: '3级',
      airQuality: '良',
      suggestion: '适宜户外养殖作业'
    };

    res.json(generateSuccessResponse(weather));
  } catch (error) {
    console.error('获取天气信息失败:', error);
    res.status(500).json(generateErrorResponse('获取天气信息失败'));
  }
};

/**
 * 获取快捷操作
 */
const getQuickActions = async (req, res) => {
  try {
    const quickActions = [
      { id: 'health_record', name: '添加健康记录', icon: 'health', url: '/pages/health/record/add' },
      { id: 'production_record', name: '生产记录', icon: 'production', url: '/pages/production/record/add' },
      { id: 'purchase_apply', name: '采购申请', icon: 'purchase', url: '/pages/workspace/purchase/apply/apply' },
      { id: 'finance_report', name: '财务报表', icon: 'finance', url: '/pages/workspace/finance/reports/reports' }
    ];

    res.json(generateSuccessResponse(quickActions));
  } catch (error) {
    console.error('获取快捷操作失败:', error);
    res.status(500).json(generateErrorResponse('获取快捷操作失败'));
  }
};

module.exports = {
  getHomeStatistics,
  getAnnouncements,
  getWeather,
  getQuickActions
};