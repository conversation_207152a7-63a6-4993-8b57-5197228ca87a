/**
 * 通知控制器
 * Notification Controller
 */

const { generateSuccessResponse, generateErrorResponse } = require('../utils/response-helper');

/**
 * 获取通知列表
 */
const getNotifications = async (req, res) => {
  try {
    const { page = 1, limit = 20, type, status } = req.query;
    const userId = req.user.id;

    // 模拟通知数据
    const notifications = [
      {
        id: 1,
        title: '审批提醒',
        content: '您有一个采购申请待审批',
        type: 'approval',
        status: 'unread',
        priority: 'high',
        createTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2小时前
        data: {
          approvalId: 'PUR-20240115-001',
          approvalType: 'purchase'
        }
      },
      {
        id: 2,
        title: '健康预警',
        content: '鹅群A区出现异常健康指标，请及时处理',
        type: 'health',
        status: 'unread',
        priority: 'urgent',
        createTime: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(), // 5小时前
        data: {
          flockId: 'FL-001',
          alertType: 'health_warning'
        }
      },
      {
        id: 3,
        title: '系统通知',
        content: '系统将于今晚23:00-01:00进行维护升级',
        type: 'system',
        status: 'read',
        priority: 'medium',
        createTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1天前
        data: {
          maintenanceTime: '2024-01-15 23:00:00'
        }
      },
      {
        id: 4,
        title: '订单状态',
        content: '您的订单 #ORD-20240114-002 已发货',
        type: 'order',
        status: 'read',
        priority: 'low',
        createTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2天前
        data: {
          orderId: 'ORD-20240114-002',
          status: 'shipped'
        }
      }
    ];

    // 过滤通知
    let filteredNotifications = notifications;
    if (type) {
      filteredNotifications = filteredNotifications.filter(n => n.type === type);
    }
    if (status) {
      filteredNotifications = filteredNotifications.filter(n => n.status === status);
    }

    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedNotifications = filteredNotifications.slice(startIndex, endIndex);

    const result = {
      notifications: paginatedNotifications,
      pagination: {
        current: parseInt(page),
        pageSize: parseInt(limit),
        total: filteredNotifications.length,
        totalPages: Math.ceil(filteredNotifications.length / limit)
      },
      summary: {
        unreadCount: notifications.filter(n => n.status === 'unread').length,
        urgentCount: notifications.filter(n => n.priority === 'urgent').length
      }
    };

    res.json(generateSuccessResponse(result));
  } catch (error) {
    console.error('获取通知列表失败:', error);
    res.status(500).json(generateErrorResponse('获取通知列表失败'));
  }
};

/**
 * 获取未读通知数量
 */
const getUnreadCount = async (req, res) => {
  try {
    const userId = req.user.id;

    // 模拟未读通知数量
    const unreadCount = {
      total: 2,
      byType: {
        approval: 1,
        health: 1,
        system: 0,
        order: 0
      },
      byPriority: {
        urgent: 1,
        high: 1,
        medium: 0,
        low: 0
      }
    };

    res.json(generateSuccessResponse(unreadCount));
  } catch (error) {
    console.error('获取未读通知数量失败:', error);
    res.status(500).json(generateErrorResponse('获取未读通知数量失败'));
  }
};

/**
 * 标记通知为已读
 */
const markAsRead = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // 这里应该更新数据库中的通知状态
    console.log(`标记通知 ${id} 为已读，用户ID: ${userId}`);

    res.json(generateSuccessResponse({ notificationId: id, status: 'read' }));
  } catch (error) {
    console.error('标记通知为已读失败:', error);
    res.status(500).json(generateErrorResponse('标记通知为已读失败'));
  }
};

/**
 * 标记所有通知为已读
 */
const markAllAsRead = async (req, res) => {
  try {
    const userId = req.user.id;

    // 这里应该更新数据库中所有未读通知的状态
    console.log(`标记用户 ${userId} 的所有通知为已读`);

    res.json(generateSuccessResponse({ message: '所有通知已标记为已读' }));
  } catch (error) {
    console.error('标记所有通知为已读失败:', error);
    res.status(500).json(generateErrorResponse('标记所有通知为已读失败'));
  }
};

/**
 * 删除通知
 */
const deleteNotification = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // 这里应该从数据库中删除通知
    console.log(`删除通知 ${id}，用户ID: ${userId}`);

    res.json(generateSuccessResponse({ notificationId: id, deleted: true }));
  } catch (error) {
    console.error('删除通知失败:', error);
    res.status(500).json(generateErrorResponse('删除通知失败'));
  }
};

/**
 * 创建通知（系统内部使用）
 */
const createNotification = async (req, res) => {
  try {
    const { userId, title, content, type, priority = 'medium', data = {} } = req.body;

    if (!userId || !title || !content) {
      return res.status(400).json(generateErrorResponse('用户ID、标题和内容不能为空'));
    }

    const notification = {
      id: Date.now(), // 简单的ID生成
      userId,
      title,
      content,
      type,
      priority,
      status: 'unread',
      data,
      createTime: new Date().toISOString()
    };

    // 这里应该保存到数据库
    console.log('创建通知:', notification);

    res.json(generateSuccessResponse(notification));
  } catch (error) {
    console.error('创建通知失败:', error);
    res.status(500).json(generateErrorResponse('创建通知失败'));
  }
};

module.exports = {
  getNotifications,
  getUnreadCount,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  createNotification
};