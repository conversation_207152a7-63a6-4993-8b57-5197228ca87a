// oa.controller-crud.js
// 自动生成的模块文件

const db = require("../config/database");

// crud 相关功能
async function updateBusinessStatus(businessType, businessId, approvalStatus) {
  let tableName = "";
  let statusValue = "";

  switch (businessType) {
    case "expense":
    case "reimbursement":
      tableName = "oa_reimbursements";
      statusValue = approvalStatus === "approved" ? "approved" : "rejected";
      break;
    case "purchase":
      tableName = "oa_purchase_requests";
      statusValue = approvalStatus === "approved" ? "approved" : "rejected";
      break;
    case "payment":
      tableName = "oa_payment_requests";
      statusValue = approvalStatus === "approved" ? "approved" : "rejected";
      break;
    case "contract":
      tableName = "oa_contract_requests";
      statusValue = approvalStatus === "approved" ? "approved" : "rejected";
      break;
    case "activity":
      tableName = "oa_activity_requests";
      statusValue = approvalStatus === "approved" ? "approved" : "rejected";
      break;
    case "reserve":
      tableName = "oa_reserve_requests";
      statusValue = approvalStatus === "approved" ? "approved" : "rejected";
      break;
    default:
      return; // 不支持的业务类型
  }

  await db.query(
    `
    UPDATE ${tableName}
    SET status = ?, updated_at = NOW()
    WHERE id = ?
  `,
    [statusValue, businessId],
  );
}

async function createApprovalWorkflow(
  businessId,
  businessType,
  title,
  applicantId,
  amount,
) {
  // 根据业务类型和金额确定审批流程
  const [workflows] = await db.query(
    `
    SELECT * FROM oa_approval_workflows
    WHERE category = ? AND is_active = true
    ORDER BY version DESC LIMIT 1
  `,
    [businessType],
  );

  if (workflows.length === 0) {
    throw new Error("未找到对应的审批流程");
  }

  const workflow = workflows[0];
  const stepsConfig = JSON.parse(workflow.steps_config);

  // 创建审批实例
  const [approvalResult] = await db.query(
    `
    INSERT INTO oa_approvals (
      workflow_id, business_id, business_type, title, applicant_id, total_steps
    ) VALUES (?, ?, ?, ?, ?, ?)
  `,
    [
      workflow.id,
      businessId,
      businessType,
      title,
      applicantId,
      stepsConfig.length,
    ],
  );

  const approvalId = approvalResult.insertId;

  // 创建审批步骤
  for (let i = 0; i < stepsConfig.length; i++) {
    const step = stepsConfig[i];

    // 根据条件判断是否需要此步骤
    if (step.condition && !evaluateCondition(step.condition, { amount })) {
      continue;
    }

    // 查找审批人（简化处理，实际应该根据角色和部门查找）
    const [approvers] = await db.query(`
      SELECT id FROM users
      WHERE JSON_CONTAINS(permissions, '{"canApprove": true}')
      LIMIT 1
    `);

    if (approvers.length > 0) {
      await db.query(
        `
        INSERT INTO oa_approval_steps (
          approval_id, step_number, step_name, approver_id, deadline
        ) VALUES (?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL 3 DAY))
      `,
        [approvalId, step.step, step.name, approvers[0].id],
      );
    }
  }

  return approvalId;
}

async function updateApprovalWorkflow(
  businessId,
  businessType,
  action,
  approverId,
  remarks,
) {
  // 查找审批实例
  const [approvals] = await db.query(
    `
    SELECT * FROM oa_approvals
    WHERE business_id = ? AND business_type = ? AND status = 'pending'
  `,
    [businessId, businessType],
  );

  if (approvals.length === 0) {
    return;
  }

  const approval = approvals[0];

  // 更新当前步骤
  await db.query(
    `
    UPDATE oa_approval_steps
    SET status = ?, action = ?, remarks = ?, processed_at = NOW()
    WHERE approval_id = ? AND step_number = ? AND approver_id = ?
  `,
    [
      action === "approve" ? "approved" : "rejected",
      action,
      remarks,
      approval.id,
      approval.current_step,
      approverId,
    ],
  );

  // 更新审批实例状态
  if (action === "reject") {
    await db.query(
      `
      UPDATE oa_approvals
      SET status = 'rejected', completed_at = NOW()
      WHERE id = ?
    `,
      [approval.id],
    );
  } else if (approval.current_step >= approval.total_steps) {
    await db.query(
      `
      UPDATE oa_approvals
      SET status = 'approved', completed_at = NOW()
      WHERE id = ?
    `,
      [approval.id],
    );
  } else {
    await db.query(
      `
      UPDATE oa_approvals
      SET current_step = current_step + 1
      WHERE id = ?
    `,
      [approval.id],
    );
  }
}

module.exports = {
  // 导出相关函数
};