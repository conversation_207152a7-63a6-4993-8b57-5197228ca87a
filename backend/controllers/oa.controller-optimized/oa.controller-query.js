// oa.controller-query.js
// 自动生成的模块文件

const db = require("../config/database");

// query 相关功能
async function getIncomeExpenseReport(startDate, endDate, userId) {
  const revenueQuery = `
    SELECT 
      DATE(revenue_date) as date,
      SUM(amount) as amount,
      'income' as type,
      category
    FROM oa_revenues 
    WHERE user_id = ? AND revenue_date BETWEEN ? AND ?
    AND status = 'confirmed'
    GROUP BY DATE(revenue_date), category
  `;

  const expenseQuery = `
    SELECT 
      DATE(expense_date) as date,
      SUM(amount) as amount,
      'expense' as type,
      category
    FROM oa_expenses 
    WHERE user_id = ? AND expense_date BETWEEN ? AND ?
    AND status = 'approved'
    GROUP BY DATE(expense_date), category
  `;

  const [revenueData] = await db.query(revenueQuery, [userId, startDate, endDate]);
  const [expenseData] = await db.query(expenseQuery, [userId, startDate, endDate]);

  // 合并收入和支出数据
  const combined = [...revenueData, ...expenseData].sort(
    (a, b) => new Date(b.date) - new Date(a.date),
  );

  return combined;
}

async function getMonthlySummaryReport(startDate, endDate, userId) {
  const query = `
    SELECT 
      DATE_FORMAT(expense_date, '%Y-%m') as month,
      SUM(CASE WHEN type = 'revenue' THEN amount ELSE 0 END) as totalIncome,
      SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as totalExpense,
      (SUM(CASE WHEN type = 'revenue' THEN amount ELSE 0 END) - 
       SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END)) as netProfit
    FROM (
      SELECT expense_date, amount, 'revenue' as type FROM oa_revenues WHERE user_id = ?
      UNION ALL
      SELECT expense_date, amount, 'expense' as type FROM oa_expenses WHERE user_id = ?
    ) combined
    WHERE expense_date BETWEEN ? AND ?
    GROUP BY DATE_FORMAT(expense_date, '%Y-%m')
    ORDER BY month DESC
  `;

  const [results] = await db.query(query, [userId, userId, startDate, endDate]);
  return results;
}

async function getQuarterlyReport(startDate, endDate, userId) {
  // 季度报表查询逻辑
  return [];
}

async function getAnnualReport(startDate, endDate, userId) {
  // 年度报表查询逻辑
  return [];
}

async function getCategoryAnalysisReport(startDate, endDate, userId) {
  // 分类分析报表查询逻辑
  return [];
}

async function getTrendAnalysisReport(startDate, endDate, userId) {
  // 趋势分析报表查询逻辑
  return [];
}

module.exports = {
  getIncomeExpenseReport,
  getMonthlySummaryReport,
  getQuarterlyReport,
  getAnnualReport,
  getCategoryAnalysisReport,
  getTrendAnalysisReport
};