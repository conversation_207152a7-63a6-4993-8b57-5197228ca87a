// oa.controller-business.js
// 自动生成的模块文件

const db = require("../config/database");

// business 相关功能
exports.getOAStats = async (req, res) => {
  try {
    const userId = req.user.id;

    // 并行查询各种统计数据
    const [pendingApprovals, todayTasks, thisMonthExpense, unreadNotices] =
      await Promise.all([
        // 待审批数量
        db.query(
          `
        SELECT COUNT(*) as count 
        FROM oa_approvals 
        WHERE status = 'pending' 
        AND (approver_id = ? OR creator_id = ?)
      `,
          [userId, userId],
        ),

        // 今日任务数
        db.query(
          `
        SELECT COUNT(*) as count 
        FROM oa_tasks 
        WHERE DATE(created_at) = CURDATE() 
        AND (assignee_id = ? OR creator_id = ?)
      `,
          [userId, userId],
        ),

        // 本月支出总额
        db.query(`
        SELECT COALESCE(SUM(amount), 0) as total 
        FROM oa_expenses 
        WHERE YEAR(expense_date) = YEAR(NOW()) 
        AND MONTH(expense_date) = MONTH(NOW())
        AND status = 'approved'
      `),

        // 未读通知数
        db.query(
          `
        SELECT COUNT(*) as count 
        FROM oa_notifications 
        WHERE recipient_id = ? 
        AND is_read = false
      `,
          [userId],
        ),
      ]);

    const stats = {
      pendingApprovals: pendingApprovals[0][0].count,
      todayTasks: todayTasks[0][0].count,
      thisMonthExpense: Math.round(thisMonthExpense[0][0].total),
      unreadNotices: unreadNotices[0][0].count,
    };

    res.json(generateResponse(stats));
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取OA统计数据失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取统计数据失败"));
  }
};

exports.getRecentActivities = async (req, res) => {
  try {
    const userId = req.user.id;
    const limit = parseInt(req.query.limit) || 10;

    // 查询最近的活动记录
    const [activities] = await db.query(
      `
      SELECT 
        a.id,
        a.type,
        a.title,
        a.description,
        a.status,
        a.created_at,
        CASE 
          WHEN a.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) 
          THEN CONCAT(TIMESTAMPDIFF(MINUTE, a.created_at, NOW()), '分钟前')
          WHEN a.created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) 
          THEN CONCAT(TIMESTAMPDIFF(HOUR, a.created_at, NOW()), '小时前')
          ELSE DATE_FORMAT(a.created_at, '%m-%d %H:%i')
        END as time
      FROM oa_activities a
      WHERE a.user_id = ?
      ORDER BY a.created_at DESC
      LIMIT ?
    `,
      [userId, limit],
    );

    res.json(generateResponse(activities));
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error("获取最近活动失败:", error); } catch(_) {}

    res.status(500).json(generateErrorResponse("获取活动记录失败"));
  }
};

// 辅助函数
function generateResponse(data) {
  return {
    success: true,
    data: data,
    timestamp: new Date().toISOString()
  };
}

function generateErrorResponse(message) {
  return {
    success: false,
    message: message,
    timestamp: new Date().toISOString()
  };
}

module.exports = {
  // 导出相关函数
};