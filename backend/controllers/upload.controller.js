/**
 * 文件上传控制器
 * Upload Controller
 */

const { generateSuccessResponse, generateErrorResponse } = require('../utils/response-helper');
const path = require('path');
const fs = require('fs');

/**
 * 上传图片
 */
const uploadImage = async (req, res) => {
  try {
    // 模拟图片上传
    if (!req.file && !req.files) {
      return res.status(400).json(generateErrorResponse('请选择要上传的图片'));
    }

    // 模拟上传结果
    const imageUrl = `/uploads/images/${Date.now()}_image.jpg`;
    const result = {
      url: imageUrl,
      originalName: 'image.jpg',
      size: 1024000, // 1MB
      type: 'image/jpeg',
      uploadTime: new Date().toISOString()
    };

    res.json(generateSuccessResponse(result));
  } catch (error) {
    console.error('图片上传失败:', error);
    res.status(500).json(generateErrorResponse('图片上传失败'));
  }
};

/**
 * 上传文件
 */
const uploadFile = async (req, res) => {
  try {
    // 模拟文件上传
    if (!req.file && !req.files) {
      return res.status(400).json(generateErrorResponse('请选择要上传的文件'));
    }

    // 模拟上传结果
    const fileUrl = `/uploads/files/${Date.now()}_document.pdf`;
    const result = {
      url: fileUrl,
      originalName: 'document.pdf',
      size: 2048000, // 2MB
      type: 'application/pdf',
      uploadTime: new Date().toISOString()
    };

    res.json(generateSuccessResponse(result));
  } catch (error) {
    console.error('文件上传失败:', error);
    res.status(500).json(generateErrorResponse('文件上传失败'));
  }
};

/**
 * 批量上传
 */
const batchUpload = async (req, res) => {
  try {
    // 模拟批量上传
    if (!req.files || req.files.length === 0) {
      return res.status(400).json(generateErrorResponse('请选择要上传的文件'));
    }

    // 模拟批量上传结果
    const results = [];
    for (let i = 0; i < 3; i++) {
      results.push({
        id: Date.now() + i,
        url: `/uploads/batch/${Date.now()}_file_${i}.jpg`,
        originalName: `file_${i}.jpg`,
        size: 512000 + i * 100000,
        type: 'image/jpeg',
        uploadTime: new Date().toISOString(),
        status: 'success'
      });
    }

    const summary = {
      total: results.length,
      success: results.filter(r => r.status === 'success').length,
      failed: results.filter(r => r.status === 'failed').length,
      totalSize: results.reduce((sum, r) => sum + r.size, 0)
    };

    res.json(generateSuccessResponse({
      files: results,
      summary
    }));
  } catch (error) {
    console.error('批量上传失败:', error);
    res.status(500).json(generateErrorResponse('批量上传失败'));
  }
};

/**
 * 删除文件
 */
const deleteFile = async (req, res) => {
  try {
    const { fileUrl } = req.body;
    
    if (!fileUrl) {
      return res.status(400).json(generateErrorResponse('文件URL不能为空'));
    }

    // 这里应该删除实际的文件
    console.log('删除文件:', fileUrl);

    res.json(generateSuccessResponse({
      message: '文件删除成功',
      fileUrl,
      deletedAt: new Date().toISOString()
    }));
  } catch (error) {
    console.error('删除文件失败:', error);
    res.status(500).json(generateErrorResponse('删除文件失败'));
  }
};

/**
 * 获取上传配置
 */
const getUploadConfig = async (req, res) => {
  try {
    const config = {
      image: {
        maxSize: 5 * 1024 * 1024, // 5MB
        allowedTypes: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        maxWidth: 2048,
        maxHeight: 2048
      },
      file: {
        maxSize: 10 * 1024 * 1024, // 10MB
        allowedTypes: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
      },
      batch: {
        maxFiles: 10,
        maxTotalSize: 50 * 1024 * 1024 // 50MB
      },
      paths: {
        images: '/uploads/images/',
        files: '/uploads/files/',
        temp: '/uploads/temp/'
      }
    };

    res.json(generateSuccessResponse(config));
  } catch (error) {
    console.error('获取上传配置失败:', error);
    res.status(500).json(generateErrorResponse('获取上传配置失败'));
  }
};

module.exports = {
  uploadImage,
  uploadFile,
  batchUpload,
  deleteFile,
  getUploadConfig
};