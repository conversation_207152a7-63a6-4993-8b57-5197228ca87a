// health.controller-crud.js
// 自动生成的模块文件

const HealthRecord = require('../models/health-record.model');
const { Op } = require('sequelize');

// crud 相关功能
exports.patchRecord = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const record = await HealthRecord.findOne({
      where: {
        id: id,
        userId: req.user.id
      }
    });

    if (!record) {
      return res.status(404).json({
        success: false,
        message: '健康记录不存在'
      });
    }

    // 更新记录
    await record.update(updateData);

    res.json({
      success: true,
      message: '健康记录更新成功',
      data: {
        id: record.id,
        userId: record.userId,
        鹅群编号: record.gooseId,
        健康状态: record.status,
        症状: record.symptoms,
        诊断: record.diagnosis,
        治疗: record.treatment,
        createdAt: record.created_at,
        updatedAt: record.updated_at
      }
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('部分更新健康记录失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

exports.batchUpdateRecords = async (req, res) => {
  try {
    const updates = req.body.updates;

    if (!updates || !Array.isArray(updates) || updates.length === 0) {
      return res.status(400).json({
        success: false,
        message: '更新列表不能为空'
      });
    }

    let updatedCount = 0;

    // 逐条更新记录
    for (const update of updates) {
      const { id, ...updateData } = update;

      const record = await HealthRecord.findOne({
        where: {
          id: id,
          userId: req.user.id
        }
      });

      if (record) {
        await record.update(updateData);
        updatedCount++;
      }
    }

    res.json({
      success: true,
      message: `成功更新${updatedCount}条健康记录`,
      data: {
        updatedCount: updatedCount
      }
    });
  } catch (error) {
    try { const { Logger } = require('../middleware/errorHandler'); Logger.error('批量更新健康记录失败', { error: error.message, stack: error.stack }); } catch(_) {}
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
};

module.exports = {
  // 导出相关函数
};