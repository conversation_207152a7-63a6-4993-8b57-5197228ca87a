/**
 * 智慧养鹅SAAS平台 - 统一API路由 (V1)
 * 基于微信小程序RESTful设计规范
 * 
 * 设计原则：
 * 1. 统一使用 /api/v1 版本
 * 2. 资源导向的RESTful设计
 * 3. 标准化权限控制
 * 4. 多租户数据隔离
 * 5. 统一响应格式
 */

const express = require('express');
const router = express.Router();

// 导入中间件
const { verifyToken } = require('../middleware/auth.middleware');
const { validatePermission } = require('../middleware/permission.middleware');
const { generateSuccessResponse, generateErrorResponse } = require('../utils/response-helper');

// 导入控制器
const authController = require('../controllers/auth.controller');
const userController = require('../controllers/user.controller');
const flockController = require('../controllers/flock.controller');
const healthController = require('../controllers/health.controller');
const productionController = require('../controllers/production.controller');
const inventoryController = require('../controllers/inventory.controller');
const oaController = require('../controllers/oa.controller');
const shopController = require('../controllers/shop.controller');
const homeController = require('../controllers/home.controller');
const helpController = require('../controllers/help-center.controller');

/**
 * 标准化响应中间件 - 符合微信小程序API规范
 * 响应格式：
 * 成功: { errcode: 0, errmsg: "ok", data: {...} }
 * 失败: { errcode: 40001, errmsg: "error message", data: null }
 */
const standardizeResponse = (req, res, next) => {
  const originalJson = res.json.bind(res);
  
  res.json = (data) => {
    // 如果已经是标准格式，直接返回
    if (data && typeof data === 'object' && ('errcode' in data || 'success' in data)) {
      return originalJson(data);
    }
    
    // 微信小程序标准响应格式
    const response = {
      errcode: 0,
      errmsg: "ok",
      data: data,
      timestamp: Date.now(),
      requestId: req.headers['x-request-id'] || `req_${Date.now()}`
    };
    
    return originalJson(response);
  };
  
  // 添加错误响应方法
  res.error = (errcode, errmsg, data = null) => {
    const response = {
      errcode: errcode || -1,
      errmsg: errmsg || "未知错误",
      data: data,
      timestamp: Date.now(),
      requestId: req.headers['x-request-id'] || `req_${Date.now()}`
    };
    
    return originalJson(response);
  };
  
  next();
};

/**
 * 租户信息识别中间件（兼容 X-Tenant-Code 与 X-Tenant-Id）
 */
const identifyTenant = async (req, res, next) => {
  try {
    // 统一兼容：优先使用 X-Tenant-Code，其次 X-Tenant-Id、子域名、查询参数
    const headerTenantCode = req.headers['x-tenant-code'];
    const headerTenantId = req.headers['x-tenant-id'];
    const tenantId = headerTenantCode || headerTenantId || req.subdomain || req.query.tenant_id || 'default';

    req.tenantId = tenantId;
    req.tenantContext = {
      id: tenantId,
      source: headerTenantCode ? 'X-Tenant-Code' : (headerTenantId ? 'X-Tenant-Id' : (req.subdomain ? 'subdomain' : 'query')),
      timestamp: Date.now()
    };

    next();
  } catch (error) {
    res.status(400).json(generateErrorResponse('租户识别失败', {
      code: 'TENANT_IDENTIFICATION_FAILED',
      error: error.message
    }));
  }
};

// 应用全局中间件
router.use(standardizeResponse);
router.use(identifyTenant);

// ====================================================================
// API信息和健康检查端点
// ====================================================================

/**
 * @api {get} /api/v1 API版本信息
 */
router.get('/', (req, res) => {
  res.json({
    name: '智慧养鹅SAAS平台API',
    version: 'v1.0',
    description: '基于RESTful设计的微信小程序API服务',
    timestamp: Date.now(),
    endpoints: {
      auth: '/api/v1/auth',
      users: '/api/v1/users', 
      health: '/api/v1/health',
      production: '/api/v1/production',
      oa: '/api/v1/oa',
      shop: '/api/v1/shop'
    },
    documentation: '/api/v1/docs'
  });
});

/**
 * @api {get} /api/v1/version API版本详情
 */
router.get('/version', (req, res) => {
  res.json({
    version: '1.0.0',
    buildNumber: '20240115001',
    buildDate: '2024-01-15T10:00:00Z',
    environment: process.env.NODE_ENV || 'development',
    features: [
      'RESTful API设计',
      '微信小程序兼容',
      '多租户支持',
      '权限控制',
      '标准化响应格式'
    ],
    compatibility: {
      miniProgram: '>=2.0.0',
      node: '>=14.0.0'
    }
  });
});

// ====================================================================
// 认证相关 API - 无需认证
// ====================================================================

/**
 * @api {post} /api/v1/auth/login 用户登录
 */
router.post('/auth/login', authController.login);

/**
 * @api {post} /api/v1/auth/logout 用户登出
 */
router.post('/auth/logout', authController.logout);

/**
 * @api {post} /api/v1/auth/refresh 刷新Token
 */
router.post('/auth/refresh', authController.refreshToken);

/**
 * @api {post} /api/v1/auth/register 用户注册
 */
router.post('/auth/register', authController.register);

/**
 * @api {post} /api/v1/auth/wechat 微信登录
 */
router.post('/auth/wechat', authController.wechatLogin);

/**
 * @api {post} /api/v1/auth/verify-code 发送验证码
 */
router.post('/auth/verify-code', authController.sendVerifyCode);

/**
 * @api {post} /api/v1/auth/reset-password 重置密码
 */
router.post('/auth/reset-password', authController.resetPassword);

// ====================================================================
// 以下所有路由需要认证
// ====================================================================
router.use(verifyToken);

// 用户信息相关
/**
 * @api {get} /api/v1/auth/profile 获取用户信息
 */
router.get('/auth/profile', authController.getProfile);

/**
 * @api {put} /api/v1/auth/profile 更新用户信息
 */
router.put('/auth/profile', authController.updateProfile);

/**
 * @api {put} /api/v1/auth/password 修改密码
 */
router.put('/auth/password', authController.changePassword);

// ====================================================================
// 用户管理 API
// ====================================================================

/**
 * @api {get} /api/v1/users 获取用户列表
 */
router.get('/users', 
  validatePermission('system:user:manage'),
  userController.getUsers
);

/**
 * @api {get} /api/v1/users/:id 获取用户详情
 */
router.get('/users/:id',
  validatePermission('system:user:manage'),
  userController.getUserDetail
);

/**
 * @api {post} /api/v1/users 创建用户
 */
router.post('/users',
  validatePermission('system:user:manage'),
  userController.createUser
);

/**
 * @api {put} /api/v1/users/:id 更新用户信息
 */
router.put('/users/:id',
  validatePermission('system:user:manage'),
  userController.updateUser
);

/**
 * @api {delete} /api/v1/users/:id 删除用户
 */
router.delete('/users/:id',
  validatePermission('system:user:manage'),
  userController.deleteUser
);

/**
 * @api {get} /api/v1/users/:id/permissions 获取用户权限
 */
router.get('/users/:id/permissions',
  validatePermission('system:user:manage'),
  userController.getUserPermissions
);

/**
 * @api {get} /api/v1/users/:id/roles 获取用户角色
 */
router.get('/users/:id/roles',
  validatePermission('system:user:manage'),
  userController.getUserRoles
);

/**
 * @api {put} /api/v1/users/:id/roles 更新用户角色
 */
router.put('/users/:id/roles',
  validatePermission('system:user:manage'),
  userController.updateUserRoles
);

// ====================================================================
// 鹅群管理 API
// ====================================================================

/**
 * @api {get} /api/v1/flocks 获取鹅群列表
 */
router.get('/flocks',
  validatePermission('production:view'),
  flockController.getFlocks
);

/**
 * @api {post} /api/v1/flocks 创建鹅群
 */
router.post('/flocks',
  validatePermission('production:manage'),
  flockController.createFlock
);

/**
 * @api {get} /api/v1/flocks/:id 获取鹅群详情
 */
router.get('/flocks/:id',
  validatePermission('production:view'),
  flockController.getFlockDetail
);

/**
 * @api {put} /api/v1/flocks/:id 更新鹅群信息
 */
router.put('/flocks/:id',
  validatePermission('production:manage'),
  flockController.updateFlock
);

/**
 * @api {delete} /api/v1/flocks/:id 删除鹅群
 */
router.delete('/flocks/:id',
  validatePermission('production:manage'),
  flockController.deleteFlock
);

/**
 * @api {get} /api/v1/flocks/:id/statistics 获取鹅群统计
 */
router.get('/flocks/:id/statistics',
  validatePermission('production:view'),
  flockController.getFlockStatistics
);

/**
 * @api {post} /api/v1/flocks/batch 批量操作鹅群
 */
router.post('/flocks/batch',
  validatePermission('production:manage'),
  flockController.batchOperations
);

// ====================================================================
// 健康管理 API
// ====================================================================

/**
 * @api {get} /api/v1/health/records 获取健康记录列表
 */
router.get('/health/records',
  validatePermission('production:view'),
  healthController.getHealthRecords
);

/**
 * @api {post} /api/v1/health/records 创建健康记录
 */
router.post('/health/records',
  validatePermission('production:manage'),
  healthController.createHealthRecord
);

/**
 * @api {get} /api/v1/health/records/:id 获取健康记录详情
 */
router.get('/health/records/:id',
  validatePermission('production:view'),
  healthController.getHealthRecordDetail
);

/**
 * @api {put} /api/v1/health/records/:id 更新健康记录
 */
router.put('/health/records/:id',
  validatePermission('production:manage'),
  healthController.updateHealthRecord
);

/**
 * @api {delete} /api/v1/health/records/:id 删除健康记录
 */
router.delete('/health/records/:id',
  validatePermission('production:manage'),
  healthController.deleteHealthRecord
);

/**
 * @api {get} /api/v1/health/statistics 获取健康统计
 */
router.get('/health/statistics',
  validatePermission('production:view'),
  healthController.getHealthStatistics
);

/**
 * @api {get} /api/v1/health/knowledge 获取健康知识库
 */
router.get('/health/knowledge',
  validatePermission('production:view'),
  healthController.getHealthKnowledge
);

/**
 * @api {post} /api/v1/health/ai-diagnosis AI健康诊断
 */
router.post('/health/ai-diagnosis',
  validatePermission('production:view'),
  healthController.aiDiagnosis
);

/**
 * @api {get} /api/v1/health/reports 获取健康报告
 */
router.get('/health/reports',
  validatePermission('production:view'),
  healthController.getHealthReports
);

// ====================================================================
// 生产管理 API
// ====================================================================

/**
 * @api {get} /api/v1/production/records 获取生产记录列表
 */
router.get('/production/records',
  validatePermission('production:view'),
  productionController.getProductionRecords
);

/**
 * @api {post} /api/v1/production/records 创建生产记录
 */
router.post('/production/records',
  validatePermission('production:manage'),
  productionController.createProductionRecord
);

/**
 * @api {get} /api/v1/production/records/:id 获取生产记录详情
 */
router.get('/production/records/:id',
  validatePermission('production:view'),
  productionController.getProductionRecordDetail
);

/**
 * @api {put} /api/v1/production/records/:id 更新生产记录
 */
router.put('/production/records/:id',
  validatePermission('production:manage'),
  productionController.updateProductionRecord
);

/**
 * @api {delete} /api/v1/production/records/:id 删除生产记录
 */
router.delete('/production/records/:id',
  validatePermission('production:manage'),
  productionController.deleteProductionRecord
);

/**
 * @api {get} /api/v1/production/statistics 获取生产统计
 */
router.get('/production/statistics',
  validatePermission('production:view'),
  productionController.getProductionStatistics
);

/**
 * @api {get} /api/v1/production/trends 获取生产趋势
 */
router.get('/production/trends',
  validatePermission('production:view'),
  productionController.getProductionTrends
);

/**
 * @api {get} /api/v1/production/environment 获取环境数据
 */
router.get('/production/environment',
  validatePermission('production:view'),
  productionController.getEnvironmentData
);

/**
 * @api {get} /api/v1/production/finance 获取生产财务数据
 */
router.get('/production/finance',
  validatePermission('production:view'),
  productionController.getProductionFinance
);

// ====================================================================
// 库存管理 API
// ====================================================================

/**
 * @api {get} /api/v1/inventory/items 获取库存物品列表
 */
router.get('/inventory/items',
  validatePermission('production:inventory:manage'),
  inventoryController.getInventoryItems
);

/**
 * @api {post} /api/v1/inventory/items 创建库存物品
 */
router.post('/inventory/items',
  validatePermission('production:inventory:manage'),
  inventoryController.createInventoryItem
);

/**
 * @api {get} /api/v1/inventory/items/:id 获取库存物品详情
 */
router.get('/inventory/items/:id',
  validatePermission('production:inventory:manage'),
  inventoryController.getInventoryItemDetail
);

/**
 * @api {put} /api/v1/inventory/items/:id 更新库存物品
 */
router.put('/inventory/items/:id',
  validatePermission('production:inventory:manage'),
  inventoryController.updateInventoryItem
);

/**
 * @api {delete} /api/v1/inventory/items/:id 删除库存物品
 */
router.delete('/inventory/items/:id',
  validatePermission('production:inventory:manage'),
  inventoryController.deleteInventoryItem
);

/**
 * @api {get} /api/v1/inventory/categories 获取库存分类
 */
router.get('/inventory/categories',
  validatePermission('production:view'),
  inventoryController.getInventoryCategories
);

/**
 * @api {get} /api/v1/inventory/low-stock 获取低库存预警
 */
router.get('/inventory/low-stock',
  validatePermission('production:view'),
  inventoryController.getLowStockItems
);

/**
 * @api {post} /api/v1/inventory/batch 批量更新库存
 */
router.post('/inventory/batch',
  validatePermission('production:inventory:manage'),
  inventoryController.batchUpdateInventory
);

// ====================================================================
// 工作台系统 API
// ====================================================================

// 导入工作台控制器
const workspaceController = require('../controllers/workspace');

// 工作台主页
/**
 * @api {get} /api/v1/workspace/dashboard 获取工作台首页数据
 */
router.get('/workspace/dashboard',
  verifyToken,
  validatePermission('workspace_access'),
  workspaceController.getDashboard
);

/**
 * @api {get} /api/v1/workspace/statistics 获取财务统计数据
 */
router.get('/workspace/statistics',
  verifyToken,
  validatePermission('finance_view'),
  workspaceController.getFinanceStatistics
);

/**
 * @api {get} /api/v1/workspace/pending-approvals 获取待审批事项
 */
router.get('/workspace/pending-approvals',
  verifyToken,
  validatePermission('approval_view'),
  workspaceController.getPendingApprovals
);

// 申请管理
/**
 * @api {post} /api/v1/workspace/applications 创建财务申请
 */
router.post('/workspace/applications',
  verifyToken,
  validatePermission('application_create'),
  workspaceController.createApplication
);

/**
 * @api {get} /api/v1/workspace/applications 获取申请列表
 */
router.get('/workspace/applications',
  verifyToken,
  validatePermission('application_view'),
  workspaceController.getApplications
);

/**
 * @api {get} /api/v1/workspace/applications/:id 获取申请详情
 */
router.get('/workspace/applications/:id',
  verifyToken,
  validatePermission('application_view'),
  workspaceController.getApplicationDetail
);

/**
 * @api {put} /api/v1/workspace/applications/:id 更新申请
 */
router.put('/workspace/applications/:id',
  verifyToken,
  validatePermission('application_edit'),
  workspaceController.updateApplication
);

/**
 * @api {post} /api/v1/workspace/applications/:id/cancel 取消申请
 */
router.post('/workspace/applications/:id/cancel',
  verifyToken,
  validatePermission('application_cancel'),
  workspaceController.cancelApplication
);

// 审批管理
/**
 * @api {post} /api/v1/workspace/applications/:id/approve 审批申请
 */
router.post('/workspace/applications/:id/approve',
  verifyToken,
  validatePermission('approval_process'),
  workspaceController.approveApplication
);

/**
 * @api {post} /api/v1/workspace/approvals/batch 批量审批
 */
router.post('/workspace/approvals/batch',
  verifyToken,
  validatePermission('approval_batch'),
  workspaceController.batchApprove
);

/**
 * @api {get} /api/v1/workspace/applications/:id/approval-history 获取审批历史
 */
router.get('/workspace/applications/:id/approval-history',
  verifyToken,
  validatePermission('approval_view'),
  workspaceController.getApprovalHistory
);

/**
 * @api {get} /api/v1/workspace/my-approvals 获取我的审批任务
 */
router.get('/workspace/my-approvals',
  verifyToken,
  validatePermission('approval_view'),
  workspaceController.getMyApprovalTasks
);

// 数据同步
/**
 * @api {post} /api/v1/workspace/sync/health-data 同步健康模块数据
 */
router.post('/workspace/sync/health-data',
  verifyToken,
  validatePermission('finance_sync'),
  workspaceController.syncHealthData
);

/**
 * @api {get} /api/v1/workspace/sync/statistics 获取同步统计信息
 */
router.get('/workspace/sync/statistics',
  verifyToken,
  validatePermission('finance_view'),
  workspaceController.getSyncStatistics
);

// ====================================================================
// OA办公自动化系统 API (保留兼容性)
// ====================================================================

// 统计概览
/**
 * @api {get} /api/v1/oa/statistics 获取OA统计信息
 */
router.get('/oa/statistics',
  validatePermission('oa_access'),
  oaController.getOAStats
);

/**
 * @api {get} /api/v1/oa/activities 获取OA最近活动
 */
router.get('/oa/activities',
  validatePermission('oa_access'),
  oaController.getRecentActivities
);





// 报销管理
/**
 * @api {get} /api/v1/oa/reimbursement/requests 获取报销申请列表
 */
router.get('/oa/reimbursement/requests',
  validatePermission('reimbursement:read'),
  oaController.getReimbursements
);

/**
 * @api {post} /api/v1/oa/reimbursement/requests 创建报销申请
 */
router.post('/oa/reimbursement/requests',
  validatePermission('reimbursement:create'),
  oaController.createReimbursement
);

/**
 * @api {get} /api/v1/oa/reimbursement/requests/:id 获取报销申请详情
 */
router.get('/oa/reimbursement/requests/:id',
  validatePermission('reimbursement:read'),
  oaController.getReimbursementDetail
);

/**
 * @api {put} /api/v1/oa/reimbursement/requests/:id 更新报销申请
 */
router.put('/oa/reimbursement/requests/:id',
  validatePermission('reimbursement:create'),
  oaController.updateReimbursement
);

/**
 * @api {put} /api/v1/oa/reimbursement/requests/:id/status 提交/撤回报销申请
 */
router.put('/oa/reimbursement/requests/:id/status',
  validatePermission('reimbursement:create'),
  oaController.submitReimbursement
);

/**
 * @api {get} /api/v1/oa/reimbursement/statistics 获取报销统计
 */
router.get('/oa/reimbursement/statistics',
  validatePermission('reimbursement:read'),
  oaController.getReimbursementStatistics
);

// 审批流程
/**
 * @api {get} /api/v1/oa/approvals/pending 获取待审批列表
 */
router.get('/oa/approvals/pending',
  validatePermission('approval:read'),
  oaController.getPendingApprovals
);

/**
 * @api {get} /api/v1/oa/approvals/urgent 获取紧急审批列表
 */
router.get('/oa/approvals/urgent',
  validatePermission('approval:read'),
  oaController.getUrgentApprovals
);

/**
 * @api {get} /api/v1/oa/approvals/history 获取审批历史
 */
router.get('/oa/approvals/history',
  validatePermission('approval:read'),
  oaController.getApprovalHistory
);

/**
 * @api {get} /api/v1/oa/approvals/statistics 获取审批统计
 */
router.get('/oa/approvals/statistics',
  validatePermission('approval:read'),
  oaController.getApprovalStatistics
);

/**
 * @api {put} /api/v1/oa/approvals/:id/process 处理审批 (批准/拒绝)
 */
router.put('/oa/approvals/:id/process',
  validatePermission('approval:process'),
  oaController.processApproval
);

// 财务管理
/**
 * @api {get} /api/v1/oa/finance/overview 获取财务概览
 */
router.get('/oa/finance/overview',
  validatePermission('finance_view'),
  oaController.getFinanceOverview
);

/**
 * @api {get} /api/v1/oa/finance/reports 获取财务报表
 */
router.get('/oa/finance/reports',
  validatePermission('finance_view'),
  oaController.getFinanceReports
);

/**
 * @api {get} /api/v1/oa/finance/statistics 获取财务统计
 */
router.get('/oa/finance/statistics',
  validatePermission('finance_view'),
  oaController.getFinanceStatistics
);

/**
 * @api {get} /api/v1/oa/finance/activities 获取财务活动记录
 */
router.get('/oa/finance/activities',
  validatePermission('finance_view'),
  oaController.getFinanceActivities
);

/**
 * @api {get} /api/v1/oa/finance/alerts 获取财务预警
 */
router.get('/oa/finance/alerts',
  validatePermission('finance_view'),
  oaController.getFinanceAlerts
);

/**
 * @api {post} /api/v1/oa/finance/export 导出财务数据
 */
router.post('/oa/finance/export',
  validatePermission('finance_export'),
  oaController.exportFinanceReport
);

// 工作流配置
/**
 * @api {get} /api/v1/oa/workflows/templates 获取工作流模板列表
 */
router.get('/oa/workflows/templates',
  validatePermission('workflow:manage'),
  oaController.getWorkflowTemplates
);

/**
 * @api {post} /api/v1/oa/workflows/templates 创建工作流模板
 */
router.post('/oa/workflows/templates',
  validatePermission('workflow:manage'),
  oaController.createWorkflowTemplate
);

/**
 * @api {get} /api/v1/oa/workflows/templates/:id 获取工作流模板详情
 */
router.get('/oa/workflows/templates/:id',
  validatePermission('workflow:manage'),
  oaController.getWorkflowTemplateDetail
);

/**
 * @api {put} /api/v1/oa/workflows/templates/:id 更新工作流模板
 */
router.put('/oa/workflows/templates/:id',
  validatePermission('workflow:manage'),
  oaController.updateWorkflowTemplate
);

/**
 * @api {delete} /api/v1/oa/workflows/templates/:id 删除工作流模板
 */
router.delete('/oa/workflows/templates/:id',
  validatePermission('workflow:manage'),
  oaController.deleteWorkflowTemplate
);

/**
 * @api {post} /api/v1/oa/workflows/templates/:id/copy 复制工作流模板
 */
router.post('/oa/workflows/templates/:id/copy',
  validatePermission('workflow:manage'),
  oaController.copyWorkflowTemplate
);

/**
 * @api {get} /api/v1/oa/workflows/statistics 获取工作流统计
 */
router.get('/oa/workflows/statistics',
  validatePermission('workflow:manage'),
  oaController.getWorkflowTemplateStatistics
);

// 权限管理
/**
 * @api {get} /api/v1/oa/permissions/users 获取权限用户列表
 */
router.get('/oa/permissions/users',
  validatePermission('system:user:manage'),
  oaController.getUsersForPermission
);

/**
 * @api {get} /api/v1/oa/permissions/users/:id 获取权限用户详情
 */
router.get('/oa/permissions/users/:id',
  validatePermission('system:user:manage'),
  oaController.getUserPermissionDetail
);

/**
 * @api {get} /api/v1/oa/permissions/users/:id/roles 获取用户角色
 */
router.get('/oa/permissions/users/:id/roles',
  validatePermission('system:user:manage'),
  oaController.getUserRoles
);

/**
 * @api {put} /api/v1/oa/permissions/users/:id/roles 更新用户角色
 */
router.put('/oa/permissions/users/:id/roles',
  validatePermission('system:user:manage'),
  oaController.updateUserRoles
);

/**
 * @api {get} /api/v1/oa/permissions/roles 获取角色列表
 */
router.get('/oa/permissions/roles',
  validatePermission('system:role:manage'),
  oaController.getRoles
);

/**
 * @api {get} /api/v1/oa/permissions/roles/:id 获取角色详情
 */
router.get('/oa/permissions/roles/:id',
  validatePermission('system:role:manage'),
  oaController.getRoleDetail
);

/**
 * @api {get} /api/v1/oa/permissions/roles/:id/permissions 获取角色权限
 */
router.get('/oa/permissions/roles/:id/permissions',
  validatePermission('system:role:manage'),
  oaController.getRolePermissions
);

/**
 * @api {put} /api/v1/oa/permissions/roles/:id/permissions 更新角色权限
 */
router.put('/oa/permissions/roles/:id/permissions',
  validatePermission('system:role:manage'),
  oaController.updateRolePermissions
);

/**
 * @api {get} /api/v1/oa/permissions/departments 获取部门列表
 */
router.get('/oa/permissions/departments',
  validatePermission('system:department:manage'),
  oaController.getDepartments
);

/**
 * @api {get} /api/v1/oa/permissions/statistics 获取权限统计
 */
router.get('/oa/permissions/statistics',
  validatePermission('system:user:manage'),
  oaController.getUserPermissionStatistics
);

// ====================================================================
// 商城系统 API
// ====================================================================

// 商品管理
/**
 * @api {get} /api/v1/shop/products 获取商品列表
 */
router.get('/shop/products',
  validatePermission('shop:view'),
  shopController.getProducts
);

/**
 * @api {get} /api/v1/shop/products/:id 获取商品详情
 */
router.get('/shop/products/:id',
  validatePermission('shop:view'),
  shopController.getProductDetail
);

/**
 * @api {get} /api/v1/shop/products/categories 获取商品分类
 */
router.get('/shop/products/categories',
  validatePermission('shop:view'),
  shopController.getProductCategories
);

// 购物车
/**
 * @api {get} /api/v1/shop/cart 获取购物车
 */
router.get('/shop/cart',
  validatePermission('shop:view'),
  shopController.getCart
);

/**
 * @api {get} /api/v1/shop/cart/items 获取购物车商品列表
 */
router.get('/shop/cart/items',
  validatePermission('shop:view'),
  shopController.getCartItems
);

/**
 * @api {post} /api/v1/shop/cart/items 添加商品到购物车
 */
router.post('/shop/cart/items',
  validatePermission('shop:view'),
  shopController.addToCart
);

/**
 * @api {put} /api/v1/shop/cart/items/:id 更新购物车商品
 */
router.put('/shop/cart/items/:id',
  validatePermission('shop:view'),
  shopController.updateCartItem
);

/**
 * @api {delete} /api/v1/shop/cart/items/:id 删除购物车商品
 */
router.delete('/shop/cart/items/:id',
  validatePermission('shop:view'),
  shopController.removeFromCart
);

// 订单管理
/**
 * @api {get} /api/v1/shop/orders 获取订单列表
 */
router.get('/shop/orders',
  validatePermission('shop:view'),
  shopController.getOrders
);

/**
 * @api {post} /api/v1/shop/orders 创建订单
 */
router.post('/shop/orders',
  validatePermission('shop:view'),
  shopController.createOrder
);

/**
 * @api {get} /api/v1/shop/orders/:id 获取订单详情
 */
router.get('/shop/orders/:id',
  validatePermission('shop:view'),
  shopController.getOrderDetail
);

/**
 * @api {put} /api/v1/shop/orders/:id/status 更新订单状态
 */
router.put('/shop/orders/:id/status',
  validatePermission('shop:order:process'),
  shopController.updateOrderStatus
);

// 支付相关
/**
 * @api {post} /api/v1/shop/payment 创建支付
 */
router.post('/shop/payment',
  validatePermission('shop:view'),
  shopController.createPayment
);

/**
 * @api {post} /api/v1/shop/payment/notify 支付回调通知
 */
router.post('/shop/payment/notify', shopController.paymentNotify);

// ====================================================================
// 系统管理 API
// ====================================================================

// 首页数据
/**
 * @api {get} /api/v1/home/<USER>
 */
router.get('/home/<USER>',
  validatePermission('oa_access'),
  homeController.getHomeStatistics
);

/**
 * @api {get} /api/v1/home/<USER>
 */
router.get('/home/<USER>',
  validatePermission('oa_access'),
  homeController.getAnnouncements
);

/**
 * @api {get} /api/v1/home/<USER>
 */
router.get('/home/<USER>',
  validatePermission('oa_access'),
  homeController.getWeather
);

/**
 * @api {get} /api/v1/home/<USER>
 */
router.get('/home/<USER>',
  validatePermission('oa_access'),
  homeController.getQuickActions
);

// 通知系统
/**
 * @api {get} /api/v1/notifications 获取通知列表
 */
router.get('/notifications',
  validatePermission('oa_access'),
  require('../controllers/notification.controller').getNotifications
);

/**
 * @api {get} /api/v1/notifications/unread-count 获取未读通知数量
 */
router.get('/notifications/unread-count',
  validatePermission('oa_access'),
  require('../controllers/notification.controller').getUnreadCount
);

/**
 * @api {put} /api/v1/notifications/:id/read 标记通知为已读
 */
router.put('/notifications/:id/read',
  validatePermission('oa_access'),
  require('../controllers/notification.controller').markAsRead
);

/**
 * @api {put} /api/v1/notifications/read-all 标记所有通知为已读
 */
router.put('/notifications/read-all',
  validatePermission('oa_access'),
  require('../controllers/notification.controller').markAllAsRead
);

// 帮助中心
/**
 * @api {get} /api/v1/help/articles 获取帮助文章列表
 */
router.get('/help/articles',
  validatePermission('oa_access'),
  helpController.getArticles
);

/**
 * @api {get} /api/v1/help/articles/:id 获取帮助文章详情
 */
router.get('/help/articles/:id',
  validatePermission('oa_access'),
  helpController.getArticleDetail
);

/**
 * @api {get} /api/v1/help/categories 获取帮助分类
 */
router.get('/help/categories',
  validatePermission('oa_access'),
  helpController.getCategories
);

/**
 * @api {get} /api/v1/help/search 搜索帮助内容
 */
router.get('/help/search',
  validatePermission('oa_access'),
  helpController.searchHelp
);

/**
 * @api {get} /api/v1/help/faq 获取常见问题
 */
router.get('/help/faq',
  validatePermission('oa_access'),
  helpController.getFAQ
);

// 系统设置
/**
 * @api {get} /api/v1/settings/system 获取系统设置
 */
router.get('/settings/system',
  validatePermission('system:config:view'),
  require('../controllers/settings.controller').getSystemSettings
);

/**
 * @api {get} /api/v1/settings/version 获取版本信息
 */
router.get('/settings/version',
  validatePermission('oa_access'),
  require('../controllers/settings.controller').getVersion
);

/**
 * @api {get} /api/v1/settings/config 获取配置信息
 */
router.get('/settings/config',
  validatePermission('oa_access'),
  require('../controllers/settings.controller').getConfig
);

// 文件上传
/**
 * @api {post} /api/v1/upload/image 上传图片
 */
router.post('/upload/image',
  validatePermission('oa_access'),
  require('../controllers/upload.controller').uploadImage
);

/**
 * @api {post} /api/v1/upload/file 上传文件
 */
router.post('/upload/file',
  validatePermission('oa_access'),
  require('../controllers/upload.controller').uploadFile
);

/**
 * @api {post} /api/v1/upload/batch 批量上传
 */
router.post('/upload/batch',
  validatePermission('oa_access'),
  require('../controllers/upload.controller').batchUpload
);

// ====================================================================
// 租户管理 API (多租户SAAS)
// ====================================================================

/**
 * @api {get} /api/v1/tenants 获取租户列表
 */
router.get('/tenants',
  validatePermission('platform:tenant:manage'),
  require('../controllers/tenant.controller').getTenants
);

/**
 * @api {post} /api/v1/tenants 创建租户
 */
router.post('/tenants',
  validatePermission('platform:tenant:manage'),
  require('../controllers/tenant.controller').createTenant
);

/**
 * @api {get} /api/v1/tenants/:id 获取租户详情
 */
router.get('/tenants/:id',
  validatePermission('platform:tenant:manage'),
  require('../controllers/tenant.controller').getTenantDetail
);

/**
 * @api {put} /api/v1/tenants/:id 更新租户信息
 */
router.put('/tenants/:id',
  validatePermission('platform:tenant:manage'),
  require('../controllers/tenant.controller').updateTenant
);

/**
 * @api {delete} /api/v1/tenants/:id 删除租户
 */
router.delete('/tenants/:id',
  validatePermission('platform:tenant:manage'),
  require('../controllers/tenant.controller').deleteTenant
);

/**
 * @api {put} /api/v1/tenants/:id/activate 激活租户
 */
router.put('/tenants/:id/activate',
  validatePermission('platform:tenant:manage'),
  require('../controllers/tenant.controller').activateTenant
);

/**
 * @api {put} /api/v1/tenants/:id/suspend 暂停租户
 */
router.put('/tenants/:id/suspend',
  validatePermission('platform:tenant:manage'),
  require('../controllers/tenant.controller').suspendTenant
);

/**
 * @api {get} /api/v1/tenants/statistics 获取租户统计
 */
router.get('/tenants/statistics',
  validatePermission('platform:analytics'),
  require('../controllers/tenant.controller').getTenantStatistics
);

/**
 * @api {get} /api/v1/tenants/:id/data 获取租户数据
 */
router.get('/tenants/:id/data',
  validatePermission('platform:tenant:manage'),
  require('../controllers/tenant.controller').getTenantData
);

/**
 * @api {get} /api/v1/tenants/:id/business 获取租户业务数据
 */
router.get('/tenants/:id/business',
  validatePermission('platform:tenant:manage'),
  require('../controllers/tenant.controller').getTenantBusiness
);

/**
 * @api {get} /api/v1/tenants/:id/users 获取租户用户
 */
router.get('/tenants/:id/users',
  validatePermission('platform:tenant:manage'),
  require('../controllers/tenant.controller').getTenantUsers
);

// ====================================================================
// 平台管理 API (超级管理员)
// ====================================================================

/**
 * @api {get} /api/v1/admin/statistics 获取平台统计
 */
router.get('/admin/statistics',
  validatePermission('platform:analytics'),
  require('../controllers/admin.controller').getPlatformStatistics
);

/**
 * @api {get} /api/v1/admin/analytics 获取平台分析数据
 */
router.get('/admin/analytics',
  validatePermission('platform:analytics'),
  require('../controllers/admin.controller').getPlatformAnalytics
);

/**
 * @api {get} /api/v1/admin/revenue 获取收入分析
 */
router.get('/admin/revenue',
  validatePermission('platform:analytics'),
  require('../controllers/admin.controller').getRevenueAnalytics
);

/**
 * @api {get} /api/v1/admin/usage 获取使用情况分析
 */
router.get('/admin/usage',
  validatePermission('platform:analytics'),
  require('../controllers/admin.controller').getUsageAnalytics
);

/**
 * @api {get} /api/v1/admin/health 获取系统健康状态
 */
router.get('/admin/health',
  validatePermission('platform:analytics'),
  require('../controllers/admin.controller').getSystemHealth
);

/**
 * @api {get} /api/v1/admin/logs 获取系统日志
 */
router.get('/admin/logs',
  validatePermission('platform:analytics'),
  require('../controllers/admin.controller').getSystemLogs
);

/**
 * @api {get} /api/v1/admin/errors 获取错误日志
 */
router.get('/admin/errors',
  validatePermission('platform:analytics'),
  require('../controllers/admin.controller').getErrorLogs
);

/**
 * @api {get} /api/v1/admin/subscriptions 获取订阅管理
 */
router.get('/admin/subscriptions',
  validatePermission('platform:analytics'),
  require('../controllers/admin.controller').getSubscriptions
);

/**
 * @api {get} /api/v1/admin/plans 获取订阅计划
 */
router.get('/admin/plans',
  validatePermission('platform:analytics'),
  require('../controllers/admin.controller').getSubscriptionPlans
);

/**
 * @api {get} /api/v1/admin/billing 获取计费信息
 */
router.get('/admin/billing',
  validatePermission('platform:analytics'),
  require('../controllers/admin.controller').getBillingInfo
);

// ====================================================================
// 错误处理 - 符合微信小程序错误码规范
// ====================================================================

// 微信小程序标准错误码
const ERROR_CODES = {
  SUCCESS: 0,
  SYSTEM_ERROR: -1,
  INVALID_PARAM: 40001,
  ACCESS_TOKEN_INVALID: 40014,
  USER_NOT_EXIST: 40003,
  PERMISSION_DENIED: 48001,
  API_NOT_FOUND: 40013,
  METHOD_NOT_ALLOWED: 40004,
  RATE_LIMIT_EXCEEDED: 45009,
  TENANT_NOT_FOUND: 50001,
  AUTHENTICATION_FAILED: 50002,
  INSUFFICIENT_PERMISSIONS: 50003
};

// 404处理
router.use('*', (req, res) => {
  res.status(404).error(ERROR_CODES.API_NOT_FOUND, 'API接口不存在', {
    path: req.originalUrl,
    method: req.method,
    availableVersions: ['v1']
  });
});

// 统一错误处理
router.use((error, req, res, next) => {
  console.error('[API Error]', {
    error: error.message,
    stack: error.stack,
    path: req.originalUrl,
    method: req.method,
    timestamp: new Date().toISOString()
  });
  
  // JWT错误
  if (error.name === 'JsonWebTokenError') {
    return res.status(401).error(ERROR_CODES.ACCESS_TOKEN_INVALID, 'Token无效或已过期');
  }
  
  // 权限错误
  if (error.code === 'INSUFFICIENT_PERMISSIONS') {
    return res.status(403).error(ERROR_CODES.INSUFFICIENT_PERMISSIONS, '权限不足', {
      required: error.required
    });
  }
  
  // 租户错误
  if (error.code === 'TENANT_ACCESS_DENIED') {
    return res.status(403).error(ERROR_CODES.TENANT_NOT_FOUND, '租户访问被拒绝');
  }
  
  // 参数错误
  if (error.name === 'ValidationError') {
    return res.status(400).error(ERROR_CODES.INVALID_PARAM, '请求参数错误', {
      details: error.details
    });
  }
  
  // 通用错误
  res.status(500).error(ERROR_CODES.SYSTEM_ERROR, '服务器内部错误', {
    requestId: req.headers['x-request-id'],
    ...(process.env.NODE_ENV === 'development' && { error: error.message })
  });
});

module.exports = router;