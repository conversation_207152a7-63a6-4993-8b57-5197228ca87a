/**
 * 财务管理路由
 * 处理财务概览、统计和报表相关的路由
 */

const express = require('express');
const router = express.Router();

// 导入中间件
const { authenticateToken, requireFinanceAccess, requireAnyRole, logPermissionAction } = require('../../middleware/rbac');

// 导入控制器
const financeController = require('../../controllers/workspace/finance.controller');

// 应用认证中间件到所有路由
router.use(authenticateToken);
router.use(logPermissionAction);

/**
 * 财务概览统计数据
 * GET /api/workspace/finance/statistics
 * 权限：财务人员、经理、管理员
 */
router.get('/statistics', 
  requireFinanceAccess,
  financeController.getFinanceStatistics
);

/**
 * 个人财务摘要
 * GET /api/workspace/finance/personal-summary
 * 权限：普通员工（只能查看自己的数据）
 */
router.get('/personal-summary', 
  financeController.getPersonalSummary
);

/**
 * 最近财务记录
 * GET /api/workspace/finance/recent-records
 * 权限：财务人员、经理、管理员
 */
router.get('/recent-records', 
  requireFinanceAccess,
  financeController.getRecentRecords
);

/**
 * 月度趋势数据
 * GET /api/workspace/finance/monthly-trend
 * 权限：财务人员、经理、管理员
 */
router.get('/monthly-trend', 
  requireFinanceAccess,
  financeController.getMonthlyTrend
);

/**
 * 业务类型统计
 * GET /api/workspace/finance/business-type-stats
 * 权限：财务人员、经理、管理员
 */
router.get('/business-type-stats', 
  requireFinanceAccess,
  financeController.getBusinessTypeStats
);

module.exports = router;
