// backend/routes/workspace/purchase.routes.js
const express = require('express');
const router = express.Router();
const purchaseController = require('../../controllers/workspace/purchase.controller.js');

// 采购申请路由
router.get('/requests', purchaseController.getPurchaseList);
router.get('/requests/:id', purchaseController.getPurchaseDetail);
router.post('/requests', purchaseController.createPurchase);
router.put('/requests/:id', purchaseController.updatePurchase);
router.delete('/requests/:id', purchaseController.deletePurchase);

// 采购统计
router.get('/statistics', purchaseController.getPurchaseStatistics);

// 供应商管理
router.get('/suppliers', purchaseController.getSuppliers);

module.exports = router;
