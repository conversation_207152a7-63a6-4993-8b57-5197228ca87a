/**
 * 费用报销申请路由
 * 处理费用报销相关的特定操作
 */

const express = require('express');
const router = express.Router();

// 导入中间件
const { 
  authenticateToken, 
  requireAnyRole,
  filterDataAccess,
  checkOperationPermission,
  logPermissionAction,
  ROLES
} = require('../../middleware/rbac');

// 导入控制器
const expenseController = require('../../controllers/workspace/expense.controller');

// 应用认证中间件到所有路由
router.use(authenticateToken);
router.use(logPermissionAction);

/**
 * 获取费用报销记录列表
 * GET /api/workspace/applications/expense/records
 * 权限：所有用户（根据角色过滤数据）
 */
router.get('/records', 
  filterDataAccess,
  expenseController.getExpenseRecords
);

/**
 * 创建费用报销申请
 * POST /api/workspace/applications/expense
 * 权限：所有用户
 */
router.post('/', 
  checkOperationPermission('create', 'own'),
  expenseController.createExpenseApplication
);

/**
 * 获取费用报销详情
 * GET /api/workspace/applications/expense/:id
 * 权限：申请人本人、财务人员、经理、管理员
 */
router.get('/:id', 
  checkOperationPermission('view', 'own'),
  expenseController.getExpenseById
);

/**
 * 更新费用报销申请
 * PUT /api/workspace/applications/expense/:id
 * 权限：申请人本人（草稿/被拒绝状态）、经理、管理员
 */
router.put('/:id', 
  checkOperationPermission('update', 'own'),
  expenseController.updateExpenseApplication
);

/**
 * 删除费用报销申请
 * DELETE /api/workspace/applications/expense/:id
 * 权限：申请人本人（草稿状态）、管理员
 */
router.delete('/:id', 
  checkOperationPermission('delete', 'own'),
  expenseController.deleteExpenseApplication
);

/**
 * 审批费用报销申请
 * POST /api/workspace/applications/expense/:id/approve
 * 权限：财务人员、经理、管理员
 */
router.post('/:id/approve', 
  requireAnyRole([ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN]),
  checkOperationPermission('approve', 'finance_related'),
  expenseController.approveExpenseApplication
);

/**
 * 拒绝费用报销申请
 * POST /api/workspace/applications/expense/:id/reject
 * 权限：财务人员、经理、管理员
 */
router.post('/:id/reject', 
  requireAnyRole([ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN]),
  checkOperationPermission('reject', 'finance_related'),
  expenseController.rejectExpenseApplication
);

/**
 * 撤回费用报销申请
 * POST /api/workspace/applications/expense/:id/withdraw
 * 权限：申请人本人（待审批状态）
 */
router.post('/:id/withdraw', 
  checkOperationPermission('update', 'own'),
  expenseController.withdrawExpenseApplication
);

/**
 * 获取费用报销统计数据
 * GET /api/workspace/applications/expense/statistics
 * 权限：财务人员、经理、管理员
 */
router.get('/statistics', 
  requireAnyRole([ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN]),
  expenseController.getExpenseStatistics
);

/**
 * 导出费用报销数据
 * GET /api/workspace/applications/expense/export
 * 权限：财务人员、经理、管理员
 */
router.get('/export', 
  requireAnyRole([ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN]),
  expenseController.exportExpenseData
);

module.exports = router;
