/**
 * 申请管理路由
 * 处理各种类型申请的CRUD操作，包含RBAC权限控制
 */

const express = require('express');
const router = express.Router();

// 导入中间件
const { 
  authenticateToken, 
  requireRole,
  requireAnyRole,
  filterDataAccess,
  checkApplicationTypeAccess,
  checkOperationPermission,
  logPermissionAction,
  ROLES
} = require('../../middleware/rbac');

// 导入控制器
const applicationController = require('../../controllers/workspace/application.controller');

// 应用认证中间件到所有路由
router.use(authenticateToken);
router.use(logPermissionAction);

/**
 * 获取申请列表
 * GET /api/workspace/applications
 * 权限：所有用户（根据角色过滤数据）
 */
router.get('/', 
  filterDataAccess,
  checkApplicationTypeAccess,
  applicationController.getApplications
);

/**
 * 获取指定类型的申请列表
 * GET /api/workspace/applications/:type
 * 权限：根据申请类型和用户角色控制
 */
router.get('/:type', 
  checkApplicationTypeAccess,
  filterDataAccess,
  applicationController.getApplicationsByType
);

/**
 * 创建新申请
 * POST /api/workspace/applications
 * 权限：所有用户都可以创建申请
 */
router.post('/', 
  checkApplicationTypeAccess,
  checkOperationPermission('create', 'own'),
  applicationController.createApplication
);

/**
 * 获取申请详情
 * GET /api/workspace/applications/:type/:id
 * 权限：申请人本人、财务人员（财务相关）、经理、管理员
 */
router.get('/:type/:id', 
  checkApplicationTypeAccess,
  checkOperationPermission('view', 'own'),
  applicationController.getApplicationById
);

/**
 * 更新申请
 * PUT /api/workspace/applications/:type/:id
 * 权限：申请人本人（草稿/被拒绝状态）、经理、管理员
 */
router.put('/:type/:id', 
  checkApplicationTypeAccess,
  checkOperationPermission('update', 'own'),
  applicationController.updateApplication
);

/**
 * 删除申请
 * DELETE /api/workspace/applications/:type/:id
 * 权限：申请人本人（草稿状态）、管理员
 */
router.delete('/:type/:id', 
  checkApplicationTypeAccess,
  checkOperationPermission('delete', 'own'),
  applicationController.deleteApplication
);

/**
 * 审批申请
 * POST /api/workspace/applications/:type/:id/approve
 * 权限：财务人员（财务相关）、经理、管理员
 */
router.post('/:type/:id/approve', 
  checkApplicationTypeAccess,
  checkOperationPermission('approve', 'finance_related'),
  applicationController.approveApplication
);

/**
 * 拒绝申请
 * POST /api/workspace/applications/:type/:id/reject
 * 权限：财务人员（财务相关）、经理、管理员
 */
router.post('/:type/:id/reject', 
  checkApplicationTypeAccess,
  checkOperationPermission('reject', 'finance_related'),
  applicationController.rejectApplication
);

/**
 * 撤回申请
 * POST /api/workspace/applications/:type/:id/withdraw
 * 权限：申请人本人（待审批状态）
 */
router.post('/:type/:id/withdraw', 
  checkApplicationTypeAccess,
  checkOperationPermission('update', 'own'),
  applicationController.withdrawApplication
);

// 费用报销相关路由
router.use('/expense', require('./expense.routes'));

// 付款申请相关路由
router.use('/payment', require('./payment.routes'));

// 合同申请相关路由
router.use('/contract', require('./contract.routes'));

// 活动经费相关路由
router.use('/activity', require('./activity.routes'));

// 备用金相关路由
router.use('/reserve', require('./reserve.routes'));

// 采购申请相关路由
router.use('/purchase', require('./purchase.routes'));

module.exports = router;
