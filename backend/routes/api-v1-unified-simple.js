/**
 * 智慧养鹅SAAS平台 - 统一API路由 (V1) - 简化版
 * 基于微信小程序RESTful设计规范
 * 
 * 设计原则：
 * 1. 统一使用 /api/v1 版本
 * 2. 资源导向的RESTful设计
 * 3. 标准化权限控制
 * 4. 微信小程序兼容的响应格式
 * 5. 统一错误处理
 */

const express = require('express');
const router = express.Router();

// 导入中间件
const { verifyToken } = require('../middleware/auth.middleware');
const { generateSuccessResponse, generateErrorResponse } = require('../utils/response-helper');

// 导入核心控制器
const authController = require('../controllers/auth.controller');
const userController = require('../controllers/user.controller');
const healthController = require('../controllers/health.controller');
const homeController = require('../controllers/home.controller');

/**
 * 标准化响应中间件 - 符合微信小程序API规范
 */
const standardizeResponse = (req, res, next) => {
  const originalJson = res.json.bind(res);
  
  res.json = (data) => {
    // 如果已经是标准格式，直接返回
    if (data && typeof data === 'object' && ('errcode' in data || 'success' in data)) {
      return originalJson(data);
    }
    
    // 微信小程序标准响应格式
    const response = {
      errcode: 0,
      errmsg: "ok",
      data: data,
      timestamp: Date.now(),
      requestId: req.headers['x-request-id'] || `req_${Date.now()}`
    };
    
    return originalJson(response);
  };
  
  // 添加错误响应方法
  res.error = (errcode, errmsg, data = null) => {
    const response = {
      errcode: errcode || -1,
      errmsg: errmsg || "未知错误",
      data: data,
      timestamp: Date.now(),
      requestId: req.headers['x-request-id'] || `req_${Date.now()}`
    };
    
    return originalJson(response);
  };
  
  next();
};

/**
 * 简化的租户识别中间件
 */
const identifyTenant = (req, res, next) => {
  try {
    const tenantId = req.headers['x-tenant-id'] || 
                   req.headers['x-tenant-code'] || 
                   req.query.tenant_id || 
                   'default';
    
    req.tenantId = tenantId;
    req.tenantContext = {
      id: tenantId,
      timestamp: Date.now()
    };
    
    next();
  } catch (error) {
    res.status(400).error(40001, '租户识别失败');
  }
};

// 应用全局中间件
router.use(standardizeResponse);
router.use(identifyTenant);

// ====================================================================
// API信息端点 - 无需认证
// ====================================================================

/**
 * @api {get} /api/v1 API版本信息
 */
router.get('/v1', (req, res) => {
  res.json({
    name: '智慧养鹅SAAS平台API',
    version: 'v1.0',
    description: '基于RESTful设计的微信小程序API服务',
    timestamp: Date.now(),
    endpoints: {
      auth: '/api/v1/auth',
      users: '/api/v1/users', 
      health: '/api/v1/health',
      home: '/api/v1/home'
    }
  });
});

/**
 * @api {get} /api/v1/version API版本详情
 */
router.get('/v1/version', (req, res) => {
  res.json({
    version: '1.0.0',
    buildNumber: '20240122001',
    buildDate: '2024-01-22T10:00:00Z',
    environment: process.env.NODE_ENV || 'development',
    features: [
      'RESTful API设计',
      '微信小程序兼容',
      '多租户支持',
      '标准化响应格式'
    ]
  });
});

// ====================================================================
// 认证相关 API - 无需认证
// ====================================================================

/**
 * @api {post} /api/v1/auth/login 用户登录
 */
router.post('/v1/auth/login', authController.login);

/**
 * @api {post} /api/v1/auth/register 用户注册
 */
router.post('/v1/auth/register', authController.register);

/**
 * @api {post} /api/v1/auth/wechat 微信登录
 */
router.post('/v1/auth/wechat', authController.wechatLogin);

/**
 * @api {post} /api/v1/auth/verify-code 发送验证码
 */
router.post('/v1/auth/verify-code', authController.sendVerifyCode);

// ====================================================================
// 以下所有路由需要认证
// ====================================================================
router.use(verifyToken);

/**
 * @api {get} /api/v1/auth/profile 获取用户信息
 */
router.get('/v1/auth/profile', authController.getProfile);

/**
 * @api {put} /api/v1/auth/profile 更新用户信息
 */
router.put('/v1/auth/profile', authController.updateProfile);

// ====================================================================
// 用户管理 API
// ====================================================================

/**
 * @api {get} /api/v1/users 获取用户列表
 */
router.get('/v1/users', userController.getUsers);

/**
 * @api {get} /api/v1/users/:id 获取用户详情
 */
router.get('/v1/users/:id', userController.getUserDetail);

// ====================================================================
// 健康管理 API
// ====================================================================

/**
 * @api {get} /api/v1/health/records 获取健康记录列表
 */
router.get('/v1/health/records', healthController.getHealthRecords);

/**
 * @api {post} /api/v1/health/records 创建健康记录
 */
router.post('/v1/health/records', healthController.createHealthRecord);

/**
 * @api {get} /api/v1/health/statistics 获取健康统计
 */
router.get('/v1/health/statistics', healthController.getHealthStatistics);

// ====================================================================
// 首页数据 API
// ====================================================================

/**
 * @api {get} /api/v1/home/<USER>
 */
router.get('/v1/home/<USER>', homeController.getHomeStatistics);

/**
 * @api {get} /api/v1/home/<USER>
 */
router.get('/v1/home/<USER>', homeController.getWeather);

// ====================================================================
// 工作台 API - 财务管理模块
// ====================================================================

// 导入工作台控制器
const workspaceController = require('../controllers/workspace/workspace.controller');
const purchaseController = require('../controllers/workspace/purchase.controller');

/**
 * @api {get} /api/v1/oa/workspace/dashboard 获取工作台数据
 */
router.get('/v1/oa/workspace/dashboard', workspaceController.getDashboard);

/**
 * @api {get} /api/v1/oa/purchase/requests 获取采购申请列表
 */
router.get('/v1/oa/purchase/requests', purchaseController.getPurchaseList);

/**
 * @api {get} /api/v1/oa/purchase/requests/:id 获取采购申请详情
 */
router.get('/v1/oa/purchase/requests/:id', purchaseController.getPurchaseDetail);

/**
 * @api {post} /api/v1/oa/purchase/requests 创建采购申请
 */
router.post('/v1/oa/purchase/requests', purchaseController.createPurchase);

/**
 * @api {put} /api/v1/oa/purchase/requests/:id 更新采购申请
 */
router.put('/v1/oa/purchase/requests/:id', purchaseController.updatePurchase);

/**
 * @api {delete} /api/v1/oa/purchase/requests/:id 删除采购申请
 */
router.delete('/v1/oa/purchase/requests/:id', purchaseController.deletePurchase);

/**
 * @api {get} /api/v1/oa/purchase/statistics 获取采购统计
 */
router.get('/v1/oa/purchase/statistics', purchaseController.getPurchaseStatistics);

/**
 * @api {get} /api/v1/oa/purchase/suppliers 获取供应商列表
 */
router.get('/v1/oa/purchase/suppliers', purchaseController.getSuppliers);

// ====================================================================
// 错误处理 - 符合微信小程序错误码规范
// ====================================================================

// 微信小程序标准错误码
const ERROR_CODES = {
  SUCCESS: 0,
  SYSTEM_ERROR: -1,
  INVALID_PARAM: 40001,
  ACCESS_TOKEN_INVALID: 40014,
  API_NOT_FOUND: 40013,
  AUTHENTICATION_FAILED: 50002,
  INSUFFICIENT_PERMISSIONS: 50003
};

// 404处理
router.use('*', (req, res) => {
  res.status(404).error(ERROR_CODES.API_NOT_FOUND, 'API接口不存在', {
    path: req.originalUrl,
    method: req.method,
    availableVersions: ['v1']
  });
});

// 统一错误处理
router.use((error, req, res, next) => {
  console.error('[API Error]', {
    error: error.message,
    path: req.originalUrl,
    method: req.method,
    timestamp: new Date().toISOString()
  });
  
  // JWT错误
  if (error.name === 'JsonWebTokenError') {
    return res.status(401).error(ERROR_CODES.ACCESS_TOKEN_INVALID, 'Token无效或已过期');
  }
  
  // 参数错误
  if (error.name === 'ValidationError') {
    return res.status(400).error(ERROR_CODES.INVALID_PARAM, '请求参数错误');
  }
  
  // 通用错误
  res.status(500).error(ERROR_CODES.SYSTEM_ERROR, '服务器内部错误', {
    requestId: req.headers['x-request-id'],
    ...(process.env.NODE_ENV === 'development' && { error: error.message })
  });
});

module.exports = router;