/**
 * 智慧养鹅SAAS平台 - 统一API路由架构
 * 基于微信小程序开发规范和SAAS多租户设计
 * 
 * 架构原则：
 * 1. 统一认证授权机制
 * 2. 多租户数据隔离
 * 3. 标准化响应格式
 * 4. 版本化API管理
 * 5. 完整的错误处理
 */

const express = require('express');

// 导入统一权限中间件
const { 
  authenticate, 
  requirePermission, 
  requirePermissions,
  requireTenantAccess,
  PERMISSIONS 
} = require('../middleware/auth-unified');

// 导入响应工具
const { generateSuccessResponse, generateErrorResponse } = require('../utils/response-helper');

// 导入控制器
const authController = require('../controllers/auth.controller');
const userController = require('../controllers/user.controller');
const healthController = require('../controllers/health.controller');
const productionController = require('../controllers/production.controller');
const oaController = require('../controllers/oa.controller');
const shopController = require('../controllers/shop.controller');

/**
 * API响应格式标准化中间件
 * 符合微信小程序接口规范
 */
const standardizeResponse = (req, res, next) => {
  // 重写res.json方法
  const originalJson = res.json.bind(res);
  
  res.json = (data) => {
    // 如果已经是标准格式，直接返回
    if (data && typeof data === 'object' && 'success' in data) {
      return originalJson(data);
    }
    
    // 包装为标准格式
    const response = {
      success: true,
      data: data,
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id'] || `req_${Date.now()}`
    };
    
    return originalJson(response);
  };
  
  next();
};

// 租户信息识别中间件（兼容 X-Tenant-Code 与 X-Tenant-Id）
const identifyTenant = async (req, res, next) => {
  try {
    // 统一兼容：优先使用 X-Tenant-Code，其次 X-Tenant-Id、子域名、查询参数
    const headerTenantCode = req.headers['x-tenant-code'];
    const headerTenantId = req.headers['x-tenant-id'];
    const tenantId = headerTenantCode || headerTenantId || req.subdomain || req.query.tenant_id || 'default';

    req.tenantId = tenantId;
    req.tenantContext = {
      id: tenantId,
      source: headerTenantCode ? 'X-Tenant-Code' : (headerTenantId ? 'X-Tenant-Id' : (req.subdomain ? 'subdomain' : 'query')),
      timestamp: Date.now()
    };

    next();
  } catch (error) {
    res.status(400).json(generateErrorResponse('租户识别失败', {
      code: 'TENANT_IDENTIFICATION_FAILED',
      error: error.message
    }));
  }
};

// 应用全局中间件
router.use(standardizeResponse);
router.use(identifyTenant);

// ============================================================================
// 认证相关 API - 无需认证
// ============================================================================

/**
 * 用户登录
 * POST /api/v2/auth/login
 */
router.post('/v2/auth/login', authController.login);

/**
 * 用户注册  
 * POST /api/v2/auth/register
 */
router.post('/v2/auth/register', authController.register);

/**
 * 刷新Token
 * POST /api/v2/auth/refresh
 */
router.post('/v2/auth/refresh', authController.refreshToken);

/**
 * 微信登录
 * POST /api/v2/auth/wechat
 */
router.post('/v2/auth/wechat', authController.wechatLogin);

// ============================================================================
// 以下所有路由需要认证
// ============================================================================
router.use(authenticate);

// ============================================================================
// 用户管理 API
// ============================================================================

/**
 * 获取当前用户信息
 * GET /api/v2/users/profile
 */
router.get('/v2/users/profile', userController.getProfile);

/**
 * 更新用户信息
 * PUT /api/v2/users/profile
 */
router.put('/v2/users/profile', userController.updateProfile);

/**
 * 获取用户列表 (管理员权限)
 * GET /api/v2/users
 */
router.get('/v2/users', 
  requirePermission(PERMISSIONS.USER.MANAGE),
  userController.getUsers
);

// ============================================================================
// 健康管理 API
// ============================================================================

/**
 * 获取健康记录列表
 * GET /api/v2/health/records
 */
router.get('/v2/health/records',
  requirePermission(PERMISSIONS.PRODUCTION.VIEW),
  healthController.getHealthRecords
);

/**
 * 创建健康记录
 * POST /api/v2/health/records
 */
router.post('/v2/health/records',
  requirePermission(PERMISSIONS.PRODUCTION.MANAGE),
  healthController.createHealthRecord
);

/**
 * 获取健康统计
 * GET /api/v2/health/stats
 */
router.get('/v2/health/stats',
  requirePermission(PERMISSIONS.PRODUCTION.VIEW),
  healthController.getHealthStats
);

/**
 * 获取健康记录详情
 * GET /api/v2/health/records/:id
 */
router.get('/v2/health/records/:id',
  requirePermission(PERMISSIONS.PRODUCTION.VIEW),
  healthController.getRecordById
);

/**
 * 更新健康记录
 * PUT /api/v2/health/records/:id
 */
router.put('/v2/health/records/:id',
  requirePermission(PERMISSIONS.PRODUCTION.MANAGE),
  healthController.updateRecord
);

/**
 * 删除健康记录
 * DELETE /api/v2/health/records/:id
 */
router.delete('/v2/health/records/:id',
  requirePermission(PERMISSIONS.PRODUCTION.MANAGE),
  healthController.deleteRecord
);

/**
 * AI健康诊断
 * POST /api/v2/health/ai-diagnosis
 */
router.post('/v2/health/ai-diagnosis',
  requirePermission(PERMISSIONS.PRODUCTION.VIEW),
  healthController.aiDiagnosis
);

// ============================================================================
// 生产管理 API  
// ============================================================================

/**
 * 获取生产记录列表
 * GET /api/v2/production/records
 */
router.get('/v2/production/records',
  requirePermission(PERMISSIONS.PRODUCTION.VIEW),
  productionController.getProductionRecords
);

/**
 * 创建生产记录
 * POST /api/v2/production/records
 */
router.post('/v2/production/records',
  requirePermission(PERMISSIONS.PRODUCTION.MANAGE),
  productionController.createProductionRecord
);

/**
 * 获取库存信息
 * GET /api/v2/production/inventory
 */
router.get('/v2/production/inventory',
  requirePermission(PERMISSIONS.PRODUCTION.VIEW),
  productionController.getInventory
);

/**
 * 更新库存
 * PUT /api/v2/production/inventory/:id
 */
router.put('/v2/production/inventory/:id',
  requirePermission(PERMISSIONS.PRODUCTION.INVENTORY_MANAGE),
  productionController.updateInventory
);

/**
 * 获取环境数据
 * GET /api/v2/production/environment
 */
router.get('/v2/production/environment',
  requirePermission(PERMISSIONS.PRODUCTION.VIEW),
  productionController.getEnvironmentData
);

// ============================================================================
// OA办公 API
// ============================================================================

/**
 * 获取OA统计信息
 * GET /api/v2/oa/stats
 */
router.get('/v2/oa/stats',
  requirePermission(PERMISSIONS.OA.ACCESS),
  oaController.getOAStats
);

/**
 * 财务概览
 * GET /api/v2/oa/finance/overview
 */
router.get('/v2/oa/finance/overview',
  requirePermission(PERMISSIONS.OA.FINANCE_VIEW),
  oaController.getFinanceOverview
);



/**
 * 创建报销申请
 * POST /api/v2/oa/reimbursement/applications
 */
router.post('/v2/oa/reimbursement/applications',
  requirePermission(PERMISSIONS.OA.REIMBURSEMENT_VIEW),
  oaController.createReimbursementApplication
);

/**
 * 审批报销申请
 * PUT /api/v2/oa/reimbursement/applications/:id/approve
 */
router.put('/v2/oa/reimbursement/applications/:id/approve',
  requirePermission(PERMISSIONS.OA.REIMBURSEMENT_APPROVE),
  oaController.approveReimbursementApplication
);

/**
 * 获取审批任务列表
 * GET /api/v2/oa/approvals/pending
 */
router.get('/v2/oa/approvals/pending',
  requirePermission(PERMISSIONS.OA.APPROVAL_PROCESS),
  oaController.getPendingApprovals
);

// ============================================================================
// 商城 API
// ============================================================================

/**
 * 获取商品列表
 * GET /api/v2/shop/products
 */
router.get('/v2/shop/products',
  requirePermission(PERMISSIONS.SHOP.VIEW),
  shopController.getProducts
);

/**
 * 创建商品 (管理员权限)
 * POST /api/v2/shop/products
 */
router.post('/v2/shop/products',
  requirePermission(PERMISSIONS.SHOP.MANAGE),
  shopController.createProduct
);

/**
 * 创建订单
 * POST /api/v2/shop/orders
 */
router.post('/v2/shop/orders',
  requirePermission(PERMISSIONS.SHOP.VIEW),
  shopController.createOrder
);

/**
 * 获取用户订单列表
 * GET /api/v2/shop/orders
 */
router.get('/v2/shop/orders',
  requirePermission(PERMISSIONS.SHOP.VIEW),
  shopController.getUserOrders
);

/**
 * 处理订单 (管理员权限)
 * PUT /api/v2/shop/orders/:id/status
 */
router.put('/v2/shop/orders/:id/status',
  requirePermission(PERMISSIONS.SHOP.ORDER_PROCESS),
  shopController.updateOrderStatus
);

// ============================================================================
// 平台管理 API (超级管理员权限)
// ============================================================================

/**
 * 获取租户列表
 * GET /api/v2/platform/tenants
 */
router.get('/v2/platform/tenants',
  requirePermission(PERMISSIONS.PLATFORM.TENANT_MANAGE),
  require('../controllers/platform.controller').getTenants
);

/**
 * 创建租户
 * POST /api/v2/platform/tenants
 */
router.post('/v2/platform/tenants',
  requirePermission(PERMISSIONS.PLATFORM.TENANT_MANAGE),
  require('../controllers/platform.controller').createTenant
);

/**
 * 获取平台统计
 * GET /api/v2/platform/analytics
 */
router.get('/v2/platform/analytics',
  requirePermission(PERMISSIONS.PLATFORM.ANALYTICS),
  require('../controllers/platform.controller').getPlatformAnalytics
);

// ============================================================================
// V1 API 兼容性支持 (逐步废弃)
// ============================================================================

// 重定向V1 API到V2
router.use('/v1/*', (req, res) => {
  const v2Path = req.path.replace('/v1/', '/v2/');
  res.status(301).json(generateErrorResponse('API版本已更新', {
    code: 'API_VERSION_DEPRECATED',
    message: '请使用V2版本API',
    newPath: v2Path,
    deprecationDate: '2024-12-31'
  }));
});

// ============================================================================
// 错误处理
// ============================================================================

// 404处理
router.use('*', (req, res) => {
  res.status(404).json(generateErrorResponse('API接口不存在', {
    code: 'API_NOT_FOUND',
    path: req.originalUrl,
    method: req.method
  }));
});

// 统一错误处理
router.use((error, req, res, next) => {
  try { const { Logger } = require('../middleware/errorHandler'); Logger.error('[API Error]', { error: error.message, stack: error.stack, path: req.originalUrl, method: req.method }); } catch(_) {}
  
  // JWT错误
  if (error.name === 'JsonWebTokenError') {
    return res.status(401).json(generateErrorResponse('认证失败', {
      code: 'AUTHENTICATION_FAILED'
    }));
  }
  
  // 权限错误
  if (error.code === 'INSUFFICIENT_PERMISSIONS') {
    return res.status(403).json(generateErrorResponse('权限不足', {
      code: 'INSUFFICIENT_PERMISSIONS',
      required: error.required
    }));
  }
  
  // 租户错误
  if (error.code === 'TENANT_ACCESS_DENIED') {
    return res.status(403).json(generateErrorResponse('租户访问被拒绝', {
      code: 'TENANT_ACCESS_DENIED'
    }));
  }
  
  // 通用错误
  res.status(500).json(generateErrorResponse('服务器内部错误', {
    code: 'INTERNAL_SERVER_ERROR',
    requestId: req.headers['x-request-id']
  }));
});

module.exports = router;