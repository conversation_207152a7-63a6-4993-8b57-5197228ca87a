const express = require('express');
const workspaceController = require('../controllers/workspace');
const { verifyToken } = require('../middleware/auth.middleware');
const { validatePermission } = require('../middleware/permission.middleware');

const router = express.Router();

// 应用认证中间件
router.use(verifyToken);

// ==================== 工作台主页 ====================

/**
 * @api {get} /api/v1/workspace/dashboard 获取工作台首页数据
 * @apiName GetDashboard
 * @apiGroup Workspace
 * @apiDescription 获取工作台首页统计数据、最近活动、快速操作等
 */
router.get('/dashboard', 
  validatePermission('workspace_access'), 
  workspaceController.getDashboard
);

/**
 * @api {get} /api/v1/workspace/statistics 获取财务统计数据
 * @apiName GetFinanceStatistics
 * @apiGroup Workspace
 * @apiDescription 获取财务统计数据，支持按日期范围和类型筛选
 */
router.get('/statistics', 
  validatePermission('finance_view'), 
  workspaceController.getFinanceStatistics
);

/**
 * @api {get} /api/v1/workspace/pending-approvals 获取待审批事项
 * @apiName GetPendingApprovals
 * @apiGroup Workspace
 * @apiDescription 获取当前用户的待审批事项列表
 */
router.get('/pending-approvals', 
  validatePermission('approval_view'), 
  workspaceController.getPendingApprovals
);

// ==================== 申请管理 ====================

/**
 * @api {post} /api/v1/workspace/applications 创建财务申请
 * @apiName CreateApplication
 * @apiGroup Application
 * @apiDescription 创建新的财务申请（费用报销、采购申请等）
 */
router.post('/applications', 
  validatePermission('application_create'), 
  workspaceController.createApplication
);

/**
 * @api {get} /api/v1/workspace/applications 获取申请列表
 * @apiName GetApplications
 * @apiGroup Application
 * @apiDescription 获取申请列表，支持分页和筛选
 */
router.get('/applications', 
  validatePermission('application_view'), 
  workspaceController.getApplications
);

/**
 * @api {get} /api/v1/workspace/applications/:id 获取申请详情
 * @apiName GetApplicationDetail
 * @apiGroup Application
 * @apiDescription 获取指定申请的详细信息
 */
router.get('/applications/:id', 
  validatePermission('application_view'), 
  workspaceController.getApplicationDetail
);

/**
 * @api {put} /api/v1/workspace/applications/:id 更新申请
 * @apiName UpdateApplication
 * @apiGroup Application
 * @apiDescription 更新申请信息（仅限草稿状态）
 */
router.put('/applications/:id', 
  validatePermission('application_edit'), 
  workspaceController.updateApplication
);

/**
 * @api {post} /api/v1/workspace/applications/:id/cancel 取消申请
 * @apiName CancelApplication
 * @apiGroup Application
 * @apiDescription 取消指定的申请
 */
router.post('/applications/:id/cancel', 
  validatePermission('application_cancel'), 
  workspaceController.cancelApplication
);

// ==================== 审批管理 ====================

/**
 * @api {post} /api/v1/workspace/applications/:id/approve 审批申请
 * @apiName ApproveApplication
 * @apiGroup Approval
 * @apiDescription 审批指定的申请（通过或拒绝）
 */
router.post('/applications/:id/approve', 
  validatePermission('approval_process'), 
  workspaceController.approveApplication
);

/**
 * @api {post} /api/v1/workspace/approvals/batch 批量审批
 * @apiName BatchApprove
 * @apiGroup Approval
 * @apiDescription 批量审批多个申请
 */
router.post('/approvals/batch', 
  validatePermission('approval_batch'), 
  workspaceController.batchApprove
);

/**
 * @api {get} /api/v1/workspace/applications/:id/approval-history 获取审批历史
 * @apiName GetApprovalHistory
 * @apiGroup Approval
 * @apiDescription 获取指定申请的审批历史记录
 */
router.get('/applications/:id/approval-history', 
  validatePermission('approval_view'), 
  workspaceController.getApprovalHistory
);

/**
 * @api {get} /api/v1/workspace/my-approvals 获取我的审批任务
 * @apiName GetMyApprovalTasks
 * @apiGroup Approval
 * @apiDescription 获取当前用户的审批任务列表
 */
router.get('/my-approvals', 
  validatePermission('approval_view'), 
  workspaceController.getMyApprovalTasks
);

// ==================== 财务记录管理 ====================

/**
 * @api {get} /api/v1/workspace/finance-records 获取财务记录
 * @apiName GetFinanceRecords
 * @apiGroup Finance
 * @apiDescription 获取财务记录列表，支持筛选和分页
 */
router.get('/finance-records', 
  validatePermission('finance_view'), 
  async (req, res) => {
    // 这里可以添加财务记录查询逻辑
    res.json({
      success: true,
      message: '财务记录接口待实现',
      data: []
    });
  }
);

/**
 * @api {post} /api/v1/workspace/finance-records/sync 同步健康模块数据
 * @apiName SyncHealthData
 * @apiGroup Finance
 * @apiDescription 同步健康模块的财务数据到工作台
 */
router.post('/finance-records/sync', 
  validatePermission('finance_sync'), 
  async (req, res) => {
    // 这里可以添加数据同步逻辑
    res.json({
      success: true,
      message: '数据同步接口待实现'
    });
  }
);

/**
 * @api {get} /api/v1/workspace/reports 获取财务报表
 * @apiName GetFinanceReports
 * @apiGroup Finance
 * @apiDescription 获取财务报表数据
 */
router.get('/reports', 
  validatePermission('finance_report'), 
  async (req, res) => {
    // 这里可以添加报表生成逻辑
    res.json({
      success: true,
      message: '财务报表接口待实现',
      data: {}
    });
  }
);

/**
 * @api {post} /api/v1/workspace/reports/export 导出财务数据
 * @apiName ExportFinanceData
 * @apiGroup Finance
 * @apiDescription 导出财务数据为Excel或PDF格式
 */
router.post('/reports/export', 
  validatePermission('finance_export'), 
  async (req, res) => {
    // 这里可以添加数据导出逻辑
    res.json({
      success: true,
      message: '数据导出接口待实现'
    });
  }
);

// ==================== 错误处理 ====================

// 404 处理
router.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在'
  });
});

// 错误处理中间件
router.use((error, req, res, next) => {
  console.error('工作台路由错误:', error);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

module.exports = router;
