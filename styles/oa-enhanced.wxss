/**
 * OA系统通用样式增强
 * 统一的视觉风格、交互动效和响应式设计
 */

/* ==================== CSS变量扩展 ==================== */
page {
  /* OA专用色彩变量 */
  --oa-primary: #0066CC;
  --oa-primary-light: #4D94FF;
  --oa-primary-dark: #003D7A;
  --oa-secondary: #0099CC;
  --oa-accent: #FF6B35;
  
  /* 角色权限色彩 */
  --role-admin: #8E44AD;
  --role-manager: #E67E22;
  --role-employee: #3498DB;
  
  /* 状态色彩 */
  --status-pending: #FF9500;
  --status-approved: #34C759;
  --status-rejected: #FF3B30;
  --status-processing: #007AFF;
  
  /* 功能模块色彩 */
  --module-purchase: #00A86B;
  --module-finance: #0066CC;
  --module-staff: #9B59B6;
  --module-approval: #E67E22;
  
  /* 增强的阴影系统 */
  --shadow-oa-sm: 0 2rpx 8rpx rgba(0, 102, 204, 0.1);
  --shadow-oa-md: 0 4rpx 16rpx rgba(0, 102, 204, 0.15);
  --shadow-oa-lg: 0 8rpx 32rpx rgba(0, 102, 204, 0.2);
  --shadow-oa-xl: 0 16rpx 48rpx rgba(0, 102, 204, 0.25);
  
  /* 毛玻璃效果 */
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-backdrop: blur(20rpx);
  
  /* 动画时长 */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.4s;
  
  /* 动画曲线 */
  --ease-out-quad: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-out-quart: cubic-bezier(0.165, 0.84, 0.44, 1);
  --ease-in-out-back: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ==================== 通用容器样式 ==================== */
.oa-page-container,
.oa-container {
  min-height: 100vh;
  background: linear-gradient(180deg, var(--oa-primary) 0%, var(--oa-primary-dark) 100%);
  position: relative;
  overflow-x: hidden;
  padding: 32rpx;
}

.oa-page-container::before,
.oa-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(0, 102, 204, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(0, 68, 153, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(0, 102, 204, 0.08) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.oa-content-section {
  position: relative;
  z-index: 1;
}

/* ==================== 通用卡片样式 ==================== */
.oa-card {
  background: var(--glass-bg);
  border-radius: var(--radius-2xl);
  padding: var(--space-xl);
  backdrop-filter: var(--glass-backdrop);
  box-shadow: var(--shadow-oa-md);
  border: 1rpx solid var(--glass-border);
  transition: all var(--transition-normal) var(--ease-out-quart);
  position: relative;
  overflow: hidden;
}

.oa-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
  border-radius: inherit;
  opacity: 0;
  transition: opacity var(--transition-normal) ease;
}

.oa-card:active {
  transform: translateY(-4rpx) scale(0.98);
  box-shadow: var(--shadow-oa-lg);
}

.oa-card:active::before {
  opacity: 1;
}

/* ==================== 通用按钮样式 ==================== */
.oa-btn {
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: 600;
  text-align: center;
  transition: all var(--transition-normal) var(--ease-out-quart);
  backdrop-filter: var(--glass-backdrop);
  border: none;
  position: relative;
  overflow: hidden;
}

.oa-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all var(--transition-fast) ease;
}

.oa-btn:active::before {
  width: 300rpx;
  height: 300rpx;
}

.oa-btn--primary {
  background: linear-gradient(135deg, var(--oa-primary) 0%, var(--oa-secondary) 100%);
  color: white;
  box-shadow: var(--shadow-oa-md);
}

.oa-btn--success {
  background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(52, 199, 89, 0.3);
}

.oa-btn--danger {
  background: linear-gradient(135deg, #ff3b30 0%, #ff6b5a 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 59, 48, 0.3);
}

.oa-btn--ghost {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.oa-btn:active {
  transform: scale(0.96);
}

/* ==================== 状态标签增强 ==================== */
.oa-status-badge {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 700;
  text-align: center;
  backdrop-filter: var(--glass-backdrop);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.oa-status-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.oa-status-badge:active::before {
  left: 100%;
}

.status-badge--pending {
  background: linear-gradient(135deg, var(--status-pending) 0%, #ffb84d 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.3);
}

.status-badge--approved {
  background: linear-gradient(135deg, var(--status-approved) 0%, #30d158 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(52, 199, 89, 0.3);
}

.status-badge--rejected {
  background: linear-gradient(135deg, var(--status-rejected) 0%, #ff6b5a 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.3);
}

.status-badge--processing {
  background: linear-gradient(135deg, var(--status-processing) 0%, #5ac8fa 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

/* ==================== 角色标识增强 ==================== */
.oa-role-badge {
  padding: 4rpx 8rpx;
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 700;
  backdrop-filter: var(--glass-backdrop);
}

.role-badge--admin {
  background: linear-gradient(135deg, var(--role-admin) 0%, #9b59b6 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(142, 68, 173, 0.3);
}

.role-badge--manager {
  background: linear-gradient(135deg, var(--role-manager) 0%, #f39c12 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(230, 126, 34, 0.3);
}

.role-badge--employee {
  background: linear-gradient(135deg, var(--role-employee) 0%, #5dade2 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(52, 152, 219, 0.3);
}

/* ==================== 加载动画增强 ==================== */
.oa-loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.2);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: oa-spin 1s linear infinite;
  position: relative;
}

.oa-loading-spinner::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20rpx;
  height: 20rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: oa-pulse 1.5s ease-in-out infinite;
}

@keyframes oa-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes oa-pulse {
  0%, 100% { 
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  50% { 
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

/* ==================== 模态框增强 ==================== */
.oa-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: oa-fade-in var(--transition-normal) ease;
}

.oa-modal {
  background: white;
  border-radius: var(--radius-2xl);
  margin: var(--space-xl);
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: var(--shadow-oa-xl);
  animation: oa-slide-in var(--transition-normal) var(--ease-out-back);
  position: relative;
}

.oa-modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--oa-primary) 0%, var(--oa-secondary) 100%);
}

@keyframes oa-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes oa-slide-in {
  from { 
    opacity: 0;
    transform: translateY(40rpx) scale(0.9);
  }
  to { 
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* ==================== 表单增强 ==================== */
.oa-form-item {
  margin-bottom: var(--space-lg);
  position: relative;
}

.oa-form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  position: relative;
}

.oa-form-label::after {
  content: '';
  position: absolute;
  bottom: -4rpx;
  left: 0;
  width: 0;
  height: 2rpx;
  background: var(--oa-primary);
  transition: width var(--transition-normal) ease;
}

.oa-form-item.focused .oa-form-label::after {
  width: 100%;
}

.oa-form-input {
  width: 100%;
  background: rgba(0, 0, 0, 0.05);
  border: 2rpx solid rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--space-md);
  font-size: var(--text-base);
  color: var(--text-primary);
  transition: all var(--transition-normal) var(--ease-out-quad);
  position: relative;
}

.oa-form-input:focus {
  border-color: var(--oa-primary);
  box-shadow: 0 0 0 4rpx rgba(0, 102, 204, 0.1);
  background: rgba(0, 102, 204, 0.02);
}

/* ==================== 微交互动效 ==================== */
.oa-hover-lift {
  transition: all var(--transition-normal) var(--ease-out-quart);
}

.oa-hover-lift:active {
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-oa-lg);
}

.oa-press-scale {
  transition: all var(--transition-fast) var(--ease-out-quad);
}

.oa-press-scale:active {
  transform: scale(0.96);
}

.oa-press-feedback {
  position: relative;
  overflow: hidden;
}

.oa-press-feedback::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all var(--transition-fast) ease;
}

.oa-press-feedback:active::after {
  width: 200rpx;
  height: 200rpx;
}

/* ==================== 权限指示器 ==================== */
.oa-permission-indicator {
  position: absolute;
  top: var(--space-sm);
  right: var(--space-sm);
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  border: 2rpx solid white;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.permission-indicator--admin {
  background: var(--role-admin);
}

.permission-indicator--manager {
  background: var(--role-manager);
}

.permission-indicator--employee {
  background: var(--role-employee);
}

/* ==================== 通知标识 ==================== */
.oa-notification-dot {
  position: relative;
}

.oa-notification-dot::after {
  content: '';
  position: absolute;
  top: -2rpx;
  right: -2rpx;
  width: 8rpx;
  height: 8rpx;
  background: #ff3b30;
  border-radius: 50%;
  border: 2rpx solid white;
  animation: oa-notification-pulse 2s infinite;
}

@keyframes oa-notification-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

/* ==================== 数据可视化增强 ==================== */
.oa-progress-bar {
  width: 100%;
  height: 8rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-md);
  overflow: hidden;
  position: relative;
}

.oa-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--oa-primary) 0%, var(--oa-secondary) 100%);
  border-radius: inherit;
  transition: width var(--transition-slow) var(--ease-out-quart);
  position: relative;
}

.oa-progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: oa-progress-shine 2s infinite;
}

@keyframes oa-progress-shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* ==================== 列表优化 ==================== */
.oa-list-item {
  background: var(--glass-bg);
  border-radius: var(--radius-xl);
  margin-bottom: var(--space-md);
  padding: var(--space-lg);
  backdrop-filter: var(--glass-backdrop);
  box-shadow: var(--shadow-oa-sm);
  border: 1rpx solid var(--glass-border);
  transition: all var(--transition-normal) var(--ease-out-quad);
  position: relative;
}

.oa-list-item:active {
  transform: translateX(8rpx);
  box-shadow: var(--shadow-oa-md);
  border-color: var(--oa-primary);
}

.oa-list-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: var(--oa-primary);
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
  transform: scaleY(0);
  transition: transform var(--transition-normal) var(--ease-out-back);
}

.oa-list-item:active::before {
  transform: scaleY(1);
}

/* ==================== 响应式网格系统 ==================== */
.oa-grid {
  display: grid;
  gap: var(--space-lg);
}

.oa-grid--2 {
  grid-template-columns: repeat(2, 1fr);
}

.oa-grid--3 {
  grid-template-columns: repeat(3, 1fr);
}

.oa-grid--4 {
  grid-template-columns: repeat(4, 1fr);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .oa-grid--3,
  .oa-grid--4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 500rpx) {
  .oa-grid--2,
  .oa-grid--3,
  .oa-grid--4 {
    grid-template-columns: 1fr;
  }
}

/* ==================== 无障碍增强 ==================== */
.oa-screen-reader-only {
  position: absolute;
  width: 1rpx;
  height: 1rpx;
  padding: 0;
  margin: -1rpx;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ==================== 深色模式支持 ==================== */
@media (prefers-color-scheme: dark) {
  :root {
    --glass-bg: rgba(28, 28, 30, 0.95);
    --glass-border: rgba(255, 255, 255, 0.1);
  }
  
  .oa-card,
  .oa-list-item {
    background: var(--glass-bg);
    border-color: var(--glass-border);
  }
  
  .oa-form-input {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    color: white;
  }
}

/* ==================== 打印样式 ==================== */
@media print {
  .oa-page-container {
    background: white !important;
  }
  
  .oa-card {
    background: white !important;
    box-shadow: none !important;
    border: 1rpx solid #ddd !important;
  }
  
  .oa-btn,
  .oa-modal-overlay {
    display: none !important;
  }
}

/* ==================== 高对比度模式支持 ==================== */
@media (prefers-contrast: high) {
  .oa-card {
    border-width: 2rpx;
    border-color: var(--text-primary);
  }
  
  .oa-btn {
    border: 2rpx solid currentColor;
  }
  
  .oa-status-badge {
    border-width: 2rpx;
  }
}

/* ==================== 减少动画模式支持 ==================== */
@media (prefers-reduced-motion: reduce) {
  .oa-card,
  .oa-btn,
  .oa-list-item,
  .oa-modal {
    transition: none !important;
    animation: none !important;
  }
  
  .oa-progress-fill::after,
  .oa-loading-spinner::after,
  .oa-notification-dot::after {
    animation: none !important;
  }
}