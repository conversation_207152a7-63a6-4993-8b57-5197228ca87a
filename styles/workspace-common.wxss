/* 工作台模块公共样式 */

/* ==================== CSS变量定义 ==================== */
page {
  --primary-color: #667eea;
  --primary-light: #8fa4f3;
  --primary-dark: #4c63d2;
  
  --success-color: #28a745;
  --success-light: #d4edda;
  
  --warning-color: #ffc107;
  --warning-light: #fff3cd;
  
  --error-color: #dc3545;
  --error-light: #f8d7da;
  
  --info-color: #17a2b8;
  --info-light: #cce7ff;
  
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #f5f7fa;
  
  --border-light: #e9ecef;
  --border-medium: #dee2e6;
  
  --shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  --shadow-medium: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  --shadow-heavy: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
  
  --radius-sm: 8rpx;
  --radius-md: 12rpx;
  --radius-lg: 16rpx;
  --radius-xl: 20rpx;
  
  --space-xs: 8rpx;
  --space-sm: 12rpx;
  --space-md: 16rpx;
  --space-lg: 20rpx;
  --space-xl: 24rpx;
  --space-2xl: 32rpx;
  --space-3xl: 40rpx;
  --space-4xl: 48rpx;
}

/* ==================== 通用布局 ==================== */
.workspace-section {
  margin: var(--space-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-medium);
}

.workspace-section-header {
  padding: var(--space-xl);
  border-bottom: 1rpx solid var(--border-light);
  background: var(--bg-primary);
}

.workspace-section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.workspace-section-content {
  padding: var(--space-xl);
}

/* ==================== 通用按钮 ==================== */
.workspace-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-md);
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.workspace-button--primary {
  background: var(--primary-color);
  color: white;
}

.workspace-button--primary:active {
  background: var(--primary-dark);
  transform: translateY(-2rpx);
}

.workspace-button--secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1rpx solid var(--border-medium);
}

.workspace-button--success {
  background: var(--success-color);
  color: white;
}

.workspace-button--warning {
  background: var(--warning-color);
  color: white;
}

.workspace-button--error {
  background: var(--error-color);
  color: white;
}

.workspace-button--small {
  padding: var(--space-sm) var(--space-lg);
  font-size: 24rpx;
}

.workspace-button--large {
  padding: var(--space-lg) var(--space-2xl);
  font-size: 32rpx;
}

.workspace-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* ==================== 通用卡片 ==================== */
.workspace-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-lg);
  box-shadow: var(--shadow-medium);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.workspace-card:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-heavy);
}

.workspace-card--clickable {
  cursor: pointer;
}

.workspace-card--bordered {
  border: 1rpx solid var(--border-light);
}

/* ==================== 通用列表 ==================== */
.workspace-list {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-medium);
}

.workspace-list-item {
  display: flex;
  align-items: center;
  padding: var(--space-xl);
  border-bottom: 1rpx solid var(--border-light);
  transition: background-color 0.2s ease;
}

.workspace-list-item:last-child {
  border-bottom: none;
}

.workspace-list-item:active {
  background: var(--bg-secondary);
}

.workspace-list-item-content {
  flex: 1;
  margin-right: var(--space-lg);
}

.workspace-list-item-title {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.workspace-list-item-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.workspace-list-item-right {
  color: var(--text-tertiary);
  font-size: 24rpx;
}

/* ==================== 状态标签 ==================== */
.workspace-status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: 22rpx;
  font-weight: 500;
}

.workspace-status--pending {
  background: var(--warning-light);
  color: var(--warning-color);
}

.workspace-status--approved {
  background: var(--success-light);
  color: var(--success-color);
}

.workspace-status--rejected {
  background: var(--error-light);
  color: var(--error-color);
}

.workspace-status--processing {
  background: var(--info-light);
  color: var(--info-color);
}

.workspace-status--draft {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

/* ==================== 表单元素 ==================== */
.workspace-form-group {
  margin-bottom: var(--space-xl);
}

.workspace-form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.workspace-form-input {
  width: 100%;
  padding: var(--space-lg);
  border: 1rpx solid var(--border-medium);
  border-radius: var(--radius-md);
  font-size: 28rpx;
  color: var(--text-primary);
  background: var(--bg-primary);
  box-sizing: border-box;
}

.workspace-form-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.workspace-form-textarea {
  min-height: 120rpx;
  resize: vertical;
}

.workspace-form-error {
  color: var(--error-color);
  font-size: 24rpx;
  margin-top: var(--space-xs);
}

/* ==================== 空状态 ==================== */
.workspace-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-4xl) var(--space-xl);
  text-align: center;
}

.workspace-empty-icon {
  font-size: 120rpx;
  margin-bottom: var(--space-xl);
  opacity: 0.5;
}

.workspace-empty-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.workspace-empty-desc {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* ==================== 加载状态 ==================== */
.workspace-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-4xl);
}

.workspace-loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-light);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: workspace-spin 1s linear infinite;
}

.workspace-loading-text {
  margin-top: var(--space-lg);
  color: var(--text-secondary);
  font-size: 28rpx;
}

@keyframes workspace-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 750rpx) {
  .workspace-section {
    margin: var(--space-md);
  }

  .workspace-section-header,
  .workspace-section-content {
    padding: var(--space-lg);
  }

  .workspace-card {
    padding: var(--space-lg);
  }

  .workspace-list-item {
    padding: var(--space-lg);
  }
}
