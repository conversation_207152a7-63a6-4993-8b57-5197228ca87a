/**
 * 多租户登录页面
 * Multi-tenant Login Page
 */

const request = require('../../utils/request.js');
const tenantConfig = require('../../utils/tenant-config.js');

// 获取全局app实例
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: false,
    tenantInfo: null,
    showLogin: false,
    userInfo: null,
    hasUserInfo: false,
    canIUseGetUserProfile: true,
    tenantCode: '',
    errorMessage: '',
    loginMode: 'wechat', // wechat 或 demo
    selectedRole: 'admin', // 默认选择管理员
    currentRoleName: '管理员', // 当前选择的角色名称
    demoRoles: [
      {
        role: 'admin',
        name: '管理员',
        description: '全部功能权限 - 系统管理、财务、采购、审批',
        avatar: '/images/icons/admin.png',
        department: '管理部',
        position: '系统管理员'
      },
      {
        role: 'manager',
        name: '经理',
        description: '部门管理权限 - 审批、采购、报销、人员管理',
        avatar: '/images/icons/manager.png',
        department: '管理部',
        position: '部门经理'
      },
      {
        role: 'finance',
        name: '财务',
        description: '财务专项权限 - 财务管理、报销审批、报表',
        avatar: '/images/icons/finance.png',
        department: '财务部',
        position: '财务主管'
      },
      {
        role: 'employee',
        name: '普通员工',
        description: '基础操作权限 - 申请报销、采购申请、查看审批',
        avatar: '/images/icons/user.png',
        department: '生产部',
        position: '普通员工'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // logger.debug('登录页面加载参数:', options);
    
    // 获取租户代码
    let tenantCode = options.tenantCode || options.tenant || '';
    
    // 如果没有租户代码，尝试从场景值获取（扫码进入）
    if (!tenantCode && options.scene) {
      const sceneValue = decodeURIComponent(options.scene);
      const sceneParams = this.parseSceneValue(sceneValue);
      tenantCode = sceneParams.tenant || sceneParams.tenantCode || '';
    }

    this.setData({
      tenantCode: tenantCode
    });

    if (tenantCode) {
      this.initTenantConfig(tenantCode);
    } else {
      this.setData({
        errorMessage: '缺少租户信息，请通过正确的链接或二维码进入',
        showLogin: true // 显示演示登录
      });
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 检查是否已经登录
    const token = wx.getStorageSync('access_token');
    if (token) {
      // 已登录，直接跳转首页
      wx.switchTab({
        url: '/pages/home/<USER>'
      });
    }
  },

  /**
   * 解析场景值参数
   */
  parseSceneValue: function(sceneValue) {
    const params = {};
    if (sceneValue) {
      const pairs = sceneValue.split('&');
      pairs.forEach(pair => {
        const [key, value] = pair.split('=');
        if (key && value) {
          params[key] = value;
        }
      });
    }
    return params;
  },

  /**
   * 初始化租户配置
   */
  initTenantConfig: async function(tenantCode) {
    try {
      wx.showLoading({
        title: '加载中...'
      });

      // 初始化租户配置
      const config = await tenantConfig.initTenantConfig(tenantCode);
      
      this.setData({
        tenantInfo: {
          name: config.tenantName || '智慧养鹅',
          logo: config.logo || '/images/default-logo.png',
          description: config.description || '智能化养鹅管理平台'
        },
        showLogin: true,
        errorMessage: ''
      });

      wx.hideLoading();
    } catch (error) {
      // logger.error('初始化租户配置失败:', error);
      wx.hideLoading();
      
      this.setData({
        errorMessage: '租户配置加载失败，请检查租户代码是否正确',
        showLogin: true // 显示演示登录
      });
      
      wx.showToast({
        title: '租户配置加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 获取用户信息（兼容新旧API）
   */
  getUserProfile: function() {
    // 检查是否支持 getUserProfile
    if (wx.getUserProfile) {
      wx.getUserProfile({
        desc: '用于完善会员资料',
        success: (res) => {
          // logger.debug('getUserProfile成功:', res);
          this.setData({
            userInfo: res.userInfo,
            hasUserInfo: true
          });
          this.doWechatLogin(res.userInfo);
        },
        fail: (err) => {
          // logger.error('getUserProfile失败:', err);
          wx.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          });
        }
      });
    } else {
      // 降级处理
      wx.showToast({
        title: '请升级微信版本',
        icon: 'none'
      });
    }
  },

  /**
   * 执行微信登录
   */
  doWechatLogin: async function(userInfo) {
    try {
      this.setData({ loading: true });

      // 获取微信登录凭证
      const loginRes = await this.wxLogin();
      // 调用后端登录接口
      const { API } = require('../../constants/index.js');

      if (response.success) {
        // 构造统一的用户信息格式
        const normalizedUserInfo = {
          id: response.data.user?.id || 'wechat_user_' + Date.now(),
          name: response.data.user?.nickname || '微信用户',
          farmName: response.data.tenant?.companyName || '智慧生态养鹅基地',
          role: response.data.user?.role === 'admin' ? '管理员' : 
            response.data.user?.role === 'manager' ? '经理' :
              response.data.user?.role === 'finance' ? '财务' : '普通员工',
          roleCode: response.data.user?.role || 'employee', // 使用employee替代user
          avatar: response.data.user?.avatar || '/images/default_avatar.png',
          phone: response.data.user?.phone || '',
          email: response.data.user?.email || '',
          department: response.data.user?.department || '生产部',
          position: response.data.user?.position || '员工'
        };

        // 保存登录信息
        wx.setStorageSync('access_token', response.data.tokens?.accessToken || response.data.accessToken);
        wx.setStorageSync('refresh_token', response.data.tokens?.refreshToken || response.data.refreshToken);
        wx.setStorageSync('user_info', normalizedUserInfo);

        // 同步到全局数据
        app.globalData.userInfo = normalizedUserInfo;

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });

        // 跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/home/<USER>'
          });
        }, 1500);
      } else {
        throw new Error(response.message || '登录失败');
      }
    } catch (error) {
      try { const logger = require('../../utils/logger.js'); logger.error && logger.error('登录失败', error); } catch(_) {}
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 微信登录
   */
  wxLogin: function() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  },

  /**
   * 选择演示角色
   */
  selectRole: function(e) {
    const role = e.currentTarget.dataset.role;
    const roleInfo = this.data.demoRoles.find(item => item.role === role);
    this.setData({
      selectedRole: role,
      currentRoleName: roleInfo ? roleInfo.name : '管理员'
    });
  },

  /**
   * 获取当前选择的角色名称
   */
  getCurrentRoleName: function() {
    const currentRole = this.data.demoRoles.find(item => item.role === this.data.selectedRole);
    return currentRole ? currentRole.name : '管理员';
  },

  /**
   * 演示模式登录
   */
  demoLogin: function() {
    wx.showLoading({ title: '登录中...' });

    // 获取选择的角色信息
    const selectedRoleInfo = this.data.demoRoles.find(item => item.role === this.data.selectedRole);
    
    // 构造统一格式的演示用户信息
    const demoUserInfo = {
      id: 'demo_' + this.data.selectedRole + '_' + Date.now(),
      name: selectedRoleInfo.name,
      farmName: '智慧生态养鹅基地',
      role: selectedRoleInfo.name, // 使用中文角色名
      roleCode: this.data.selectedRole, // 保存英文角色代码
      avatar: selectedRoleInfo.avatar,
      department: selectedRoleInfo.department,
      position: selectedRoleInfo.position,
      phone: '138****' + Math.floor(Math.random() * 10000).toString().padStart(4, '0'),
      email: this.data.selectedRole + '@goosefarm.com',
      openid: 'demo_openid_' + this.data.selectedRole + '_' + Date.now()
    };

    // 模拟token
    const demoToken = 'demo_token_' + this.data.selectedRole + '_' + Date.now();

    setTimeout(() => {
      wx.hideLoading();
      
      // 保存演示登录信息
      wx.setStorageSync('access_token', demoToken);
      wx.setStorageSync('user_info', demoUserInfo);

      // 设置到全局数据
      app.globalData.userInfo = demoUserInfo;

      wx.showToast({
        title: `${selectedRoleInfo.name}登录成功`,
        icon: 'success'
      });

      // 跳转到首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/home/<USER>'
        });
      }, 1500);
    }, 1000);
  },

  /**
   * 切换登录模式
   */
  switchLoginMode: function() {
    const newMode = this.data.loginMode === 'wechat' ? 'demo' : 'wechat';
    this.setData({
      loginMode: newMode
    });
  },

  /**
   * 重新加载页面
   */
  reloadPage: function() {
    wx.reLaunch({
      url: '/pages/login/login'
    });
  },

  /**
   * 联系客服
   */
  contactService: function() {
    wx.showModal({
      title: '联系客服',
      content: '如需帮助，请联系客服微信：service123',
      showCancel: false
    });
  },

  /**
   * 演示模式说明
   */
  onDemoInfo: function() {
    wx.showModal({
      title: '演示模式说明',
      content: '当前为演示模式，可选择不同角色体验相应功能权限：\n\n• 管理员：全部功能权限\n• 经理：审批和管理权限\n• 财务：财务管理权限\n• 普通员工：基础操作权限\n\n演示数据仅供展示，不会保存到真实数据库。',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.reloadPage();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '智慧养鹅管理平台',
      path: `/pages/login/login?tenantCode=${this.data.tenantCode}`
    };
  }
});