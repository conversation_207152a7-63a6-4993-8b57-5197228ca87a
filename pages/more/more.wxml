<!-- pages/more/more.wxml -->
<view class="more-container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-avatar">
              <image src="{{userInfo.avatar || '/images/default_avatar.png'}}" mode="aspectFill"></image>
    </view>
    <view class="user-info">
      <text class="user-name">{{userInfo.name || '养殖户'}}</text>
      <text class="user-role">{{userInfo.role || '普通用户'}}</text>
      <text class="farm-name">{{userInfo.farmName || '未设置养殖场'}}</text>
    </view>
    <view class="user-stats">
      <view class="stat-item">
        <text class="stat-value">{{userInfo.totalDays || 0}}</text>
        <text class="stat-label">使用天数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{userInfo.totalRecords || 0}}</text>
        <text class="stat-label">记录数</text>
      </view>
    </view>
  </view>

  <!-- 功能分类 -->
  <view class="function-sections">
    <!-- 生产管理 -->
    <view class="section">
      <view class="section-header">
        <image class="section-icon" src="/images/icons/production.png" mode="aspectFit"></image>
        <text class="section-title">生产管理</text>
      </view>
      <view class="function-grid">
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/health/health?tab=3">
          <image class="function-icon" src="/images/icons/record.png" mode="aspectFit"></image>
          <text class="function-name">生产记录</text>
        </view>
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/production/environment/environment">
          <image class="function-icon" src="/images/icons/environment.png" mode="aspectFit"></image>
          <text class="function-name">环境监控</text>
        </view>
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/health/health?tab=2">
          <image class="function-icon" src="/images/icons/material.png" mode="aspectFit"></image>
          <text class="function-name">物料管理</text>
        </view>
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/production/finance/finance">
          <image class="function-icon" src="/images/icons/finance.png" mode="aspectFit"></image>
          <text class="function-name">财务管理</text>
        </view>
      </view>
    </view>

    <!-- 健康管理 -->
    <view class="section">
      <view class="section-header">
        <image class="section-icon" src="/images/icons/health.png" mode="aspectFit"></image>
        <text class="section-title">健康管理</text>
      </view>
      <view class="function-grid">
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/health/record-list/record-list">
          <image class="function-icon" src="/images/icons/health_record.png" mode="aspectFit"></image>
          <text class="function-name">健康记录</text>
        </view>
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/health/report/report">
          <image class="function-icon" src="/images/icons/chart.png" mode="aspectFit"></image>
          <text class="function-name">健康报告</text>
        </view>
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/health/knowledge/knowledge">
          <image class="function-icon" src="/images/icons/book.png" mode="aspectFit"></image>
          <text class="function-name">养殖知识</text>
        </view>
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/health/ai-diagnosis/ai-diagnosis">
          <image class="function-icon" src="/images/icons/ai.png" mode="aspectFit"></image>
          <text class="function-name">AI诊断</text>
        </view>
      </view>
    </view>

    <!-- 商城服务 -->
    <view class="section">
      <view class="section-header">
        <image class="section-icon" src="/images/icons/shop.png" mode="aspectFit"></image>
        <text class="section-title">商城服务</text>
      </view>
      <view class="function-grid">
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/shop/shop">
          <image class="function-icon" src="/images/icons/shopping.png" mode="aspectFit"></image>
          <text class="function-name">采购商城</text>
        </view>
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/shop/cart">
          <image class="function-icon" src="/images/icons/cart.png" mode="aspectFit"></image>
          <text class="function-name">购物车</text>
        </view>
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/order/order-list/order-list">
          <image class="function-icon" src="/images/icons/order.png" mode="aspectFit"></image>
          <text class="function-name">我的订单</text>
        </view>
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/shop/favorites/favorites">
          <image class="function-icon" src="/images/icons/favorite.png" mode="aspectFit"></image>
          <text class="function-name">收藏夹</text>
        </view>
      </view>
    </view>

    <!-- 数据分析 -->
    <view class="section">
      <view class="section-header">
        <image class="section-icon" src="/images/icons/chart.png" mode="aspectFit"></image>
        <text class="section-title">数据分析</text>
      </view>
      <view class="function-grid">
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/analytics/production/production">
          <image class="function-icon" src="/images/icons/production_chart.png" mode="aspectFit"></image>
          <text class="function-name">生产分析</text>
        </view>
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/analytics/health/health">
          <image class="function-icon" src="/images/icons/health_chart.png" mode="aspectFit"></image>
          <text class="function-name">健康分析</text>
        </view>
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/analytics/finance/finance">
          <image class="function-icon" src="/images/icons/finance_chart.png" mode="aspectFit"></image>
          <text class="function-name">财务分析</text>
        </view>
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/analytics/report/report">
          <image class="function-icon" src="/images/icons/report.png" mode="aspectFit"></image>
          <text class="function-name">综合报告</text>
        </view>
      </view>
    </view>

    <!-- OA办公 -->
    <view class="section">
      <view class="section-header">
        <image class="section-icon" src="/images/icons/oa.png" mode="aspectFit"></image>
        <text class="section-title">OA办公</text>
      </view>
      <view class="function-grid">
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/workspace/workspace">
          <image class="function-icon" src="/images/icons/oa_system.png" mode="aspectFit"></image>
          <text class="function-name">工作台</text>
        </view>
        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/workspace/finance/overview/overview">
          <image class="function-icon" src="/images/icons/finance.png" mode="aspectFit"></image>
          <text class="function-name">财务管理</text>
        </view>

        <view class="function-item" bindtap="onFunctionTap" data-url="/pages/workspace/approval/pending/pending">
          <image class="function-icon" src="/images/icons/approval.png" mode="aspectFit"></image>
          <text class="function-name">审批流程</text>
        </view>
      </view>
    </view>

    <!-- 系统设置 -->
    <view class="section">
      <view class="section-header">
        <image class="section-icon" src="/images/icons/settings.png" mode="aspectFit"></image>
        <text class="section-title">系统设置</text>
      </view>
      <view class="function-list">
        <view class="function-item-list" bindtap="onFunctionTap" data-url="/pages/settings/profile/profile">
          <image class="function-icon-small" src="/images/icons/user.png" mode="aspectFit"></image>
          <text class="function-name-list">个人资料</text>
          <image class="arrow-icon" src="/images/icons/arrow_right.png" mode="aspectFit"></image>
        </view>
        <view class="function-item-list" bindtap="onFunctionTap" data-url="/pages/settings/farm/farm">
          <image class="function-icon-small" src="/images/icons/farm.png" mode="aspectFit"></image>
          <text class="function-name-list">养殖场设置</text>
          <image class="arrow-icon" src="/images/icons/arrow_right.png" mode="aspectFit"></image>
        </view>
        <view class="function-item-list" bindtap="onFunctionTap" data-url="/pages/settings/notification/notification">
          <image class="function-icon-small" src="/images/icons/notification.png" mode="aspectFit"></image>
          <text class="function-name-list">消息通知</text>
          <image class="arrow-icon" src="/images/icons/arrow_right.png" mode="aspectFit"></image>
        </view>
        <view class="function-item-list" bindtap="onFunctionTap" data-url="/pages/settings/backup/backup">
          <image class="function-icon-small" src="/images/icons/backup.png" mode="aspectFit"></image>
          <text class="function-name-list">数据备份</text>
          <image class="arrow-icon" src="/images/icons/arrow_right.png" mode="aspectFit"></image>
        </view>
        <view class="function-item-list" bindtap="onFunctionTap" data-url="/pages/settings/about/about">
          <image class="function-icon-small" src="/images/icons/info.png" mode="aspectFit"></image>
          <text class="function-name-list">关于我们</text>
          <image class="arrow-icon" src="/images/icons/arrow_right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer-info">
    <text class="app-version">智慧养鹅 v1.0.0</text>
    <text class="copyright">© 2024 智慧养鹅系统</text>
  </view>
</view>
