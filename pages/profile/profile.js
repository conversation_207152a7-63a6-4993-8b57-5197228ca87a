// pages/profile/profile.js
const { IMAGES, UI, BUSINESS } = require('../../constants/index.js');
const request = require('../../utils/request.js');
const orderService = require('../../utils/order-service.js');
const { pageHooks } = require('../../utils/unified-loading.js');

/**
 * 个人中心页面 - 智慧养鹅小程序用户管理页面
 * 功能：用户信息展示、订单管理、数据统计、功能菜单
 * 优化完成：图标常量化、性能优化、最佳实践应用
 */

Page({
  data: {
    // 用户信息
    userInfo: {
      avatar: IMAGES.DEFAULTS.AVATAR,
      name: '张三',
      farmName: '绿野生态养鹅场',
      role: '管理员', // 可以是：管理员、经理、财务、普通员工
      phone: '138****8888',
      email: 'z<PERSON><PERSON>@example.com'
    },
    
    // 订单统计
    orderCounts: {
      pending: 0,
      paid: 0,
      shipped: 0,
      completed: 0
    },

    // 工作台办公模块 - 重构后的模块配置
    workspaceModules: [
      {
        id: 'finance-management',
        title: '财务管理',
        description: '全部财务数据查看与管理',
        url: '/pages/workspace/finance/overview/overview',
        badge: 5,
        forRole: 'all' // 所有用户都可以看到，财务角色拥有最高权限
      },
      {
        id: 'approval-management',
        title: '审批管理',
        description: '审批流程与权限管理',
        url: '/pages/workspace/approval/pending/pending',
        badge: 0,
        forRole: ['admin', 'manager'] // 仅管理员和经理可见
      }
    ],

    // 功能菜单 - 四宫格样式
    menuItems: [
      {
        id: 'settings',
        title: '系统设置',
        subtitle: '个人偏好与应用设置',
        url: '/pages/profile/settings/settings',
        icon: '/images/icons/settings.png'
      },
      {
        id: 'help',
        title: '帮助中心',
        subtitle: '使用指南与常见问题',
        url: '/pages/profile/help/help',
        icon: '/images/icons/help.png'
      },
      {
        id: 'about',
        title: '关于我们',
        subtitle: '了解智慧养鹅系统',
        url: '/pages/profile/about/about',
        icon: '/images/icons/info.png'
      },
      {
        id: 'feedback',
        title: '意见反馈',
        subtitle: '您的建议让我们更好',
        url: '/pages/profile/feedback/feedback',
        icon: '/images/icons/feedback.png'
      }
    ]
  },

  onLoad: function (options) {
    // 页面加载时获取用户信息
    this.loadUserInfo();
    this.filterModulesByPermission();
    this.loadOrderCounts();
  },

  onShow: function () {
    // 页面显示时清理loading状态
    if (pageHooks && pageHooks.onShow) {
      pageHooks.onShow();
    }

    // 页面显示时重新加载用户信息和刷新模块权限
    this.loadUserInfo();
    this.filterModulesByPermission();
    this.updateWorkspaceBadges();
    this.loadOrderCounts(); // 刷新订单统计
  },

  onHide: function () {
    // 页面隐藏时清理loading状态
    if (pageHooks && pageHooks.onHide) {
      pageHooks.onHide();
    }
  },

  onUnload: function () {
    // 页面卸载时清理loading状态
    if (pageHooks && pageHooks.onUnload) {
      pageHooks.onUnload();
    }
  },

  // 加载订单统计
  loadOrderCounts: function () {
    try {
      // 初始化示例订单数据（仅用于开发测试）
      orderService.initSampleData();
      
      // 获取订单统计
      const orderCounts = orderService.getOrderCounts();
      
      this.setData({
        orderCounts: {
          pending: orderCounts.pending,
          paid: orderCounts.paid,
          shipped: orderCounts.shipped,
          completed: orderCounts.completed
        }
      });
      
      try { logger.debug && logger.debug('[Profile] 订单统计已更新', orderCounts); } catch(_) {}
    } catch (error) {
      console.error('加载订单统计失败:', error);
    }
  },

  // 加载用户信息
  loadUserInfo: function () {
    const app = getApp();
    let userInfo = app.globalData.userInfo || wx.getStorageSync('user_info');
    
    try { logger.debug && logger.debug('获取到的用户信息', userInfo); } catch(_) {}
    
    // 检查是否有有效的登录token
    const token = wx.getStorageSync('access_token');
    
    // 如果没有用户信息或信息不完整，且没有有效token，使用统一的模拟数据
    if (!userInfo || !userInfo.name) {
      if (!token) {
        // 无token时使用默认模拟数据
        userInfo = {
          avatar: IMAGES.DEFAULTS.AVATAR,
          name: '李经理',
          farmName: '智慧生态养鹅基地',
          role: '管理员',
          phone: '138****8888',
          email: '<EMAIL>'
        };
        
        // 保存到全局数据和本地存储
        getApp().globalData.userInfo = userInfo;
        wx.setStorageSync('user_info', userInfo);
        
        try { logger.debug && logger.debug('[Profile] 使用统一模拟用户信息', userInfo); } catch(_) {}
      } else {
        // 有token但无用户信息，等待服务器返回数据
        try { logger.debug && logger.debug('[Profile] 有token但无用户信息，等待服务器验证'); } catch(_) {}
        userInfo = {
          avatar: IMAGES.DEFAULTS.AVATAR,
          name: '加载中...',
          farmName: '智慧生态养鹅基地',
          role: '管理员'
        };
      }
    } else {
      // 确保头像有默认值  
      if (!userInfo.avatar) {
        userInfo.avatar = IMAGES.DEFAULTS.AVATAR;
      }
      try { logger.debug && logger.debug('[Profile] 使用已保存的用户信息', userInfo); } catch(_) {}
    }
    
    this.setData({
      userInfo: userInfo
    });
  },

  // 编辑个人资料
  onEditProfile: function() {
    wx.showActionSheet({
      itemList: ['编辑基本信息', '管理收货地址', '修改头像'],
      success: (res) => {
        switch(res.tapIndex) {
        case 0:
          this.editBasicInfo();
          break;
        case 1:
          this.editAddress();
          break;
        case 2:
          this.changeAvatar();
          break;
        }
      }
    });
  },

  // 编辑基本信息
  editBasicInfo: function() {
    const currentUserInfo = this.data.userInfo;
    wx.showModal({
      title: '编辑基本信息',
      editable: true,
      placeholderText: currentUserInfo.name,
      success: (res) => {
        if (res.confirm && res.content.trim()) {
          // 更新用户名
          const updatedUserInfo = {
            ...currentUserInfo,
            name: res.content.trim()
          };
          
          this.setData({
            userInfo: updatedUserInfo
          });
          
          // 保存到本地存储和全局数据
          getApp().globalData.userInfo = updatedUserInfo;
          wx.setStorageSync('user_info', updatedUserInfo);
          
          wx.showToast({
            title: '修改成功',
            icon: 'success'
          });
        }
      }
    });
  },

  // 编辑收货地址
  editAddress: function() {
    wx.navigateTo({
      url: '/pages/address/address',
      fail: () => {
        // 如果地址页面不存在，显示简单的地址管理
        this.showAddressModal();
      }
    });
  },

  // 显示地址管理弹窗
  showAddressModal: function() {
    const currentAddress = wx.getStorageSync('user_address') || '暂未设置收货地址';
    wx.showModal({
      title: '收货地址',
      content: `当前地址：${currentAddress}`,
      confirmText: '修改地址',
      success: (res) => {
        if (res.confirm) {
          this.editAddressInput();
        }
      }
    });
  },

  // 输入地址
  editAddressInput: function() {
    wx.showModal({
      title: '编辑收货地址',
      editable: true,
      placeholderText: '请输入详细收货地址',
      success: (res) => {
        if (res.confirm && res.content.trim()) {
          wx.setStorageSync('user_address', res.content.trim());
          wx.showToast({
            title: '地址已保存',
            icon: 'success'
          });
        }
      }
    });
  },

  // 修改头像
  changeAvatar: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePaths = res.tempFilePaths;
        if (tempFilePaths && tempFilePaths.length > 0) {
          const updatedUserInfo = {
            ...this.data.userInfo,
            avatar: tempFilePaths[0]
          };
          
          this.setData({
            userInfo: updatedUserInfo
          });
          
          // 保存到本地存储和全局数据
          getApp().globalData.userInfo = updatedUserInfo;
          wx.setStorageSync('user_info', updatedUserInfo);
          
          wx.showToast({
            title: '头像已更新',
            icon: 'success'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '取消选择',
          icon: 'none'
        });
      }
    });
  },

  // 根据权限过滤模块
  filterModulesByPermission: function() {
    const userInfo = getApp().globalData.userInfo || this.data.userInfo;
    const userRole = userInfo.role || 'user';
    
    // 获取角色映射 - 与新权限系统对应
    const roleMap = {
      '管理员': 'admin',
      '经理': 'manager', 
      '财务': 'finance',
      '普通员工': 'employee', // 使用employee替代user
      'admin': 'admin',
      'manager': 'manager',
      'finance': 'finance',
      'user': 'employee', // 向后兼容
      'employee': 'employee'
    };
    
    const currentRole = roleMap[userRole] || 'employee';
    
    // 过滤工作台模块
    const filteredWorkspaceModules = this.data.workspaceModules.filter(item => {
      if (item.forRole === 'all') {
        return true;
      }
      if (Array.isArray(item.forRole)) {
        return item.forRole.includes(currentRole);
      }
      return item.forRole === currentRole;
    });

    // 根据角色特别处理模块显示
    const processedWorkspaceModules = filteredWorkspaceModules.map(item => {
      if (item.id === 'finance-management') {
        // 根据角色调整财务模块描述
        switch (currentRole) {
          case 'admin':
          case 'manager':
            item.description = '全部财务数据查看与管理';
            break;
          case 'finance':
            item.description = '财务专业权限 - 审批与报表管理';
            break;
          default:
            item.description = '个人报销申请与查看';
        }
      }
      

      
      return item;
    });
    
    this.setData({
      workspaceModules: processedWorkspaceModules
    });
  },

  // 更新工作台模块徽章数量 - 重构后的模块
  updateWorkspaceBadges: function() {
    // 模拟从服务器获取待处理事项数量
    const badges = {
      'finance-management': wx.getStorageSync('pendingFinanceItems') || 5,
      'approval-management': wx.getStorageSync('pendingApprovalItems') || 0
    };

    const updatedWorkspaceModules = this.data.workspaceModules.map(module => ({
      ...module,
      badge: badges[module.id] || 0
    }));

    this.setData({
      workspaceModules: updatedWorkspaceModules
    });
  },

  // 工作台模块点击事件
  onWorkspaceTap: function(e) {
    const { id, url } = e.currentTarget.dataset;
    
    // 检查用户登录状态
    const token = wx.getStorageSync('access_token');
    const userInfo = getApp().globalData.userInfo || wx.getStorageSync('user_info');
    
    if (!token || !userInfo) {
      wx.showModal({
        title: '请先登录',
        content: '使用OA功能需要先登录系统',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.redirectTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }
    
    // 检查权限
    const currentModule = this.data.workspaceModules.find(module => module.id === id);
    if (currentModule && currentModule.forRole !== 'all') {
      const roleMap = {
        '管理员': 'admin',
        '经理': 'manager', 
        '财务': 'finance',
        '普通员工': 'employee' // 使用employee替代user
      };
      
      const currentRole = roleMap[userInfo.role] || 'employee';
      const requiredRoles = Array.isArray(currentModule.forRole) ? currentModule.forRole : [currentModule.forRole];
      
      if (!requiredRoles.includes(currentRole)) {
        // 为财务角色提供特殊说明
        let permissionMessage = `您的角色（${userInfo.role}）无权访问此功能\n\n需要权限：${requiredRoles.map(role => {
          switch(role) {
            case 'admin': return '管理员';
            case 'manager': return '经理';
            case 'finance': return '财务';
            case 'employee': return '普通员工';
            default: return role;
          }
        }).join('、')}`;
        
        if (currentRole === 'finance' && id !== 'finance-management') {
          permissionMessage += '\n\n提示：财务人员可以访问"财务管理"模块进行财务审批和报表管理。';
        }
        
        wx.showModal({
          title: '权限不足',
          content: permissionMessage,
          showCancel: false,
          confirmText: '知道了'
        });
        return;
      }
    }
    
    if (!url) {
      // 实现模块功能
      this.handleModuleTap({ id: id });
      return;
    }
    
    // 使用微信小程序原生导航
    wx.navigateTo({
      url: url,
      fail: () => {
        wx.showModal({
          title: '功能开发中',
          content: '该功能正在紧急开发中，敬请期待！',
          showCancel: false,
          confirmText: '知道了'
        });
      }
    });
  },

  // 菜单点击事件
  onMenuTap: function (e) {
    const { url, id, requiredrole } = e.currentTarget.dataset;
    
    // 检查权限
    if (requiredrole) {
      const userInfo = getApp().globalData.userInfo || {};
      const userRole = userInfo.role || 'user';
      const requiredRoles = requiredrole.split(',');
      
      if (!requiredRoles.includes(userRole)) {
        // 静默处理，不显示提示
        return;
      }
    }
    
    if (url) {
      // 使用微信小程序原生导航
      wx.navigateTo({
        url: url,
        fail: () => {
          wx.showToast({
            title: '页面暂未开放',
            icon: 'none'
          });
        }
      });
    } else {
      // 实现菜单项功能
      this.handleMenuItemTap(id);
    }
  },

  // 统计项点击事件
  onStatTap: function (e) {
    const { id } = e.currentTarget.dataset;
    switch (id) {
    case 'health-records':
      wx.switchTab({
        url: '/pages/health/health'
      });
      break;
    case 'production-records':
      wx.switchTab({
        url: '/pages/health/health?tab=3'
      });
      break;
    case 'materials':
      wx.switchTab({
        url: '/pages/health/health?tab=2'
      });
      break;
    case 'finance-records':
      wx.navigateTo({
        url: '/pages/production/finance/finance'
      });
      break;
    }
  },

  // 查看全部订单
  onViewAllOrders: function () {
    wx.navigateTo({
      url: '/pages/orders/orders'
    });
  },

  // 按状态查看订单
  onOrderStatus: function (e) {
    const status = e.currentTarget.dataset.status;
    wx.navigateTo({
      url: `/pages/orders/orders?status=${status}`
    });
  },

  // 退出登录
  onLogout: function () {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除用户信息
          wx.removeStorageSync('access_token');
          wx.removeStorageSync('refresh_token');
          wx.removeStorageSync('user_info');
          
          // 清除全局数据
          getApp().globalData.userInfo = null;

          // 跳转到登录页
          wx.redirectTo({
            url: '/pages/login/login'
          });
        }
      }
    });
  },

  // ==================== 新增功能实现 ====================

  // 处理模块点击
  handleModuleTap: function(module) {
    if (!module) return;
    
    const moduleActions = {
      production: () => this.showProductionFeatures(),
      health: () => this.showHealthFeatures(),
      environment: () => this.showEnvironmentFeatures(),
      finance: () => this.showFinanceFeatures(),
      oa: () => this.showOAFeatures(),
      shop: () => this.showShopFeatures()
    };

    const action = moduleActions[module.id];
    if (action) {
      action();
    } else {
      wx.showModal({
        title: module.title,
        content: `${module.title}功能模块包含了丰富的功能，正在为您展示详细信息...`,
        showCancel: false
      });
    }
  },

  // 处理菜单项点击
  handleMenuItemTap: function(id) {
    const menuActions = {
      address: () => this.showAddressManagement(),
      settings: () => this.showSystemSettings(),
      help: () => this.showHelpCenter(),
      about: () => this.showAboutUs(),
      feedback: () => this.showFeedbackForm()
    };

    const action = menuActions[id];
    if (action) {
      action();
    } else {
      wx.showToast({
        title: '功能正在完善中',
        icon: 'none'
      });
    }
  },

  // 显示生产功能
  showProductionFeatures: function() {
    wx.showActionSheet({
      itemList: ['鹅群管理', '环境监控', '生产记录', '生产管理', '物料管理'],
      success: (res) => {
        const features = [
          '鹅群管理功能包含鹅群信息记录、健康状况跟踪、生长数据分析等。',
          '环境监控功能提供实时温湿度监测、环境质量分析、报警提醒等。',
          '生产记录功能帮助记录日常生产活动、产蛋数据、饲料消耗等。',
          '生产管理功能包含疾病预防、健康检查、用药记录、AI诊断等。',
          '物料管理功能提供库存管理、供应商管理等服务。'
        ];
        
        wx.showModal({
          title: '功能详情',
          content: features[res.tapIndex],
          showCancel: false
        });
      }
    });
  },

  // 显示健康功能
  showHealthFeatures: function() {
    wx.showModal({
      title: '生产管理功能',
      content: '• 健康档案管理\n• AI智能诊断\n• 疾病预防提醒\n• 用药记录跟踪\n• 健康趋势分析\n• 专家咨询服务',
      showCancel: true,
      confirmText: '了解更多'
    });
  },

  // 显示环境功能
  showEnvironmentFeatures: function() {
    wx.showModal({
      title: '环境监控功能',
      content: '• 实时环境数据监测\n• 温湿度自动调节\n• 空气质量分析\n• 环境异常报警\n• 历史数据查询\n• 环境优化建议',
      showCancel: true,
      confirmText: '查看详情'
    });
  },

  // 显示财务功能
  showFinanceFeatures: function() {
    wx.showModal({
      title: '财务管理功能',
      content: '• 收支记录管理\n• 财务报表生成\n• 成本分析计算\n• 利润统计分析\n• 预算管理规划\n• 财务预警提醒',
      showCancel: true,
      confirmText: '查看报表'
    });
  },

  // 显示工作台功能
  showWorkspaceFeatures: function() {
    wx.showModal({
      title: '工作台功能',
      content: '• 财务管理系统\n• 费用报销申请\n• 付款申请处理\n• 合同申请管理\n• 活动经费申请\n• 备用金管理',
      showCancel: true,
      confirmText: '进入工作台'
    });
  },

  // 显示商城功能
  showShopFeatures: function() {
    wx.showModal({
      title: '商城功能',
      content: '• 养殖用品购买\n• 优质饲料选购\n• 设备工具购买\n• 专业药品购买\n• 订单管理跟踪\n• 优惠活动参与',
      showCancel: true,
      confirmText: '立即购买'
    });
  },

  // 显示地址管理
  showAddressManagement: function() {
    wx.showModal({
      title: '地址管理',
      content: '管理您的收货地址：\n• 添加新地址\n• 编辑现有地址\n• 设置默认地址\n• 删除不用地址',
      showCancel: true,
      confirmText: '管理地址',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/address/address'
          });
        }
      }
    });
  },

  // 显示系统设置
  showSystemSettings: function() {
    wx.showModal({
      title: '系统设置',
      content: '个性化系统设置：\n• 通知偏好设置\n• 界面主题选择\n• 语言偏好设置\n• 数据同步设置',
      showCancel: true,
      confirmText: '前往设置',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/profile/settings/settings'
          });
        }
      }
    });
  },

  // 显示帮助中心
  showHelpCenter: function() {
    wx.showModal({
      title: '帮助中心',
      content: '获取使用帮助：\n• 功能使用指南\n• 常见问题解答\n• 视频教程观看\n• 联系客服支持',
      showCancel: true,
      confirmText: '获取帮助',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/profile/help/help'
          });
        }
      }
    });
  },

  // 显示关于我们
  showAboutUs: function() {
    wx.showModal({
      title: '关于智慧养鹅',
      content: '智慧养鹅系统v2.9.2\n\n专业的智能养殖管理平台\n提供全方位的养殖解决方案\n\n© 2024 智慧养鹅团队',
      showCancel: true,
      confirmText: '了解更多'
    });
  },

  // 显示反馈表单
  showFeedbackForm: function() {
    wx.showModal({
      title: '意见反馈',
      content: '您的建议对我们很重要：\n• 功能改进建议\n• 使用问题反馈\n• 新功能需求\n• 服务质量评价',
      showCancel: true,
      confirmText: '提交反馈',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/profile/feedback/feedback'
          });
        }
      }
    });
  }
});