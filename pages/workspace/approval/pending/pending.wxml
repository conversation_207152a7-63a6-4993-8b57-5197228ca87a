<!--pages/workspace/approval/pending/pending.wxml-->
<view class="container">
  <!-- 头部标题 -->
  <view class="header">
    <text class="title">待审批</text>
    <text class="subtitle">处理财务相关申请的审批</text>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading && pendingList.length === 0}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view wx:elif="{{!loading && pendingList.length === 0}}" class="empty-container">
    <image class="empty-icon" src="/assets/icons/empty-approval.svg" mode="aspectFit"></image>
    <text class="empty-text">暂无待审批事项</text>
    <text class="empty-desc">所有申请都已处理完成</text>
  </view>

  <!-- 审批列表 -->
  <view wx:else class="approval-list">
    <view
      wx:for="{{pendingList}}"
      wx:key="id"
      class="approval-item"
      bindtap="onViewDetail"
      data-id="{{item.id}}"
      data-type="{{item.business_type}}"
    >
      <!-- 申请信息 -->
      <view class="item-header">
        <view class="item-title">
          <text class="business-type">{{businessTypeMap[item.business_type]}}</text>
          <text wx:if="{{item.isUrgent}}" class="urgent-tag">紧急</text>
        </view>
        <text class="item-amount" wx:if="{{item.amount}}">¥{{item.amount}}</text>
      </view>

      <view class="item-content">
        <text class="item-desc">{{item.title || item.description}}</text>
        <view class="item-meta">
          <text class="applicant">申请人：{{item.applicant_name}}</text>
          <text class="apply-time">{{item.created_at}}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="item-actions" catchtap="stopPropagation">
        <button
          class="action-btn reject-btn"
          size="mini"
          bindtap="onApprovalAction"
          data-id="{{item.id}}"
          data-action="reject"
        >
          拒绝
        </button>
        <button
          class="action-btn approve-btn"
          size="mini"
          bindtap="onApprovalAction"
          data-id="{{item.id}}"
          data-action="approve"
        >
          通过
        </button>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view wx:if="{{loading && pendingList.length > 0}}" class="load-more">
    <text>加载中...</text>
  </view>

  <view wx:elif="{{!hasMore && pendingList.length > 0}}" class="load-more">
    <text>没有更多了</text>
  </view>
</view>