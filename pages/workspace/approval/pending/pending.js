// pages/workspace/approval/pending/pending.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    pendingList: [],
    loading: true,
    refreshing: false,
    hasMore: true,
    page: 1,
    limit: 10,

    // 业务类型映射
    businessTypeMap: {
      'expense': '费用报销',
      'purchase': '采购申请',
      'payment': '付款申请',
      'contract': '合同申请',
      'activity': '活动经费',
      'reserve': '备用金'
    },

    // 状态映射
    statusMap: {
      'pending': '待审批',
      'processing': '审批中',
      'approved': '已通过',
      'rejected': '已拒绝'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadPendingApprovals();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 刷新数据
    this.refreshData();
  },

  /**
   * 加载待审批列表
   */
  async loadPendingApprovals() {
    if (!this.data.hasMore && this.data.page > 1) return;

    try {
      this.setData({ loading: true });

      const response = await request.get(API.API_ENDPOINTS.WORKSPACE.APPROVALS.PENDING, {
        page: this.data.page,
        limit: this.data.limit
      });

      if (response.success) {
        const newList = this.data.page === 1 ? response.data.list : [...this.data.pendingList, ...response.data.list];

        this.setData({
          pendingList: newList,
          hasMore: response.data.pagination.page * response.data.pagination.limit < response.data.pagination.total,
          loading: false
        });
      } else {
        throw new Error(response.message || '加载失败');
      }
    } catch (error) {
      console.error('加载待审批列表失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({
      page: 1,
      hasMore: true,
      refreshing: true
    });

    await this.loadPendingApprovals();
    this.setData({ refreshing: false });
  },

  /**
   * 审批操作
   */
  async onApprovalAction(e) {
    const { id, action } = e.currentTarget.dataset;
    const item = this.data.pendingList.find(item => item.id === id);

    if (!item) return;

    const actionText = action === 'approve' ? '通过' : '拒绝';

    wx.showModal({
      title: `确认${actionText}`,
      content: `确定要${actionText}这个${this.data.businessTypeMap[item.business_type]}申请吗？`,
      success: async (res) => {
        if (res.confirm) {
          await this.processApproval(id, action);
        }
      }
    });
  },

  /**
   * 处理审批
   */
  async processApproval(id, action) {
    try {
      wx.showLoading({ title: '处理中...' });

      const response = await request.post(`${API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.APPROVE(id)}`, {
        action,
        comments: ''
      });

      wx.hideLoading();

      if (response.success) {
        wx.showToast({
          title: action === 'approve' ? '审批通过' : '审批拒绝',
          icon: 'success'
        });

        // 刷新列表
        this.refreshData();
      } else {
        throw new Error(response.message || '操作失败');
      }
    } catch (error) {
      wx.hideLoading();
      console.error('审批操作失败:', error);
      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 查看详情
   */
  onViewDetail(e) {
    const { id, type } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/workspace/${type}/detail/detail?id=${id}`
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.setData({
        page: this.data.page + 1
      });
      this.loadPendingApprovals();
    }
  }
})