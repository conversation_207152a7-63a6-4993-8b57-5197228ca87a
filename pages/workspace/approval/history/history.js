// pages/workspace/approval/history/history.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    historyList: [],
    loading: true,
    refreshing: false,
    hasMore: true,
    page: 1,
    limit: 10,

    // 业务类型映射
    businessTypeMap: {
      'expense': '费用报销',
      'purchase': '采购申请',
      'payment': '付款申请',
      'contract': '合同申请',
      'activity': '活动经费',
      'reserve': '备用金'
    },

    // 状态映射
    statusMap: {
      'approved': '已通过',
      'rejected': '已拒绝',
      'cancelled': '已取消'
    },

    // 状态颜色映射
    statusColorMap: {
      'approved': '#52c41a',
      'rejected': '#ff4d4f',
      'cancelled': '#d9d9d9'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadApprovalHistory();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 刷新数据
    this.refreshData();
  },

  /**
   * 加载审批历史
   */
  async loadApprovalHistory() {
    if (!this.data.hasMore && this.data.page > 1) return;

    try {
      this.setData({ loading: true });

      const response = await request.get(API.API_ENDPOINTS.WORKSPACE.APPROVALS.HISTORY, {
        page: this.data.page,
        limit: this.data.limit
      });

      if (response.success) {
        const newList = this.data.page === 1 ? response.data.list : [...this.data.historyList, ...response.data.list];

        this.setData({
          historyList: newList,
          hasMore: response.data.pagination.page * response.data.pagination.limit < response.data.pagination.total,
          loading: false
        });
      } else {
        throw new Error(response.message || '加载失败');
      }
    } catch (error) {
      console.error('加载审批历史失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({
      page: 1,
      hasMore: true,
      refreshing: true
    });

    await this.loadApprovalHistory();
    this.setData({ refreshing: false });
  },

  /**
   * 查看详情
   */
  onViewDetail(e) {
    const { id, type } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/workspace/${type}/detail/detail?id=${id}`
    });
  },

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前';
    } else {
      return time.toLocaleDateString();
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.setData({
        page: this.data.page + 1
      });
      this.loadApprovalHistory();
    }
  }
})