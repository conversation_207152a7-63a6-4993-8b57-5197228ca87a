<!--pages/workspace/approval/history/history.wxml-->
<view class="container">
  <!-- 头部标题 -->
  <view class="header">
    <text class="title">审批历史</text>
    <text class="subtitle">查看已处理的财务申请记录</text>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading && historyList.length === 0}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view wx:elif="{{!loading && historyList.length === 0}}" class="empty-container">
    <image class="empty-icon" src="/assets/icons/empty-history.svg" mode="aspectFit"></image>
    <text class="empty-text">暂无审批记录</text>
    <text class="empty-desc">还没有处理过任何申请</text>
  </view>

  <!-- 历史列表 -->
  <view wx:else class="history-list">
    <view
      wx:for="{{historyList}}"
      wx:key="id"
      class="history-item"
      bindtap="onViewDetail"
      data-id="{{item.id}}"
      data-type="{{item.business_type}}"
    >
      <!-- 申请信息 -->
      <view class="item-header">
        <view class="item-title">
          <text class="business-type">{{businessTypeMap[item.business_type]}}</text>
          <view class="status-tag" style="background-color: {{statusColorMap[item.status]}}">
            {{statusMap[item.status]}}
          </view>
        </view>
        <text class="item-amount" wx:if="{{item.amount}}">¥{{item.amount}}</text>
      </view>

      <view class="item-content">
        <text class="item-desc">{{item.title || item.description}}</text>
        <view class="item-meta">
          <text class="applicant">申请人：{{item.applicant_name}}</text>
          <text class="approval-time">处理时间：{{item.approvalTime || item.created_at}}</text>
        </view>
      </view>

      <!-- 审批信息 -->
      <view class="approval-info" wx:if="{{item.approver_name || item.comments}}">
        <text class="approver" wx:if="{{item.approver_name}}">审批人：{{item.approver_name}}</text>
        <text class="comments" wx:if="{{item.comments}}">备注：{{item.comments}}</text>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view wx:if="{{loading && historyList.length > 0}}" class="load-more">
    <text>加载中...</text>
  </view>

  <view wx:elif="{{!hasMore && historyList.length > 0}}" class="load-more">
    <text>没有更多了</text>
  </view>
</view>