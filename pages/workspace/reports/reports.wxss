/* pages/workspace/reports/reports.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e6e6e6;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #999;
  font-size: 28rpx;
}

/* 报表内容 */
.reports-content {
  padding: 20rpx;
}

/* 筛选区域 */
.filter-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.date-filter {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.filter-item {
  flex: 1;
  margin: 0 10rpx;
}

.filter-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.date-picker {
  border: 2rpx solid #e6e6e6;
  border-radius: 8rpx;
  padding: 20rpx;
  background: #fafafa;
}

.picker-text {
  font-size: 30rpx;
  color: #333;
}

/* 报表类型切换 */
.report-types {
  display: flex;
  justify-content: space-between;
}

.type-tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 20rpx;
  margin: 0 8rpx;
  border: 2rpx solid #e6e6e6;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;
}

.type-tab.active {
  background: rgba(24, 144, 255, 0.1);
  font-weight: bold;
}

/* 汇总统计 */
.summary-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.summary-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.summary-item {
  text-align: center;
  padding: 20rpx;
  background: #fafafa;
  border-radius: 12rpx;
}

.summary-label {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.summary-value {
  font-size: 32rpx;
  font-weight: bold;
}

.summary-value.income {
  color: #52c41a;
}

.summary-value.expense {
  color: #ff4d4f;
}

.summary-value.pending {
  color: #faad14;
}

.summary-value.approved {
  color: #1890ff;
}

/* 图表区域 */
.chart-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.chart-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

/* 月度统计图表 */
.monthly-chart {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 200rpx;
  padding: 20rpx 0;
}

.month-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 5rpx;
}

.month-bar {
  width: 40rpx;
  height: 120rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  position: relative;
  margin-bottom: 10rpx;
}

.bar-fill {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-radius: 4rpx;
  transition: height 0.3s;
}

.month-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.month-amount {
  font-size: 22rpx;
  color: #333;
  font-weight: bold;
}

/* 分类统计 */
.category-list {
  space-y: 20rpx;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.category-item:last-child {
  border-bottom: none;
}

.category-info {
  flex: 1;
}

.category-name {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.category-count {
  font-size: 24rpx;
  color: #666;
  margin-left: 10rpx;
}

.category-amount {
  text-align: right;
  min-width: 200rpx;
}

.amount-text {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.progress-bar {
  width: 150rpx;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  margin-top: 8rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.3s;
}

/* 趋势分析 */
.trend-chart {
  max-height: 400rpx;
  overflow-y: auto;
}

.trend-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.trend-item:last-child {
  border-bottom: none;
}

.trend-date {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  min-width: 120rpx;
}

.trend-values {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.trend-income {
  font-size: 26rpx;
  color: #52c41a;
  margin-bottom: 4rpx;
}

.trend-expense {
  font-size: 26rpx;
  color: #ff4d4f;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.3;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #ccc;
}

/* 操作按钮 */
.action-buttons {
  padding: 40rpx 0;
  text-align: center;
}

.export-btn {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 60rpx;
  font-size: 30rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
}
