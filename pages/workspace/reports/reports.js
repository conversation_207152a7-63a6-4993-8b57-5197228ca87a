// pages/workspace/reports/reports.js
const { API } = require('../../../constants/index.js');
const request = require('../../../utils/request.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    
    // 报表数据
    reportData: {
      monthlyStats: [],
      categoryStats: [],
      trendData: [],
      summary: {
        totalIncome: 0,
        totalExpense: 0,
        totalPending: 0,
        totalApproved: 0
      }
    },
    
    // 时间筛选
    dateRange: {
      startDate: '',
      endDate: ''
    },
    
    // 报表类型
    reportTypes: [
      { value: 'expense', label: '费用报表', color: '#ff4d4f' },
      { value: 'income', label: '收入报表', color: '#52c41a' },
      { value: 'category', label: '分类统计', color: '#1890ff' },
      { value: 'trend', label: '趋势分析', color: '#722ed1' }
    ],
    
    currentReportType: 'expense'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 设置默认时间范围（最近3个月）
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 3);
    
    this.setData({
      'dateRange.startDate': this.formatDate(startDate),
      'dateRange.endDate': this.formatDate(endDate)
    });
    
    this.loadReportData();
  },

  /**
   * 加载报表数据
   */
  async loadReportData() {
    try {
      this.setData({ loading: true });
      
      const { dateRange, currentReportType } = this.data;
      
      const response = await request.get(API.API_ENDPOINTS.WORKSPACE.FINANCE.REPORTS, {
        type: currentReportType,
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      });
      
      if (response.success) {
        this.setData({
          reportData: response.data,
          loading: false
        });
      } else {
        throw new Error(response.message || '加载失败');
      }
    } catch (error) {
      console.error('加载财务报表失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 报表类型切换
   */
  onReportTypeChange(e) {
    const { type } = e.currentTarget.dataset;
    this.setData({
      currentReportType: type
    });
    this.loadReportData();
  },

  /**
   * 开始日期选择
   */
  onStartDateChange(e) {
    this.setData({
      'dateRange.startDate': e.detail.value
    });
    this.loadReportData();
  },

  /**
   * 结束日期选择
   */
  onEndDateChange(e) {
    this.setData({
      'dateRange.endDate': e.detail.value
    });
    this.loadReportData();
  },

  /**
   * 导出报表
   */
  onExportReport() {
    wx.showToast({
      title: '导出功能开发中',
      icon: 'none'
    });
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 格式化金额
   */
  formatAmount(amount) {
    return (amount || 0).toLocaleString();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadReportData().then(() => {
      wx.stopPullDownRefresh();
    });
  }
});
