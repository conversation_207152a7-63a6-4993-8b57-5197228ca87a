<!--pages/workspace/reports/reports.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 报表内容 -->
  <view wx:else class="reports-content">
    <!-- 头部筛选 -->
    <view class="filter-section">
      <view class="date-filter">
        <view class="filter-item">
          <text class="filter-label">开始日期</text>
          <picker 
            mode="date" 
            value="{{dateRange.startDate}}" 
            bindchange="onStartDateChange"
            class="date-picker"
          >
            <view class="picker-text">{{dateRange.startDate}}</view>
          </picker>
        </view>
        <view class="filter-item">
          <text class="filter-label">结束日期</text>
          <picker 
            mode="date" 
            value="{{dateRange.endDate}}" 
            bindchange="onEndDateChange"
            class="date-picker"
          >
            <view class="picker-text">{{dateRange.endDate}}</view>
          </picker>
        </view>
      </view>
      
      <!-- 报表类型切换 -->
      <view class="report-types">
        <view 
          wx:for="{{reportTypes}}" 
          wx:key="value"
          class="type-tab {{currentReportType === item.value ? 'active' : ''}}"
          style="border-color: {{item.color}}; color: {{currentReportType === item.value ? item.color : '#666'}}"
          bindtap="onReportTypeChange"
          data-type="{{item.value}}"
        >
          {{item.label}}
        </view>
      </view>
    </view>

    <!-- 汇总统计 -->
    <view class="summary-section">
      <view class="summary-title">财务汇总</view>
      <view class="summary-grid">
        <view class="summary-item">
          <text class="summary-label">总收入</text>
          <text class="summary-value income">¥{{formatAmount(reportData.summary.totalIncome)}}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">总支出</text>
          <text class="summary-value expense">¥{{formatAmount(reportData.summary.totalExpense)}}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">待审批</text>
          <text class="summary-value pending">¥{{formatAmount(reportData.summary.totalPending)}}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">已通过</text>
          <text class="summary-value approved">¥{{formatAmount(reportData.summary.totalApproved)}}</text>
        </view>
      </view>
    </view>

    <!-- 月度统计 -->
    <view class="chart-section" wx:if="{{reportData.monthlyStats.length > 0}}">
      <view class="chart-title">月度统计</view>
      <view class="monthly-chart">
        <view wx:for="{{reportData.monthlyStats}}" wx:key="month" class="month-item">
          <view class="month-bar">
            <view 
              class="bar-fill" 
              style="height: {{(item.amount / reportData.monthlyStats[0].amount) * 100}}%; background-color: #1890ff;"
            ></view>
          </view>
          <text class="month-label">{{item.month}}</text>
          <text class="month-amount">¥{{formatAmount(item.amount)}}</text>
        </view>
      </view>
    </view>

    <!-- 分类统计 -->
    <view class="chart-section" wx:if="{{reportData.categoryStats.length > 0}}">
      <view class="chart-title">分类统计</view>
      <view class="category-list">
        <view wx:for="{{reportData.categoryStats}}" wx:key="category" class="category-item">
          <view class="category-info">
            <text class="category-name">{{item.categoryName}}</text>
            <text class="category-count">{{item.count}}笔</text>
          </view>
          <view class="category-amount">
            <text class="amount-text">¥{{formatAmount(item.amount)}}</text>
            <view class="progress-bar">
              <view 
                class="progress-fill" 
                style="width: {{(item.amount / reportData.categoryStats[0].amount) * 100}}%; background-color: {{item.color || '#1890ff'}};"
              ></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 趋势分析 -->
    <view class="chart-section" wx:if="{{reportData.trendData.length > 0}}">
      <view class="chart-title">趋势分析</view>
      <view class="trend-chart">
        <view wx:for="{{reportData.trendData}}" wx:key="date" class="trend-item">
          <text class="trend-date">{{item.date}}</text>
          <view class="trend-values">
            <text class="trend-income">收入: ¥{{formatAmount(item.income)}}</text>
            <text class="trend-expense">支出: ¥{{formatAmount(item.expense)}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{reportData.monthlyStats.length === 0 && reportData.categoryStats.length === 0}}" class="empty-state">
      <image class="empty-icon" src="/assets/icons/report.svg" mode="aspectFit"></image>
      <text class="empty-text">暂无报表数据</text>
      <text class="empty-desc">请选择其他时间范围或报表类型</text>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="export-btn" bindtap="onExportReport">
        导出报表
      </button>
    </view>
  </view>
</view>
