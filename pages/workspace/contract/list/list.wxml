<!--pages/workspace/contract/list/list.wxml-->
<view class="container">
  <!-- 头部标题 -->
  <view class="header">
    <text class="title">合同申请</text>
    <text class="subtitle">管理和查看合同申请记录</text>
  </view>

  <!-- 新建按钮 -->
  <view class="create-btn-container">
    <button class="create-btn" bindtap="onCreateContract">
      <text class="create-btn-text">+ 新建合同申请</text>
    </button>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading && contractList.length === 0}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view wx:elif="{{!loading && contractList.length === 0}}" class="empty-container">
    <image class="empty-icon" src="/assets/icons/contract.svg" mode="aspectFit"></image>
    <text class="empty-text">暂无合同申请记录</text>
    <text class="empty-desc">点击上方按钮创建第一个合同申请</text>
  </view>

  <!-- 合同申请列表 -->
  <view wx:else class="contract-list">
    <view 
      wx:for="{{contractList}}" 
      wx:key="id" 
      class="contract-item"
      bindtap="onViewDetail"
      data-id="{{item.id}}"
    >
      <!-- 申请信息 -->
      <view class="item-header">
        <view class="item-title">
          <text class="contract-title">{{item.title || '合同申请'}}</text>
          <view class="status-tag" style="background-color: {{statusColorMap[item.status]}}">
            {{statusMap[item.status]}}
          </view>
        </view>
        <text class="item-amount">¥{{item.amount}}</text>
      </view>

      <view class="item-content">
        <text class="item-desc">{{item.description || item.category}}</text>
        <view class="item-meta">
          <text class="apply-time">申请时间：{{item.created_at}}</text>
          <text class="applicant" wx:if="{{item.applicant_name}}">申请人：{{item.applicant_name}}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="item-actions" wx:if="{{item.status === 'draft'}}">
        <button class="action-btn edit-btn" size="mini" catchtap="onEditContract" data-id="{{item.id}}">
          编辑
        </button>
        <button class="action-btn submit-btn" size="mini" catchtap="onSubmitContract" data-id="{{item.id}}">
          提交
        </button>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view wx:if="{{loading && contractList.length > 0}}" class="load-more">
    <text>加载中...</text>
  </view>
  
  <view wx:elif="{{!hasMore && contractList.length > 0}}" class="load-more">
    <text>没有更多了</text>
  </view>
</view>