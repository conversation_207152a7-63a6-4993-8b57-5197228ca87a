// pages/workspace/contract/detail/detail.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');

Page({
  data: {
    contractId: '',
    contractDetail: null,
    loading: true,
    
    statusMap: {
      'draft': '草稿',
      'pending': '待审批',
      'processing': '审批中',
      'approved': '已通过',
      'rejected': '已拒绝',
      'cancelled': '已取消'
    },
    
    statusColorMap: {
      'draft': '#d9d9d9',
      'pending': '#faad14',
      'processing': '#1890ff',
      'approved': '#52c41a',
      'rejected': '#ff4d4f',
      'cancelled': '#d9d9d9'
    },
    
    approvalFlow: [],
    attachments: []
  },

  onLoad(options) {
    if (options.id) {
      this.setData({ contractId: options.id });
      this.loadContractDetail();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  async loadContractDetail() {
    try {
      this.setData({ loading: true });
      
      const response = await request.get(API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.DETAIL(this.data.contractId));
      
      if (response.success) {
        this.setData({
          contractDetail: response.data,
          approvalFlow: response.data.approval_flow || [],
          attachments: response.data.attachments || [],
          loading: false
        });
        
        wx.setNavigationBarTitle({
          title: response.data.title || '合同申请详情'
        });
      } else {
        throw new Error(response.message || '加载失败');
      }
    } catch (error) {
      console.error('加载合同申请详情失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  onEditContract() {
    wx.navigateTo({
      url: `/pages/workspace/contract/apply/apply?id=${this.data.contractId}&mode=edit`
    });
  },

  async onSubmitContract() {
    try {
      wx.showLoading({ title: '提交中...' });
      
      const response = await request.post(API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.SUBMIT(this.data.contractId));
      
      if (response.success) {
        wx.showToast({
          title: '提交成功',
          icon: 'success'
        });
        
        this.loadContractDetail();
      } else {
        throw new Error(response.message || '提交失败');
      }
    } catch (error) {
      console.error('提交合同申请失败:', error);
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  async onCancelContract() {
    const result = await wx.showModal({
      title: '确认撤销',
      content: '确定要撤销这个合同申请吗？',
      confirmText: '撤销',
      confirmColor: '#722ed1'
    });
    
    if (!result.confirm) return;
    
    try {
      wx.showLoading({ title: '撤销中...' });
      
      const response = await request.post(API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.CANCEL(this.data.contractId));
      
      if (response.success) {
        wx.showToast({
          title: '撤销成功',
          icon: 'success'
        });
        
        this.loadContractDetail();
      } else {
        throw new Error(response.message || '撤销失败');
      }
    } catch (error) {
      console.error('撤销合同申请失败:', error);
      wx.showToast({
        title: error.message || '撤销失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  onPreviewAttachment(e) {
    const { url, type } = e.currentTarget.dataset;
    
    if (type === 'image') {
      wx.previewImage({
        current: url,
        urls: this.data.attachments.filter(item => item.type === 'image').map(item => item.url)
      });
    } else {
      wx.downloadFile({
        url: url,
        success: (res) => {
          wx.openDocument({
            filePath: res.tempFilePath
          });
        }
      });
    }
  },

  formatTime(timeStr) {
    if (!timeStr) return '';
    const time = new Date(timeStr);
    return time.toLocaleString();
  },

  onPullDownRefresh() {
    this.loadContractDetail().then(() => {
      wx.stopPullDownRefresh();
    });
  }
});