// pages/workspace/contract/approve/approve.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    contractId: '',
    contractDetail: null,
    loading: true,

    // 审批表单
    approvalForm: {
      action: '', // approve | reject
      comment: ''
    },

    // 状态映射
    statusMap: {
      'draft': '草稿',
      'pending': '待审批',
      'processing': '审批中',
      'approved': '已通过',
      'rejected': '已拒绝',
      'cancelled': '已取消'
    },

    // 状态颜色映射
    statusColorMap: {
      'draft': '#d9d9d9',
      'pending': '#faad14',
      'processing': '#1890ff',
      'approved': '#52c41a',
      'rejected': '#ff4d4f',
      'cancelled': '#d9d9d9'
    },

    // 审批流程
    approvalFlow: [],

    // 附件列表
    attachments: [],

    // 提交状态
    submitting: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.id) {
      this.setData({ contractId: options.id });
      this.loadContractDetail();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 加载合同申请详情
   */
  async loadContractDetail() {
    try {
      this.setData({ loading: true });

      const response = await request.get(API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.DETAIL(this.data.contractId));

      if (response.success) {
        this.setData({
          contractDetail: response.data,
          approvalFlow: response.data.approval_flow || [],
          attachments: response.data.attachments || [],
          loading: false
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: '审批合同申请'
        });
      } else {
        throw new Error(response.message || '加载失败');
      }
    } catch (error) {
      console.error('加载合同申请详情失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 审批意见输入
   */
  onCommentInput(e) {
    this.setData({
      'approvalForm.comment': e.detail.value
    });
  },

  /**
   * 通过申请
   */
  async onApprove() {
    this.setData({
      'approvalForm.action': 'approve'
    });

    await this.submitApproval();
  },

  /**
   * 拒绝申请
   */
  async onReject() {
    if (!this.data.approvalForm.comment.trim()) {
      wx.showToast({
        title: '拒绝申请时必须填写审批意见',
        icon: 'none'
      });
      return;
    }

    this.setData({
      'approvalForm.action': 'reject'
    });

    await this.submitApproval();
  },

  /**
   * 提交审批
   */
  async submitApproval() {
    const { approvalForm } = this.data;

    try {
      this.setData({ submitting: true });

      const submitData = {
        action: approvalForm.action,
        comment: approvalForm.comment.trim()
      };

      const response = await request.post(
        API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.APPROVE(this.data.contractId),
        submitData
      );

      if (response.success) {
        wx.showToast({
          title: approvalForm.action === 'approve' ? '审批通过' : '审批拒绝',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(response.message || '审批失败');
      }
    } catch (error) {
      console.error('提交审批失败:', error);
      wx.showToast({
        title: error.message || '审批失败',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 预览附件
   */
  onPreviewAttachment(e) {
    const { url, type } = e.currentTarget.dataset;

    if (type === 'image') {
      wx.previewImage({
        current: url,
        urls: this.data.attachments.filter(item => item.type === 'image').map(item => item.url)
      });
    } else {
      wx.downloadFile({
        url: url,
        success: (res) => {
          wx.openDocument({
            filePath: res.tempFilePath
          });
        }
      });
    }
  },

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    if (!timeStr) return '';
    const time = new Date(timeStr);
    return time.toLocaleString();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadContractDetail().then(() => {
      wx.stopPullDownRefresh();
    });
  }
})