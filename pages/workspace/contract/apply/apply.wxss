/* pages/workspace/contract/apply/apply.wxss */

/* ==== 基础布局 ==== */
.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #1890ff 0%, #4096ff 100%);
  min-height: 100vh;
}

/* ==== 加载状态 ==== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  background: white;
  border-radius: 16rpx;
  margin: 40rpx 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* ==== 表单内容 ==== */
.form-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.form-section {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.12);
  margin-bottom: 8rpx;
  border: 1rpx solid rgba(24, 144, 255, 0.08);
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #1890ff;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #1890ff, #4096ff);
  border-radius: 2rpx;
}

.section-subtitle {
  font-size: 24rpx;
  color: #95a5a6;
  font-weight: normal;
  margin-left: 8rpx;
}

/* ==== 表单项 ==== */
.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.item-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-text {
  font-size: 30rpx;
  color: #34495e;
  font-weight: 500;
}

.required {
  color: #e74c3c;
  margin-left: 6rpx;
  font-size: 28rpx;
  font-weight: bold;
}

/* ==== 输入框样式 ==== */
.form-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e8ecf0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #2c3e50;
  background: #fafbfc;
  box-sizing: border-box;
  min-height: 72rpx;
  line-height: 1.5;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-input:focus {
  border-color: #1890ff;
  background: white;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1);
  outline: none;
}

.form-input.error {
  border-color: #e74c3c;
  background: #fdf2f2;
  box-shadow: 0 0 0 4rpx rgba(231, 76, 60, 0.1);
}

/* ==== 金额输入框 ==== */
.amount-input-container {
  position: relative;
  display: flex;
  align-items: center;
  border: 2rpx solid #e8ecf0;
  border-radius: 16rpx;
  background: #fafbfc;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.amount-input-container:focus-within {
  border-color: #1890ff;
  background: white;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1);
}

.currency-symbol {
  padding: 24rpx 0 24rpx 20rpx;
  color: #1890ff;
  font-size: 32rpx;
  font-weight: 600;
}

.amount-input {
  border: none !important;
  background: transparent !important;
  padding: 24rpx 20rpx 24rpx 8rpx !important;
  box-shadow: none !important;
  font-size: 32rpx;
  font-weight: 500;
  color: #27ae60;
}

/* ==== 选择器样式增强 ==== */
.form-picker {
  width: 100%;
  border: 2rpx solid #e8ecf0;
  border-radius: 12rpx;
  background: #fafbfc;
  box-sizing: border-box;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.form-picker:active {
  border-color: #1890ff;
  background: white;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1);
}

.form-picker.error {
  border-color: #e74c3c;
  background: #fdf2f2;
  box-shadow: 0 0 0 4rpx rgba(231, 76, 60, 0.1);
}

.picker-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 72rpx;
  padding: 0 20rpx;
  min-height: 72rpx;
  position: relative;
}

.picker-text {
  flex: 1;
  font-size: 28rpx;
  line-height: 72rpx;
  color: #2c3e50;
  font-weight: 500;
  text-align: left;
  overflow: visible;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 72rpx;
}

.picker-text.placeholder {
  color: #95a5a6;
  font-weight: normal;
}

.picker-text.selected {
  color: #2c3e50;
  font-weight: 500;
}

.picker-arrow {
  color: #95a5a6;
  font-size: 24rpx;
  margin-left: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: bold;
}

.form-picker:active .picker-arrow {
  color: #1890ff;
  transform: rotate(180deg);
}

/* ==== 文本域样式 ==== */
.form-textarea {
  width: 100%;
  min-height: 200rpx;
  max-height: 400rpx;
  padding: 20rpx;
  border: 2rpx solid #e8ecf0;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #2c3e50;
  background: #fafbfc;
  box-sizing: border-box;
  line-height: 1.6;
  word-wrap: break-word;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  resize: none;
}

.form-textarea:focus {
  border-color: #1890ff;
  background: white;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1);
  outline: none;
}

.form-textarea.error {
  border-color: #e74c3c;
  background: #fdf2f2;
  box-shadow: 0 0 0 4rpx rgba(231, 76, 60, 0.1);
}

.textarea-counter {
  display: flex;
  justify-content: flex-end;
  margin-top: 8rpx;
}

.counter-text {
  font-size: 24rpx;
  color: #95a5a6;
}

/* ==== 错误提示 ==== */
.error-text {
  color: #e74c3c;
  font-size: 24rpx;
  margin-top: 8rpx;
  display: block;
  font-weight: 500;
  padding-left: 4rpx;
}

/* ==== 附件上传 ==== */
.attachment-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.attachment-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.attachment-item:active {
  transform: scale(0.95);
}

.attachment-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.attachment-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 44rpx;
  height: 44rpx;
  background: rgba(231, 76, 60, 0.9);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.attachment-delete:active {
  background: #c0392b;
  transform: scale(0.9);
}

.attachment-size {
  position: absolute;
  bottom: 8rpx;
  left: 8rpx;
  right: 8rpx;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  text-align: center;
  font-weight: 500;
}

.attachment-add {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #91d5ff;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  color: #1890ff;
  background: rgba(24, 144, 255, 0.05);
  transition: all 0.3s ease;
}

.attachment-add:active {
  background: rgba(24, 144, 255, 0.1);
  border-color: #1890ff;
  transform: scale(0.98);
}

.add-icon {
  font-size: 36rpx;
  font-weight: 300;
}

.add-text {
  font-size: 26rpx;
  font-weight: 500;
}

.attachment-tip {
  margin-top: 20rpx;
  padding: 16rpx 20rpx;
  background: rgba(24, 144, 255, 0.05);
  border-radius: 12rpx;
  border-left: 4rpx solid #1890ff;
}

.tip-text {
  font-size: 26rpx;
  color: #576574;
  line-height: 1.5;
}

/* ==== 提交按钮 ==== */
.submit-buttons {
  display: flex !important;
  flex-direction: row !important;
  gap: 24rpx;
  padding: 32rpx 0 20rpx 0;
  margin-top: 20rpx;
  width: 100%;
}

.submit-btn {
  flex: 1 !important;
  padding: 24rpx 0 !important;
  font-size: 28rpx !important;
  border-radius: 12rpx !important;
  border: none !important;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex !important;
  align-items: center;
  justify-content: center;
  width: auto !important;
  margin: 0 !important;
}

.draft-btn {
  background: linear-gradient(135deg, #6c757d, #495057) !important;
  color: white !important;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

.primary-btn {
  background: linear-gradient(135deg, #007bff, #0056b3) !important;
  color: white !important;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

.submit-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

.btn-hover {
  transform: translateY(1rpx) !important;
  opacity: 0.9 !important;
}

.submit-btn:disabled {
  opacity: 0.6;
  transform: none !important;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1) !important;
}

/* ==== 响应式设计 ==== */
@media (max-width: 750rpx) {
  .container {
    padding: 16rpx;
  }

  .form-section {
    padding: 24rpx;
  }

  .section-title {
    font-size: 32rpx;
  }

  .attachment-item,
  .attachment-add {
    width: calc(50% - 10rpx);
    height: 160rpx;
  }

  .submit-buttons {
    gap: 16rpx;
  }

  .submit-btn {
    padding: 20rpx 0 !important;
    font-size: 26rpx !important;
  }
}

/* ==== 暗色模式适配 ==== */
@media (prefers-color-scheme: dark) {
  .form-section {
    background: #2c3e50;
    border-color: #34495e;
  }

  .section-title {
    color: #ecf0f1;
  }

  .label-text {
    color: #bdc3c7;
  }

  .form-input,
  .form-picker,
  .form-textarea {
    background: #34495e;
    border-color: #576574;
    color: #ecf0f1;
  }

  .picker-text.placeholder {
    color: #7f8c8d;
  }
}