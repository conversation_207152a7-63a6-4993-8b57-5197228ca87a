/* pages/workspace/finance/reports/reports.wxss */

/* ==== 页面背景 - 多重保险策略 ==== */
page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  background-color: #667eea !important;
  background-attachment: fixed !important;
  min-height: 100vh !important;
}

/* 为了确保背景显示，添加body样式 */
body {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  background-color: #667eea !important;
  min-height: 100vh !important;
}

/* 微信小程序特殊处理 */
.container {
  min-height: 100vh;
  padding: 20rpx;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* 添加伪元素作为背景备用方案 */
.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: -1;
  pointer-events: none;
}

/* 为了确保在所有情况下都有背景，添加根元素背景 */
.report-content {
  background: transparent;
  position: relative;
  z-index: 1;
}

/* ==== 加载状态 ==== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 150rpx 0;
  background: white;
  border-radius: 24rpx;
  margin: 40rpx 20rpx;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 24rpx;
  color: #666;
  font-size: 28rpx;
}

/* ==== 报表内容 ==== */
.report-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* ==== 控制面板 ==== */
.control-panel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 24rpx;
}

.panel-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.panel-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.panel-subtitle {
  font-size: 24rpx;
  color: #666;
  opacity: 0.8;
}

/* 筛选器区域 */
.filters-section {
  display: flex;
  gap: 20rpx;
}

.filter-item {
  flex: 1;
}

.filter-label {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.filter-picker {
  width: 100%;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #eef1ff 100%);
  border: 2rpx solid #e1e5f7;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.picker-display:active {
  background: linear-gradient(135deg, #eef1ff 0%, #e1e5f7 100%);
  border-color: #667eea;
  transform: scale(0.98);
}

.picker-text {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.picker-icon {
  font-size: 20rpx;
  color: #999;
  margin-left: 10rpx;
  transition: transform 0.3s ease;
}

/* 自定义日期范围 */
.custom-date-section {
  margin-top: 25rpx;
  padding: 25rpx;
  background: rgba(248, 249, 255, 0.8);
  border-radius: 16rpx;
  border: 1rpx solid #e1e5f7;
}

.date-input-row {
  display: flex;
  gap: 20rpx;
}

.date-input-item {
  flex: 1;
}

.date-display {
  padding: 14rpx 16rpx;
  background: white;
  border: 1rpx solid #ddd;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.date-display:active {
  border-color: #667eea;
  background: #f8f9ff;
}

/* ==== 主要内容区域 ==== */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* ==== 宫格布局 ==== */
.card-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12rpx;
  margin-bottom: 20rpx;
}

/* ==== 数据卡片 ==== */
.data-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 120rpx;
}

.data-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
}

.card-icon {
  font-size: 28rpx;
  margin-bottom: 8rpx;
  display: block;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 2rpx;
}

.card-title {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

.card-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  margin: 2rpx 0;
}

.card-desc {
  font-size: 18rpx;
  color: #999;
  opacity: 0.8;
}

/* 卡片趋势指示器 */
.card-trend {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
}

.card-trend.positive {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.card-trend.negative {
  background: rgba(244, 67, 54, 0.1);
  color: #F44336;
}

.trend-icon {
  font-size: 16rpx;
}

.trend-text {
  font-size: 18rpx;
  font-weight: 500;
}

/* 卡片信息 */
.card-info {
  margin-top: 8rpx;
}

.info-text {
  font-size: 20rpx;
  color: #999;
}

/* 卡片百分比 */
.card-percentage {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
}

.percentage-text {
  font-size: 20rpx;
  color: #667eea;
  font-weight: bold;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
}

/* ==== 特定卡片样式 ==== */
.income-card {
  border-left: 4rpx solid #4CAF50;
}

.expense-card {
  border-left: 4rpx solid #FF5722;
}

.profit-card {
  border-left: 4rpx solid #2196F3;
}

.transaction-card {
  border-left: 4rpx solid #FF9800;
}



.health-card {
  border-left: 4rpx solid #E91E63;
}

.production-card {
  border-left: 4rpx solid #00BCD4;
}

.net-health-card {
  border-left: 4rpx solid #8BC34A;
}

/* ==== 趋势分析区域 ==== */
.trend-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 24rpx;
}

.trend-chart-container {
  margin-top: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background: #f8f9ff;
  padding: 20rpx;
}

.trend-chart {
  width: 100%;
  border-radius: 12rpx;
}

/* ==== 对比分析区域 ==== */
.comparison-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 24rpx;
}

.comparison-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-top: 20rpx;
}

.comparison-item {
  text-align: center;
  padding: 20rpx;
  background: #f8f9ff;
  border-radius: 16rpx;
}

.comparison-label {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.comparison-value {
  font-size: 24rpx;
  font-weight: bold;
}

.comparison-value.current {
  color: #2196F3;
}

.comparison-value.previous {
  color: #666;
}

.comparison-value.positive {
  color: #4CAF50;
}

.comparison-value.negative {
  color: #F44336;
}

/* ==== 交易记录列表 ==== */
.transaction-list-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 24rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(102, 126, 234, 0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.section-count {
  font-size: 22rpx;
  color: #666;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.transaction-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background: rgba(248, 249, 255, 0.8);
  border-radius: 16rpx;
  transition: all 0.3s ease;
  border-left: 3rpx solid transparent;
}

.transaction-item:active {
  background: rgba(102, 126, 234, 0.1);
  transform: scale(0.98);
}

.transaction-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.icon-text {
  font-size: 20rpx;
}

.transaction-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.transaction-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.transaction-title {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
}

.transaction-amount {
  font-size: 24rpx;
  font-weight: bold;
}

.transaction-amount.income {
  color: #4CAF50;
}

.transaction-amount.expense {
  color: #F44336;
}

.transaction-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.transaction-category {
  font-size: 20rpx;
  color: #666;
  background: rgba(102, 126, 234, 0.08);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.transaction-date {
  font-size: 20rpx;
  color: #999;
}

.transaction-arrow {
  margin-left: 12rpx;
}

.arrow-icon {
  font-size: 24rpx;
  color: #ccc;
}

/* ==== 快速操作区域 ==== */
.quick-actions {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  margin-top: 24rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.action-item:active {
  background: rgba(102, 126, 234, 0.1);
  transform: scale(0.95);
}

.action-icon {
  font-size: 32rpx;
}

.action-text {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

/* ==== 响应式设计 ==== */
@media screen and (max-width: 750rpx) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8rpx;
  }

  .data-card {
    padding: 16rpx;
    min-height: 100rpx;
  }

  .card-value {
    font-size: 22rpx;
  }

  .card-title {
    font-size: 20rpx;
  }

  .comparison-grid {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }

  .filters-section {
    flex-direction: column;
    gap: 16rpx;
  }

  .date-input-row {
    flex-direction: column;
    gap: 16rpx;
  }
}

@media screen and (min-width: 1000rpx) {
  .card-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .comparison-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* ==== 动画效果 ==== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.data-card {
  animation: fadeInUp 0.6s ease-out;
}

.data-card:nth-child(1) {
  animation-delay: 0.1s;
}

.data-card:nth-child(2) {
  animation-delay: 0.2s;
}

.data-card:nth-child(3) {
  animation-delay: 0.3s;
}

.data-card:nth-child(4) {
  animation-delay: 0.4s;
}

.data-card:nth-child(5) {
  animation-delay: 0.5s;
}

.data-card:nth-child(6) {
  animation-delay: 0.6s;
}

/* ==== 滚动优化 ==== */
.report-content {
  scroll-behavior: smooth;
}

/* ==== 无障碍优化 ==== */
.data-card:focus {
  outline: 2rpx solid #667eea;
  outline-offset: 2rpx;
}

.action-item:focus {
  outline: 2rpx solid #667eea;
  outline-offset: 2rpx;
}

/* ==== 新增分析样式 ==== */

/* 分析区域样式 */
.analysis-section {
  margin-bottom: 30rpx;
}

.section-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 700;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  display: block;
  margin-bottom: 10rpx;
}

.section-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 分析卡片样式 */
.analysis-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.card-header .card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
}

/* 收入来源分析样式 */
.income-sources {
  padding: 30rpx;
}

.source-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.source-item:last-child {
  margin-bottom: 0;
}

.source-info {
  width: 200rpx;
  flex-shrink: 0;
}

.source-name {
  font-size: 26rpx;
  color: #2c3e50;
  display: block;
  margin-bottom: 4rpx;
}

.source-amount {
  font-size: 22rpx;
  color: #27ae60;
  font-weight: 500;
}

.source-bar {
  flex: 1;
  height: 16rpx;
  background: #ecf0f1;
  border-radius: 8rpx;
  margin: 0 20rpx;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.expense-bar {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.source-percentage {
  font-size: 24rpx;
  color: #7f8c8d;
  font-weight: 500;
  width: 60rpx;
  text-align: right;
}

/* 支出分类分析样式 */
.expense-categories {
  padding: 30rpx;
}

.category-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.category-item:last-child {
  margin-bottom: 0;
}

.category-info {
  width: 200rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.category-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.category-details {
  flex: 1;
}

.category-name {
  font-size: 26rpx;
  color: #2c3e50;
  display: block;
  margin-bottom: 4rpx;
}

.category-amount {
  font-size: 22rpx;
  color: #e74c3c;
  font-weight: 500;
}

.category-bar {
  flex: 1;
  height: 16rpx;
  background: #ecf0f1;
  border-radius: 8rpx;
  margin: 0 20rpx;
  overflow: hidden;
}

.category-percentage {
  font-size: 24rpx;
  color: #7f8c8d;
  font-weight: 500;
  width: 60rpx;
  text-align: right;
}

/* 建议样式 */
.suggestions {
  padding: 30rpx;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 12rpx;
  border-left: 4rpx solid #3498db;
}

.suggestion-item:last-child {
  margin-bottom: 0;
}

.suggestion-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.suggestion-text {
  font-size: 24rpx;
  color: #2c3e50;
  line-height: 1.5;
}

/* 图表容器样式 */
.chart-container {
  padding: 30rpx;
  height: 400rpx;
}

.trend-chart,
.category-chart,
.comparison-chart {
  width: 100%;
  height: 100%;
}

/* 图表错误信息样式 */
.chart-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8rpx;
  border: 1rpx solid #ff4444;
}

.error-text {
  color: #ff4444;
  font-size: 24rpx;
}

/* 单行卡片网格 */
.card-grid.single-row {
  grid-template-columns: 1fr;
}

/* 分类统计样式 */
.category-stats {
  padding: 30rpx;
}

.stat-item {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: rgba(248, 249, 255, 0.8);
  border-radius: 16rpx;
  border-left: 4rpx solid #3498db;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.stat-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.stat-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
}

.stat-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 24rpx;
  color: #7f8c8d;
}

.stat-value {
  font-size: 24rpx;
  font-weight: 500;
}

.stat-value.income {
  color: #27ae60;
}

.stat-value.expense {
  color: #e74c3c;
}

.stat-value.positive {
  color: #27ae60;
}

.stat-value.negative {
  color: #e74c3c;
}

/* 趋势指标样式 */
.trend-indicators {
  padding: 30rpx;
}

.indicator-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 16rpx;
  background: rgba(248, 249, 255, 0.8);
  border-radius: 12rpx;
}

.indicator-item:last-child {
  margin-bottom: 0;
}

.indicator-label {
  font-size: 26rpx;
  color: #2c3e50;
}

.indicator-value {
  font-size: 26rpx;
  font-weight: 600;
}

.indicator-value.positive {
  color: #27ae60;
}

.indicator-value.negative {
  color: #e74c3c;
}

/* 对比指标样式 */
.comparison-indicators {
  padding: 30rpx;
}

.comparison-row {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  background: rgba(248, 249, 255, 0.8);
  border-radius: 12rpx;
}

.comparison-row:last-child {
  margin-bottom: 0;
}

.comparison-label {
  font-size: 26rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.comparison-current,
.comparison-previous {
  font-size: 24rpx;
  color: #7f8c8d;
}

.comparison-change {
  font-size: 24rpx;
  font-weight: 600;
  text-align: right;
}

.comparison-change.positive {
  color: #27ae60;
}

.comparison-change.negative {
  color: #e74c3c;
}