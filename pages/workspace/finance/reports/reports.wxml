<!--pages/workspace/finance/reports/reports.wxml-->
<permission-check permission="{{requiredPermissions.view}}" mode="show">
  <view class="container">
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载报表数据...</text>
    </view>

    <!-- 报表内容 -->
    <view wx:else class="report-content">
      <!-- 顶部控制面板 -->
      <view class="control-panel">
        <view class="panel-header">
          <text class="panel-title">📊 财务报表</text>
          <text class="panel-subtitle">多维度数据分析</text>
        </view>

        <!-- 筛选器区域 -->
        <view class="filters-section">
          <!-- 时间范围选择 -->
          <view class="filter-item">
            <text class="filter-label">时间范围</text>
            <picker
              class="filter-picker"
              range="{{timeRangeOptions}}"
              range-key="label"
              value="{{selectedTimeRangeIndex}}"
              bindchange="onTimeRangeChange"
            >
              <view class="picker-display">
                <text class="picker-text">{{selectedTimeRangeLabel}}</text>
                <text class="picker-icon">▼</text>
              </view>
            </picker>
          </view>

          <!-- 报表类型选择 -->
          <view class="filter-item">
            <text class="filter-label">报表类型</text>
            <picker
              class="filter-picker"
              range="{{reportTypes}}"
              range-key="label"
              value="{{selectedReportTypeIndex}}"
              bindchange="onReportTypeChange"
            >
              <view class="picker-display">
                <text class="picker-text">{{selectedReportTypeLabel}}</text>
                <text class="picker-icon">▼</text>
              </view>
            </picker>
          </view>
        </view>

        <!-- 自定义日期范围 -->
        <view wx:if="{{timeRange === 'custom'}}" class="custom-date-section">
          <view class="date-input-row">
            <view class="date-input-item">
              <text class="date-label">开始日期</text>
              <picker
                class="date-picker"
                mode="date"
                value="{{customDateRange.start}}"
                data-type="start"
                bindchange="onCustomDateChange"
              >
                <view class="date-display">
                  <text class="date-text {{customDateRange.start ? 'selected' : 'placeholder'}}">
                    {{customDateRange.start || '选择开始日期'}}
                  </text>
                </view>
              </picker>
            </view>

            <view class="date-input-item">
              <text class="date-label">结束日期</text>
              <picker
                class="date-picker"
                mode="date"
                value="{{customDateRange.end}}"
                data-type="end"
                bindchange="onCustomDateChange"
              >
                <view class="date-display">
                  <text class="date-text {{customDateRange.end ? 'selected' : 'placeholder'}}">
                    {{customDateRange.end || '选择结束日期'}}
                  </text>
                </view>
              </picker>
            </view>
          </view>
        </view>
      </view>

      <!-- 主要内容区域 - 根据报表类型显示不同分析 -->
      <view class="main-content">

        <!-- 财务总览 -->
        <view wx:if="{{reportType === 'overview'}}" class="analysis-section">
          <view class="section-header">
            <text class="section-title">💰 财务总览</text>
            <text class="section-desc">整体财务状况概览</text>
          </view>
          <view class="card-grid">
            <view class="data-card income-card">
              <view class="card-icon">💰</view>
              <view class="card-content">
                <text class="card-title">总收入</text>
                <text class="card-value">¥{{reportData.overview.totalIncome || 0}}</text>
                <text class="card-desc">{{selectedTimeRangeLabel}}收入</text>
              </view>
            </view>
            <view class="data-card expense-card">
              <view class="card-icon">📤</view>
              <view class="card-content">
                <text class="card-title">总支出</text>
                <text class="card-value">¥{{reportData.overview.totalExpense || 0}}</text>
                <text class="card-desc">{{selectedTimeRangeLabel}}支出</text>
              </view>
            </view>
            <view class="data-card profit-card">
              <view class="card-icon">📈</view>
              <view class="card-content">
                <text class="card-title">净利润</text>
                <text class="card-value">¥{{reportData.overview.netProfit || 0}}</text>
                <text class="card-desc">收支差额</text>
              </view>
            </view>
            <view class="data-card transaction-card">
              <view class="card-icon">📊</view>
              <view class="card-content">
                <text class="card-title">交易笔数</text>
                <text class="card-value">{{reportData.overview.transactionCount || 0}}</text>
                <text class="card-desc">总交易次数</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 收入统计分析 -->
        <view wx:if="{{reportType === 'income'}}" class="analysis-section">
          <view class="section-header">
            <text class="section-title">💰 收入统计分析</text>
            <text class="section-desc">深度分析收入来源和趋势</text>
          </view>

          <!-- 收入概览卡片 -->
          <view class="card-grid single-row">
            <view class="data-card income-summary-card">
              <view class="card-icon">💰</view>
              <view class="card-content">
                <text class="card-title">总收入</text>
                <text class="card-value">¥{{reportData.overview.totalIncome || 0}}</text>
                <text class="card-desc">{{selectedTimeRangeLabel}}累计收入</text>
              </view>
              <view class="card-trend positive" wx:if="{{reportData.overview.incomeGrowth > 0}}">
                <text class="trend-text">较上期 +{{reportData.overview.incomeGrowth}}%</text>
              </view>
            </view>
          </view>

          <!-- 收入来源分析 -->
          <view class="analysis-card">
            <view class="card-header">
              <text class="card-title">📊 收入来源分析</text>
            </view>
            <view class="income-sources">
              <view class="source-item" wx:for="{{reportData.incomeAnalysis.sources}}" wx:key="category">
                <view class="source-info">
                  <text class="source-name">{{item.category}}</text>
                  <text class="source-amount">¥{{item.amount}}</text>
                </view>
                <view class="source-bar">
                  <view class="bar-fill" style="width: {{item.percentage}}%"></view>
                </view>
                <text class="source-percentage">{{item.percentage}}%</text>
              </view>
            </view>
          </view>

          <!-- 收入趋势图 -->
          <view class="analysis-card">
            <view class="card-header">
              <text class="card-title">📈 收入趋势</text>
            </view>
            <view class="chart-container">
              <canvas type="2d" id="incomeChart" class="trend-chart"></canvas>
            </view>
          </view>
        </view>

        <!-- 支出分析 -->
        <view wx:if="{{reportType === 'expense'}}" class="analysis-section">
          <view class="section-header">
            <text class="section-title">📤 支出分析</text>
            <text class="section-desc">详细分析支出结构和趋势</text>
          </view>

          <!-- 支出概览卡片 -->
          <view class="card-grid single-row">
            <view class="data-card expense-summary-card">
              <view class="card-icon">📤</view>
              <view class="card-content">
                <text class="card-title">总支出</text>
                <text class="card-value">¥{{reportData.overview.totalExpense || 0}}</text>
                <text class="card-desc">{{selectedTimeRangeLabel}}累计支出</text>
              </view>
              <view class="card-trend negative" wx:if="{{reportData.overview.expenseGrowth > 0}}">
                <text class="trend-text">较上期 +{{reportData.overview.expenseGrowth}}%</text>
              </view>
            </view>
          </view>

          <!-- 支出分类图表 -->
          <view class="analysis-card">
            <view class="card-header">
              <text class="card-title">📊 支出分类图表</text>
            </view>
            <view class="chart-container">
              <canvas type="2d" id="expenseChart" class="category-chart"></canvas>
            </view>
          </view>

          <!-- 支出分类分析 -->
          <view class="analysis-card">
            <view class="card-header">
              <text class="card-title">📋 支出分类明细</text>
            </view>
            <view class="expense-categories">
              <view class="category-item" wx:for="{{reportData.expenseAnalysis.categories}}" wx:key="name">
                <view class="category-info">
                  <text class="category-icon">{{item.icon}}</text>
                  <view class="category-details">
                    <text class="category-name">{{item.name}}</text>
                    <text class="category-amount">¥{{item.amount}}</text>
                  </view>
                </view>
                <view class="category-bar">
                  <view class="bar-fill expense-bar" style="width: {{item.percentage}}%"></view>
                </view>
                <text class="category-percentage">{{item.percentage}}%</text>
              </view>
            </view>
          </view>

          <!-- 支出控制建议 -->
          <view class="analysis-card">
            <view class="card-header">
              <text class="card-title">💡 支出控制建议</text>
            </view>
            <view class="suggestions">
              <view class="suggestion-item" wx:for="{{reportData.expenseAnalysis.suggestions}}" wx:key="index">
                <text class="suggestion-icon">{{item.icon}}</text>
                <text class="suggestion-text">{{item.text}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 分类统计 -->
        <view wx:if="{{reportType === 'category'}}" class="analysis-section">
          <view class="section-header">
            <text class="section-title">📊 分类统计</text>
            <text class="section-desc">各分类收支详细统计</text>
          </view>

          <!-- 分类对比图表 -->
          <view class="analysis-card">
            <view class="card-header">
              <text class="card-title">📈 分类收支对比</text>
            </view>
            <view class="chart-container">
              <canvas type="2d" id="categoryChart" class="category-chart"></canvas>
              <!-- 图表错误信息显示 -->
              <view wx:if="{{chartError_categoryChart}}" class="chart-error">
                <text class="error-text">{{chartError_categoryChart}}</text>
              </view>
            </view>
          </view>

          <!-- 分类详细统计 -->
          <view class="analysis-card">
            <view class="card-header">
              <text class="card-title">📋 分类明细</text>
            </view>
            <view class="category-stats">
              <view class="stat-item" wx:for="{{reportData.categoryStats}}" wx:key="category">
                <view class="stat-header">
                  <text class="stat-icon">{{item.icon}}</text>
                  <text class="stat-name">{{item.category}}</text>
                </view>
                <view class="stat-details">
                  <view class="stat-row">
                    <text class="stat-label">收入</text>
                    <text class="stat-value income">¥{{item.income || 0}}</text>
                  </view>
                  <view class="stat-row">
                    <text class="stat-label">支出</text>
                    <text class="stat-value expense">¥{{item.expense || 0}}</text>
                  </view>
                  <view class="stat-row">
                    <text class="stat-label">净额</text>
                    <text class="stat-value {{item.net >= 0 ? 'positive' : 'negative'}}">¥{{item.net || 0}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 趋势分析 -->
        <view wx:if="{{reportType === 'trend'}}" class="analysis-section">
          <view class="section-header">
            <text class="section-title">📈 趋势分析</text>
            <text class="section-desc">财务数据时间趋势分析</text>
          </view>

          <!-- 趋势图表 -->
          <view class="analysis-card">
            <view class="card-header">
              <text class="card-title">📊 收支趋势对比</text>
            </view>
            <view class="chart-container">
              <canvas type="2d" id="trendChart" class="trend-chart"></canvas>
            </view>
          </view>

          <!-- 趋势指标 -->
          <view class="analysis-card">
            <view class="card-header">
              <text class="card-title">📋 趋势指标</text>
            </view>
            <view class="trend-indicators">
              <view class="indicator-item">
                <text class="indicator-label">收入增长率</text>
                <text class="indicator-value {{reportData.trendAnalysis.incomeGrowthRate >= 0 ? 'positive' : 'negative'}}">
                  {{reportData.trendAnalysis.incomeGrowthRate >= 0 ? '+' : ''}}{{reportData.trendAnalysis.incomeGrowthRate || 0}}%
                </text>
              </view>
              <view class="indicator-item">
                <text class="indicator-label">支出增长率</text>
                <text class="indicator-value {{reportData.trendAnalysis.expenseGrowthRate >= 0 ? 'negative' : 'positive'}}">
                  {{reportData.trendAnalysis.expenseGrowthRate >= 0 ? '+' : ''}}{{reportData.trendAnalysis.expenseGrowthRate || 0}}%
                </text>
              </view>
              <view class="indicator-item">
                <text class="indicator-label">净利润趋势</text>
                <text class="indicator-value {{reportData.trendAnalysis.profitTrend === 'up' ? 'positive' : 'negative'}}">
                  {{reportData.trendAnalysis.profitTrend === 'up' ? '上升' : '下降'}}
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- 健康财务整合 -->
        <view wx:if="{{reportType === 'health_integration'}}" class="analysis-section">
          <view class="section-header">
            <text class="section-title">🏥 健康财务整合</text>
            <text class="section-desc">健康相关财务数据分析</text>
          </view>

          <view class="card-grid">
            <view class="data-card health-card">
              <view class="card-icon">🏥</view>
              <view class="card-content">
                <text class="card-title">健康支出</text>
                <text class="card-value">¥{{reportData.healthIntegrationStats.totalHealthExpense || 0}}</text>
                <text class="card-desc">医疗相关费用</text>
              </view>
            </view>
            <view class="data-card production-card">
              <view class="card-icon">🏭</view>
              <view class="card-content">
                <text class="card-title">生产收入</text>
                <text class="card-value">¥{{reportData.healthIntegrationStats.totalProductionIncome || 0}}</text>
                <text class="card-desc">生产销售收入</text>
              </view>
            </view>
            <view class="data-card net-health-card">
              <view class="card-icon">💊</view>
              <view class="card-content">
                <text class="card-title">健康净收益</text>
                <text class="card-value">¥{{reportData.healthIntegrationStats.netHealthProfit || 0}}</text>
                <text class="card-desc">健康投入产出比</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 对比分析 -->
        <view wx:if="{{reportType === 'comparison'}}" class="analysis-section">
          <view class="section-header">
            <text class="section-title">🔄 对比分析</text>
            <text class="section-desc">不同时期财务数据对比</text>
          </view>

          <!-- 对比图表 -->
          <view class="analysis-card">
            <view class="card-header">
              <text class="card-title">📊 同比/环比分析</text>
            </view>
            <view class="chart-container">
              <canvas type="2d" id="comparisonChart" class="comparison-chart"></canvas>
            </view>
          </view>

          <!-- 对比指标 -->
          <view class="analysis-card">
            <view class="card-header">
              <text class="card-title">📋 对比指标</text>
            </view>
            <view class="comparison-indicators">
              <view class="comparison-row">
                <text class="comparison-label">收入对比</text>
                <text class="comparison-current">本期: ¥{{reportData.comparisonData.current.income || 0}}</text>
                <text class="comparison-previous">上期: ¥{{reportData.comparisonData.previous.income || 0}}</text>
                <text class="comparison-change {{reportData.comparisonData.incomeChange >= 0 ? 'positive' : 'negative'}}">
                  {{reportData.comparisonData.incomeChange >= 0 ? '+' : ''}}{{reportData.comparisonData.incomeChange || 0}}%
                </text>
              </view>
              <view class="comparison-row">
                <text class="comparison-label">支出对比</text>
                <text class="comparison-current">本期: ¥{{reportData.comparisonData.current.expense || 0}}</text>
                <text class="comparison-previous">上期: ¥{{reportData.comparisonData.previous.expense || 0}}</text>
                <text class="comparison-change {{reportData.comparisonData.expenseChange >= 0 ? 'negative' : 'positive'}}">
                  {{reportData.comparisonData.expenseChange >= 0 ? '+' : ''}}{{reportData.comparisonData.expenseChange || 0}}%
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- 对比分析卡片 -->
        <view class="comparison-section" wx:if="{{reportType === 'comparison' && reportData.comparisonData}}">
          <view class="section-header">
            <text class="section-title">⚖️ 对比分析</text>
          </view>
          <view class="comparison-grid">
            <view class="comparison-item">
              <text class="comparison-label">当期收入</text>
              <text class="comparison-value current">¥{{reportData.comparisonData.current.totalIncome || 0}}</text>
            </view>
            <view class="comparison-item">
              <text class="comparison-label">上期收入</text>
              <text class="comparison-value previous">¥{{reportData.comparisonData.previous.totalIncome || 0}}</text>
            </view>
            <view class="comparison-item">
              <text class="comparison-label">收入变化</text>
              <text class="comparison-value {{reportData.comparisonData.incomeChangeRate >= 0 ? 'positive' : 'negative'}}">
                {{reportData.comparisonData.incomeChangeRate || 0}}%
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 交易记录列表 -->
      <view class="transaction-list-section" wx:if="{{reportData.transactions && reportData.transactions.length > 0}}">
        <view class="section-header">
          <text class="section-title">📋 交易记录</text>
          <text class="section-count">共{{reportData.transactions.length}}笔</text>
        </view>

        <view class="transaction-list">
          <view
            wx:for="{{reportData.transactions}}"
            wx:key="id"
            class="transaction-item"
            bindtap="onTransactionTap"
            data-transaction="{{item}}"
          >
            <view class="transaction-icon">
              <text class="icon-text">{{item.type === 'income' ? '💰' : '💸'}}</text>
            </view>

            <view class="transaction-content">
              <view class="transaction-main">
                <text class="transaction-title">{{item.description || item.category}}</text>
                <text class="transaction-amount {{item.type === 'income' ? 'income' : 'expense'}}">
                  {{item.type === 'income' ? '+' : '-'}}¥{{item.amount}}
                </text>
              </view>

              <view class="transaction-meta">
                <text class="transaction-category">{{item.category}}</text>
                <text class="transaction-date">{{item.date}}</text>
              </view>
            </view>

            <view class="transaction-arrow">
              <text class="arrow-icon">›</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 快速操作区域 -->
      <view class="quick-actions">
        <view class="action-item" bindtap="onExportReport" data-type="excel">
          <view class="action-icon">📊</view>
          <text class="action-text">导出Excel</text>
        </view>
        <view class="action-item" bindtap="onExportReport" data-type="pdf">
          <view class="action-icon">📄</view>
          <text class="action-text">导出PDF</text>
        </view>
        <view class="action-item" bindtap="onRefreshData">
          <view class="action-icon">🔄</view>
          <text class="action-text">刷新数据</text>
        </view>
      </view>
    </view>
  </view>
</permission-check>