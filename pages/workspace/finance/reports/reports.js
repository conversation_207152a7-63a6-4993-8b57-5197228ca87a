// pages/workspace/finance/reports/reports.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');
const { getCurrentUserPermissions, PERMISSIONS, FinancePermissionChecker } = require('../../../../utils/role-permission.js');
const { healthFinanceIntegration } = require('../../../../utils/health-finance-integration.js');
const FinanceMockDataService = require('../../../../utils/finance-mock-data.js');

Page({
  data: {
    // 页面状态
    loading: false,
    refreshing: false,
    
    // 权限控制
    userPermissions: null,
    requiredPermissions: {
      view: PERMISSIONS.FINANCE_VIEW,
      edit: PERMISSIONS.FINANCE_EDIT,
      admin: PERMISSIONS.FINANCE_ADMIN
    },
    
    // 时间范围选择
    timeRange: 'month',
    customDateRange: {
      start: '',
      end: ''
    },
    timeRangeOptions: [
      { value: 'week', label: '📅 本周' },
      { value: 'month', label: '📆 本月' },
      { value: 'quarter', label: '🗓️ 本季度' },
      { value: 'year', label: '📊 本年度' },
      { value: 'custom', label: '📝 自定义' }
    ],
    selectedTimeRangeIndex: 1, // 默认选中"本月"
    selectedTimeRangeLabel: '📆 本月',
    
    // 报表类型
    reportType: 'overview',
    reportTypes: [
      { value: 'overview', label: '💰 财务总览', description: '整体收支概况' },
      { value: 'expense', label: '📤 支出分析', description: '详细支出分类' },
      { value: 'income', label: '📈 收入统计', description: '收入来源分析' },
      { value: 'health_integration', label: '🏥 健康财务', description: '健康管理相关费用' },
      { value: 'category', label: '📊 分类统计', description: '按类别统计分析' },
      { value: 'trend', label: '📈 趋势分析', description: '时间维度趋势' },
      { value: 'comparison', label: '⚖️ 对比分析', description: '同期数据对比' }
    ],
    selectedReportTypeIndex: 0, // 默认选中"财务总览"
    selectedReportTypeLabel: '💰 财务总览',
    
    // 报表数据
    reportData: {
      overview: {
        totalIncome: 0,
        totalExpense: 0,
        netProfit: 0,
        transactionCount: 0,
        avgTransactionAmount: 0,
        incomeGrowth: 0,
        expenseGrowth: 0
      },
      categoryStats: [],
      trendData: [],
      healthIntegrationStats: null,
      comparisonData: null,
      transactions: []
    },
    
    // 图表配置
    chartConfig: {
      canvasId: 'finance-chart',
      width: 0,
      height: 300,
      backgroundColor: '#ffffff',
      color: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe']
    },
    
    // 过滤选项
    filters: {
      categories: [],
      selectedCategories: [],
      minAmount: '',
      maxAmount: '',
      status: 'all'
    },
    
    // 导出选项
    exportOptions: {
      format: 'excel',
      includeCharts: true,
      includeSummary: true
    }
  },

  async onLoad(options) {
    // 开发模式：强制使用模拟数据以便测试
    wx.setStorageSync('finance_use_mock_data', true);

    await this.initPage();

    // 检查URL参数
    if (options.type) {
      this.setData({
        reportType: options.type
      });
    }

    if (options.range) {
      this.setData({
        timeRange: options.range
      });
    }

    // 更新计算属性
    this.updateComputedProperties();

    await this.loadReportData();
  },

  onReady() {
    // 页面渲染完成后初始化图表
    console.log('页面渲染完成，准备初始化图表');
    setTimeout(() => {
      console.log('开始初始化图表，当前报表类型:', this.data.reportType);
      this.initCharts();
    }, 500); // 延迟初始化确保DOM已渲染
  },

  onUnload() {
    // 页面卸载时清理资源
    this.clearCharts();
  },

  async initPage() {
    try {
      // 获取用户权限
      const userPermissions = await getCurrentUserPermissions();
      
      // 检查查看权限
      if (!FinancePermissionChecker.canViewReports(userPermissions.role)) {
        wx.showModal({
          title: '权限不足',
          content: '您没有查看财务报表的权限',
          showCancel: false,
          complete: () => {
            wx.navigateBack();
          }
        });
        return;
      }
      
      // 获取画布尺寸
      const windowInfo = wx.getWindowInfo();
      const canvasWidth = windowInfo.windowWidth - 40; // 减去边距
      
      this.setData({
        userPermissions,
        'chartConfig.width': canvasWidth
      });
      
      // 初始化健康财务整合服务
      await healthFinanceIntegration.init();
      
    } catch (error) {
      console.error('初始化页面失败:', error);
      wx.showToast({
        title: '初始化失败',
        icon: 'error'
      });
    }
  },

  async onShow() {
    // 页面显示时刷新数据
    if (this.data.userPermissions) {
      await this.refreshData();
    }
  },

  onPullDownRefresh() {
    this.refreshData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  async refreshData() {
    this.setData({ refreshing: true });
    await this.loadReportData();
    this.setData({ refreshing: false });
  },

  /**
   * 更新计算属性
   */
  updateComputedProperties() {
    const { timeRange, timeRangeOptions, reportType, reportTypes } = this.data;

    // 计算选中的时间范围索引
    const selectedTimeIndex = timeRangeOptions.findIndex(item => item.value === timeRange);
    const selectedTimeLabel = timeRangeOptions.find(item => item.value === timeRange)?.label || '';

    // 计算选中的报表类型索引
    const selectedReportIndex = reportTypes.findIndex(item => item.value === reportType);
    const selectedReportLabel = reportTypes.find(item => item.value === reportType)?.label || '';

    this.setData({
      selectedTimeRangeIndex: selectedTimeIndex >= 0 ? selectedTimeIndex : 0,
      selectedTimeRangeLabel: selectedTimeLabel,
      selectedReportTypeIndex: selectedReportIndex >= 0 ? selectedReportIndex : 0,
      selectedReportTypeLabel: selectedReportLabel
    });
  },

  async loadReportData() {
    try {
      this.setData({ loading: true });

      const { timeRange, reportType, customDateRange } = this.data;

      // 构建请求参数
      const params = {
        timeRange: timeRange,
        reportType: reportType
      };

      if (timeRange === 'custom') {
        params.startDate = customDateRange.start;
        params.endDate = customDateRange.end;
      }

      // 检查是否强制使用模拟数据（开发模式）
      const useMockData = wx.getStorageSync('finance_use_mock_data') || false;

      if (useMockData) {
        console.log('使用模拟数据模式');
        await this.loadMockData(params);
        return;
      }

      // 根据报表类型加载对应数据
      const promises = [];

      // 基础财务概览数据（所有报表类型都需要）
      promises.push(this.loadFinanceOverview(params));

      // 根据报表类型加载特定数据
      switch (reportType) {
        case 'overview':
          // 财务总览：加载概览、分类统计、趋势数据
          promises.push(this.loadCategoryStatistics(params));
          promises.push(this.loadTrendData(params));
          promises.push(this.loadTransactionData(params));
          break;

        case 'category':
          // 分类统计：重点加载分类数据
          promises.push(this.loadCategoryStatistics(params));
          promises.push(this.loadTransactionData(params));
          break;

        case 'trend':
          // 趋势分析：重点加载趋势数据
          promises.push(this.loadTrendData(params));
          promises.push(this.loadTransactionData(params));
          break;

        case 'health_integration':
          // 健康财务整合
          promises.push(this.loadHealthIntegrationData(params));
          promises.push(this.loadTransactionData(params));
          break;

        case 'comparison':
          // 对比分析
          promises.push(this.loadComparisonData(params));
          promises.push(this.loadTransactionData(params));
          break;

        default:
          // 默认加载所有数据
          promises.push(this.loadCategoryStatistics(params));
          promises.push(this.loadTrendData(params));
          promises.push(this.loadTransactionData(params));
          break;
      }

      await Promise.all(promises);

    } catch (error) {
      console.error('加载报表数据失败:', error);
      wx.showToast({
        title: '加载失败，使用模拟数据',
        icon: 'none'
      });

      // 作为最后的回退，加载模拟数据
      await this.loadMockData(this.buildParams());
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载模拟数据（用于开发和测试）
   */
  async loadMockData(params) {
    try {
      const { reportType } = params;
      const mockData = FinanceMockDataService.getAllReportData(params);

      if (mockData) {
        // 生成模拟交易记录
        const mockTransactions = this.generateMockTransactions();

        // 根据报表类型设置对应的数据
        const updateData = {
          'reportData.overview': mockData.overview,
          'reportData.transactions': mockTransactions
        };

        // 根据报表类型添加特定数据
        switch (reportType) {
          case 'overview':
            // 财务总览：显示基础概览数据
            updateData['reportData.categoryStats'] = mockData.categoryStats;
            updateData['reportData.trendData'] = mockData.trendData;
            break;

          case 'income':
            // 收入统计：添加收入分析数据
            updateData['reportData.incomeAnalysis'] = {
              sources: [
                { category: '销售收入', amount: 45000, percentage: 80 },
                { category: '投资收益', amount: 8000, percentage: 14 },
                { category: '其他收入', amount: 3249, percentage: 6 }
              ]
            };
            break;

          case 'expense':
            // 支出分析：添加支出分析数据
            updateData['reportData.expenseAnalysis'] = {
              categories: [
                { name: '日常开销', icon: '🛒', amount: 12000, percentage: 38 },
                { name: '房租水电', icon: '🏠', amount: 8000, percentage: 25 },
                { name: '交通出行', icon: '🚗', amount: 5000, percentage: 16 },
                { name: '餐饮娱乐', icon: '🍽️', amount: 4503, percentage: 14 },
                { name: '其他支出', icon: '💼', amount: 2000, percentage: 7 }
              ],
              suggestions: [
                { icon: '💡', text: '日常开销占比较高，建议制定预算计划' },
                { icon: '🏠', text: '房租支出稳定，可考虑长期租赁优惠' },
                { icon: '🚗', text: '交通费用可通过公共交通优化' }
              ]
            };
            break;

          case 'category':
            // 分类统计：显示分类数据
            updateData['reportData.categoryStats'] = mockData.categoryStats;
            break;

          case 'trend':
            // 趋势分析：添加趋势分析数据
            updateData['reportData.trendData'] = mockData.trendData;
            updateData['reportData.trendAnalysis'] = {
              incomeGrowthRate: 12.5,
              expenseGrowthRate: 8.3,
              profitTrend: 'up'
            };
            break;

          case 'health_integration':
            // 健康财务整合
            updateData['reportData.healthIntegrationStats'] = mockData.healthIntegrationStats;
            break;

          case 'comparison':
            // 对比分析：添加对比数据
            updateData['reportData.comparisonData'] = {
              current: { income: 56249, expense: 31503 },
              previous: { income: 48200, expense: 29800 },
              incomeChange: 16.7,
              expenseChange: 5.7
            };
            break;

          default:
            // 默认加载所有数据
            updateData['reportData.categoryStats'] = mockData.categoryStats;
            updateData['reportData.trendData'] = mockData.trendData;
            updateData['reportData.healthIntegrationStats'] = mockData.healthIntegrationStats;
            updateData['reportData.comparisonData'] = mockData.comparisonData;
            break;
        }

        this.setData(updateData);

        console.log(`模拟数据加载成功 - 报表类型: ${reportType}`, mockData);
      }
    } catch (error) {
      console.error('加载模拟数据失败:', error);
    }
  },

  /**
   * 生成模拟交易记录（根据筛选条件动态生成）
   */
  generateMockTransactions() {
    const { timeRange, reportType } = this.data;

    // 根据时间范围生成不同的交易记录
    let transactions = [];

    // 基础交易模板
    const transactionTemplates = [
      { type: 'income', category: '鹅蛋销售', description: '鹅蛋批发销售', baseAmount: 15000 },
      { type: 'expense', category: '饲料采购', description: '优质鹅饲料采购', baseAmount: 3200 },
      { type: 'income', category: '鹅肉销售', description: '鹅肉批发销售', baseAmount: 8500 },
      { type: 'expense', category: '医疗费用', description: '鹅群疫苗接种', baseAmount: 1800 },
      { type: 'income', category: '鹅苗销售', description: '优质鹅苗销售', baseAmount: 12000 },
      { type: 'expense', category: '设备维护', description: '孵化设备维护', baseAmount: 2500 }
    ];

    // 根据报表类型筛选交易记录
    let filteredTemplates = transactionTemplates;
    if (reportType === 'expense') {
      // 支出分析只显示支出记录
      filteredTemplates = transactionTemplates.filter(t => t.type === 'expense');
    } else if (reportType === 'income') {
      // 收入分析只显示收入记录（如果有的话）
      filteredTemplates = transactionTemplates.filter(t => t.type === 'income');
    }

    // 根据时间范围生成对应日期的交易记录
    const today = new Date();
    let startDate, endDate;

    switch (timeRange) {
      case 'thisMonth':
        startDate = new Date(today.getFullYear(), today.getMonth(), 1);
        endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        break;
      case 'lastMonth':
        startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        endDate = new Date(today.getFullYear(), today.getMonth(), 0);
        break;
      case 'thisQuarter':
        const quarter = Math.floor(today.getMonth() / 3);
        startDate = new Date(today.getFullYear(), quarter * 3, 1);
        endDate = new Date(today.getFullYear(), quarter * 3 + 3, 0);
        break;
      default:
        startDate = new Date(today.getFullYear(), today.getMonth(), 1);
        endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    }

    // 生成交易记录
    filteredTemplates.forEach((template, index) => {
      const transactionDate = new Date(startDate.getTime() + (endDate.getTime() - startDate.getTime()) * Math.random());
      const amount = Math.floor(template.baseAmount * (0.8 + Math.random() * 0.4)); // 金额浮动±20%

      transactions.push({
        id: `${Date.now()}_${index}`,
        type: template.type,
        amount: amount.toString(),
        category: template.category,
        description: template.description,
        date: transactionDate.toISOString().split('T')[0]
      });
    });

    // 按日期倒序排列
    transactions.sort((a, b) => new Date(b.date) - new Date(a.date));

    console.log(`生成交易记录 - 时间范围: ${timeRange}, 报表类型: ${reportType}, 记录数: ${transactions.length}`);

    return transactions;
  },

  /**
   * 构建请求参数
   */
  buildParams() {
    const { timeRange, reportType, customDateRange } = this.data;

    const params = {
      timeRange: timeRange,
      reportType: reportType
    };

    if (timeRange === 'custom') {
      params.startDate = customDateRange.start;
      params.endDate = customDateRange.end;
    }

    return params;
  },

  async loadFinanceOverview(params) {
    try {
      // 尝试从API加载数据
      const response = await request.get(
        API.API_ENDPOINTS.WORKSPACE.FINANCE.REPORTS.OVERVIEW,
        params
      );

      if (response && response.success) {
        this.setData({
          'reportData.overview': response.data
        });
      } else {
        throw new Error('API响应失败');
      }
    } catch (error) {
      console.error('加载财务概览失败，使用模拟数据:', error);

      // 使用模拟数据作为回退
      const mockData = FinanceMockDataService.generateOverviewData(params);
      this.setData({
        'reportData.overview': mockData
      });
    }
  },

  async loadCategoryStatistics(params) {
    try {
      // 尝试从API加载数据
      const response = await request.get(
        API.API_ENDPOINTS.WORKSPACE.FINANCE.REPORTS.CATEGORY_STATS,
        params
      );

      if (response && response.success) {
        this.setData({
          'reportData.categoryStats': response.data
        });
      } else {
        throw new Error('API响应失败');
      }
    } catch (error) {
      console.error('加载分类统计失败，使用模拟数据:', error);

      // 使用模拟数据作为回退
      const mockData = FinanceMockDataService.generateCategoryStats(params);
      this.setData({
        'reportData.categoryStats': mockData
      });
    }
  },

  async loadTrendData(params) {
    try {
      // 尝试从API加载数据
      const response = await request.get(
        API.API_ENDPOINTS.WORKSPACE.FINANCE.REPORTS.TREND,
        params
      );

      if (response && response.success) {
        this.setData({
          'reportData.trendData': response.data
        });
      } else {
        throw new Error('API响应失败');
      }
    } catch (error) {
      console.error('加载趋势数据失败，使用模拟数据:', error);

      // 使用模拟数据作为回退
      const mockData = FinanceMockDataService.generateTrendData(params);
      this.setData({
        'reportData.trendData': mockData
      });
    }
  },

  async loadHealthIntegrationData(params) {
    try {
      // 尝试从健康财务整合服务加载数据
      const stats = await healthFinanceIntegration.getHealthFinanceStatistics(params);

      if (stats) {
        this.setData({
          'reportData.healthIntegrationStats': stats
        });
      } else {
        throw new Error('健康财务整合数据为空');
      }
    } catch (error) {
      console.error('加载健康财务整合数据失败，使用模拟数据:', error);

      // 使用模拟数据作为回退
      const mockData = FinanceMockDataService.generateHealthIntegrationData(params);
      this.setData({
        'reportData.healthIntegrationStats': mockData
      });
    }
  },

  async loadComparisonData(params) {
    try {
      // 尝试从API加载数据
      const response = await request.get(
        API.API_ENDPOINTS.WORKSPACE.FINANCE.REPORTS.COMPARISON,
        params
      );

      if (response && response.success) {
        this.setData({
          'reportData.comparisonData': response.data
        });
      } else {
        throw new Error('API响应失败');
      }
    } catch (error) {
      console.error('加载对比数据失败，使用模拟数据:', error);

      // 使用模拟数据作为回退
      const mockData = FinanceMockDataService.generateComparisonData(params);
      this.setData({
        'reportData.comparisonData': mockData
      });
    }
  },

  async loadTransactionData(params) {
    try {
      const response = await request.get(API.FINANCE.TRANSACTIONS, params);

      if (response && response.success && response.data) {
        this.setData({
          'reportData.transactions': response.data.transactions || []
        });
      } else {
        throw new Error('API响应失败');
      }
    } catch (error) {
      console.error('加载交易记录失败，使用模拟数据:', error);

      // 使用模拟数据作为回退
      const mockTransactions = this.generateMockTransactions();
      this.setData({
        'reportData.transactions': mockTransactions
      });
    }
  },

  onTimeRangeChange(e) {
    const index = parseInt(e.detail.value);
    const timeRange = this.data.timeRangeOptions[index].value;

    this.setData({
      timeRange: timeRange
    });

    // 更新计算属性
    this.updateComputedProperties();

    if (timeRange !== 'custom') {
      this.loadReportData();
    }
  },

  onReportTypeChange(e) {
    const index = parseInt(e.detail.value);
    const reportType = this.data.reportTypes[index];
    const currentReportType = this.data.reportType;

    // 如果选择的是当前类型，不需要重新加载
    if (reportType.value === currentReportType) {
      return;
    }

    this.setData({
      selectedReportTypeIndex: index,
      reportType: reportType.value,
      selectedReportTypeLabel: reportType.label
    });

    // 更新计算属性
    this.updateComputedProperties();

    // 重新加载数据
    this.loadReportData().then(() => {
      // 数据加载完成后重新初始化图表
      this.reinitCharts();
    });

    // 显示切换提示
    wx.showToast({
      title: `切换到${reportType.label}`,
      icon: 'none',
      duration: 1500
    });
  },

  onCustomDateChange(e) {
    const { type } = e.currentTarget.dataset;
    const date = e.detail.value;
    
    this.setData({
      [`customDateRange.${type}`]: date
    });
    
    // 如果两个日期都已选择，自动刷新数据
    const { start, end } = this.data.customDateRange;
    if (type === 'end' && start && date) {
      this.loadReportData();
    } else if (type === 'start' && end && date) {
      this.loadReportData();
    }
  },

  onFilterChange(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`filters.${field}`]: value
    });
    
    // 应用过滤器
    this.applyFilters();
  },

  applyFilters() {
    // TODO: 实现过滤逻辑
    console.log('应用过滤器:', this.data.filters);
  },

  onExportReport() {
    const that = this;
    
    wx.showActionSheet({
      itemList: ['导出为Excel', '导出为PDF', '分享报表'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            that.exportToExcel();
            break;
          case 1:
            that.exportToPDF();
            break;
          case 2:
            that.shareReport();
            break;
        }
      }
    });
  },

  async exportToExcel() {
    try {
      wx.showLoading({ title: '生成中...' });
      
      const { reportType, timeRange, reportData } = this.data;
      
      const exportData = {
        reportType,
        timeRange,
        data: reportData,
        generatedAt: new Date().toISOString(),
        generatedBy: this.data.userPermissions.username
      };
      
      const response = await request.post(
        API.API_ENDPOINTS.WORKSPACE.FINANCE.REPORTS.EXPORT_EXCEL,
        exportData
      );
      
      if (response && response.success) {
        wx.downloadFile({
          url: response.data.downloadUrl,
          success: (res) => {
            wx.openDocument({
              filePath: res.tempFilePath,
              success: () => {
                wx.showToast({
                  title: '导出成功',
                  icon: 'success'
                });
              }
            });
          }
        });
      }
    } catch (error) {
      console.error('导出Excel失败:', error);
      wx.showToast({
        title: '导出失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  async exportToPDF() {
    try {
      wx.showLoading({ title: '生成中...' });
      
      // TODO: 实现PDF导出
      wx.showToast({
        title: 'PDF导出功能开发中',
        icon: 'none'
      });
    } catch (error) {
      console.error('导出PDF失败:', error);
    } finally {
      wx.hideLoading();
    }
  },

  shareReport() {
    const { reportType, timeRange } = this.data;
    const reportTypeLabel = this.data.reportTypes.find(t => t.value === reportType)?.label || '';
    const timeRangeLabel = this.data.timeRangeOptions.find(t => t.value === timeRange)?.label || '';
    
    return {
      title: `${reportTypeLabel} - ${timeRangeLabel}`,
      path: `/pages/workspace/finance/reports/reports?type=${reportType}&range=${timeRange}`,
      imageUrl: '/images/share-finance-report.jpg'
    };
  },

  formatCurrency(amount) {
    if (!amount && amount !== 0) return '¥0.00';
    return '¥' + parseFloat(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  },

  formatPercentage(value) {
    if (!value && value !== 0) return '0%';
    const percentage = parseFloat(value);
    return (percentage >= 0 ? '+' : '') + percentage.toFixed(1) + '%';
  },

  formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return `${date.getMonth() + 1}/${date.getDate()}`;
  },

  onShareAppMessage() {
    return this.shareReport();
  },

  /**
   * 切换模拟数据模式（开发工具）
   */
  toggleMockDataMode() {
    const currentMode = wx.getStorageSync('finance_use_mock_data') || false;
    const newMode = !currentMode;

    wx.setStorageSync('finance_use_mock_data', newMode);

    wx.showModal({
      title: '模拟数据模式',
      content: `已${newMode ? '开启' : '关闭'}模拟数据模式，将重新加载数据`,
      showCancel: false,
      success: () => {
        this.loadReportData();
      }
    });
  },

  /**
   * 长按标题触发开发者工具
   */
  onTitleLongPress() {
    const that = this;
    wx.showActionSheet({
      itemList: ['切换模拟数据模式', '清除缓存', '重新加载数据'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            that.toggleMockDataMode();
            break;
          case 1:
            wx.clearStorageSync();
            wx.showToast({
              title: '缓存已清除',
              icon: 'success'
            });
            break;
          case 2:
            that.loadReportData();
            break;
        }
      }
    });
  },

  /**
   * 卡片点击处理
   */
  onCardTap(e) {
    const { type } = e.currentTarget.dataset;
    console.log('点击卡片:', type);

    // 可以根据卡片类型进行不同的处理
    switch (type) {
      case 'income':
        this.showDetailModal('收入详情', '查看详细收入数据');
        break;
      case 'expense':
        this.showDetailModal('支出详情', '查看详细支出数据');
        break;
      case 'profit':
        this.showDetailModal('利润详情', '查看详细利润分析');
        break;
      case 'transaction':
        this.showDetailModal('交易详情', '查看详细交易记录');
        break;
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        });
    }
  },

  /**
   * 分类卡片点击处理
   */
  onCategoryTap(e) {
    const { category } = e.currentTarget.dataset;
    console.log('点击分类:', category);

    wx.showToast({
      title: `查看${category}详情`,
      icon: 'none'
    });
  },

  /**
   * 导出报表
   */
  onExportReport(e) {
    const { type } = e.currentTarget.dataset;

    wx.showLoading({
      title: '正在导出...'
    });

    // 模拟导出过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: `${type === 'excel' ? 'Excel' : 'PDF'}导出成功`,
        icon: 'success'
      });
    }, 2000);
  },

  /**
   * 刷新数据
   */
  onRefreshData() {
    this.setData({ refreshing: true });
    this.loadReportData().finally(() => {
      this.setData({ refreshing: false });
    });
  },

  /**
   * 显示详情模态框
   */
  showDetailModal(title, content) {
    wx.showModal({
      title: title,
      content: content,
      showCancel: false,
      confirmText: '确定'
    });
  },

  /**
   * 图表触摸事件
   */
  onChartTouch(e) {
    console.log('图表触摸事件:', e);
    // 可以添加图表交互逻辑
  },

  /**
   * 交易记录点击处理
   */
  onTransactionTap(e) {
    const transaction = e.currentTarget.dataset.transaction;
    console.log('点击交易记录:', transaction);

    const typeText = transaction.type === 'income' ? '收入' : '支出';
    const content = `类型：${typeText}\n金额：¥${transaction.amount}\n分类：${transaction.category}\n日期：${transaction.date}`;

    wx.showModal({
      title: '交易详情',
      content: content,
      showCancel: false,
      confirmText: '确定'
    });
  },

  /**
   * 初始化图表
   */
  initCharts() {
    try {
      const { reportType } = this.data;

      // 根据报表类型初始化对应的图表
      switch (reportType) {
        case 'overview':
          this.initOverviewChart();
          break;
        case 'income':
          this.initIncomeChart();
          break;
        case 'expense':
          this.initExpenseChart();
          break;
        case 'category':
          this.initCategoryChart();
          break;
        case 'trend':
          this.initTrendChart();
          break;
        case 'comparison':
          this.initComparisonChart();
          break;
        default:
          console.log(`报表类型 ${reportType} 不需要图表`);
      }
    } catch (error) {
      console.error('初始化图表失败:', error);
    }
  },

  /**
   * 初始化财务总览图表
   */
  initOverviewChart() {
    this.initChartHelper('#overviewChart', this.drawOverviewChart);
  },

  /**
   * 绘制财务总览图表（收支趋势对比）- 移动端优化柱状图
   */
  drawOverviewChart(ctx, width, height) {
    try {
      console.log('=== 开始绘制财务总览图表 ===');
      console.log('画布尺寸:', width, 'x', height);

      const { reportData } = this.data;

      // 处理趋势数据，确保格式正确
      let trendData = reportData?.trendData || [];

      // 如果没有数据或数据格式不正确，使用默认数据
      if (!trendData || trendData.length === 0) {
        trendData = [
          { formattedDate: '1月', income: 35000, expense: 28000 },
          { formattedDate: '2月', income: 42000, expense: 31000 },
          { formattedDate: '3月', income: 38000, expense: 29000 },
          { formattedDate: '4月', income: 45000, expense: 32000 }
        ];
      }

      // 只显示最近4个月的数据，避免移动端过于拥挤
      const displayData = trendData.slice(-4).map(item => ({
        label: item.formattedDate || item.month || '未知',
        income: item.income || 0,
        expense: item.expense || 0
      }));

      console.log('显示数据:', displayData);

      if (!displayData || displayData.length === 0) {
        console.warn('没有趋势数据，无法绘制图表');
        // 绘制空状态
        ctx.fillStyle = '#999';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('暂无数据', width / 2, height / 2);
        return;
      }

      // 清空画布并设置背景
      ctx.clearRect(0, 0, width, height);
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, width, height);

      // 设置边距 - 移动端优化
      const leftPadding = 50;
      const rightPadding = 20;
      const topPadding = 40;
      const bottomPadding = 60;

      const chartWidth = width - leftPadding - rightPadding;
      const chartHeight = height - topPadding - bottomPadding;

      // 计算数值范围
      const allValues = [];
      displayData.forEach(item => {
        allValues.push(item.income, item.expense);
      });

      const maxValue = Math.max(...allValues);
      const paddedMaxValue = maxValue * 1.1; // 添加10%边距

      // 绘制坐标轴
      ctx.beginPath();
      ctx.moveTo(leftPadding, topPadding);
      ctx.lineTo(leftPadding, height - bottomPadding);
      ctx.lineTo(width - rightPadding, height - bottomPadding);
      ctx.strokeStyle = '#ddd';
      ctx.lineWidth = 1;
      ctx.stroke();

      // 计算柱状图参数
      const barGroupWidth = chartWidth / displayData.length;
      const barWidth = barGroupWidth * 0.3; // 每个柱子的宽度
      const barSpacing = barWidth * 0.2; // 柱子间距

      // 绘制柱状图
      displayData.forEach((item, index) => {
        const groupX = leftPadding + (index + 0.5) * barGroupWidth;

        // 收入柱子
        const incomeHeight = (item.income / paddedMaxValue) * chartHeight;
        const incomeX = groupX - barWidth - barSpacing / 2;
        const incomeY = height - bottomPadding - incomeHeight;

        ctx.fillStyle = '#4CAF50';
        ctx.fillRect(incomeX, incomeY, barWidth, incomeHeight);

        // 支出柱子
        const expenseHeight = (item.expense / paddedMaxValue) * chartHeight;
        const expenseX = groupX + barSpacing / 2;
        const expenseY = height - bottomPadding - expenseHeight;

        ctx.fillStyle = '#F44336';
        ctx.fillRect(expenseX, expenseY, barWidth, expenseHeight);

        // 绘制X轴标签
        ctx.fillStyle = '#666';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(item.label, groupX, height - bottomPadding + 20);

        // 绘制数值标签
        ctx.font = '10px Arial';
        ctx.fillStyle = '#4CAF50';
        if (incomeHeight > 20) {
          const incomeLabel = item.income >= 1000 ? `${(item.income/1000).toFixed(0)}k` : item.income.toString();
          ctx.fillText(incomeLabel, incomeX + barWidth/2, incomeY - 5);
        }

        ctx.fillStyle = '#F44336';
        if (expenseHeight > 20) {
          const expenseLabel = item.expense >= 1000 ? `${(item.expense/1000).toFixed(0)}k` : item.expense.toString();
          ctx.fillText(expenseLabel, expenseX + barWidth/2, expenseY - 5);
        }
      });

      // 绘制Y轴刻度
      const ySteps = 4;
      for (let i = 0; i <= ySteps; i++) {
        const value = (paddedMaxValue / ySteps) * i;
        const y = height - bottomPadding - (i / ySteps) * chartHeight;

        // 绘制刻度线
        ctx.beginPath();
        ctx.moveTo(leftPadding - 5, y);
        ctx.lineTo(leftPadding, y);
        ctx.strokeStyle = '#ccc';
        ctx.lineWidth = 1;
        ctx.stroke();

        // 绘制刻度标签
        ctx.fillStyle = '#666';
        ctx.font = '10px Arial';
        ctx.textAlign = 'right';
        const formattedValue = value >= 1000 ? `${(value / 1000).toFixed(0)}k` : value.toFixed(0);
        ctx.fillText(formattedValue, leftPadding - 8, y + 3);
      }

      // 绘制图例
      const legendY = topPadding - 15;

      // 收入图例
      ctx.fillStyle = '#4CAF50';
      ctx.fillRect(leftPadding, legendY - 5, 12, 8);
      ctx.fillStyle = '#333';
      ctx.font = '12px Arial';
      ctx.textAlign = 'left';
      ctx.fillText('收入', leftPadding + 18, legendY + 2);

      // 支出图例
      const expenseLegendX = leftPadding + 60;
      ctx.fillStyle = '#F44336';
      ctx.fillRect(expenseLegendX, legendY - 5, 12, 8);
      ctx.fillStyle = '#333';
      ctx.fillText('支出', expenseLegendX + 18, legendY + 2);

      console.log('=== 财务总览柱状图绘制完成 ===');



    } catch (error) {
      console.error('绘制财务总览图表失败:', error);
      // 绘制错误提示 - 优化错误显示
      ctx.clearRect(0, 0, width, height);
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, width, height);

      ctx.fillStyle = '#999';
      ctx.font = '14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('图表加载失败', width / 2, height / 2 - 10);

      ctx.fillStyle = '#ccc';
      ctx.font = '12px Arial';
      ctx.fillText('请稍后重试', width / 2, height / 2 + 15);
    }
  },

  /**
   * 初始化收入趋势图表
   */
  initIncomeChart() {
    try {
      const query = wx.createSelectorQuery().in(this);
      query.select('#incomeChart').fields({ node: true, size: true }).exec((res) => {
        if (res[0] && res[0].node) {
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');

          if (!ctx) {
            console.error('无法获取Canvas 2D上下文');
            return;
          }

          // 设置画布尺寸
          const deviceInfo = wx.getDeviceInfo();
          const dpr = deviceInfo.pixelRatio || 1;
          canvas.width = res[0].width * dpr;
          canvas.height = res[0].height * dpr;
          ctx.scale(dpr, dpr);

          // 绘制收入趋势线图
          this.drawIncomeLineChart(ctx, res[0].width, res[0].height);
        } else {
          console.warn('收入图表Canvas元素未找到');
        }
      });
    } catch (error) {
      console.error('初始化收入图表失败:', error);
    }
  },

  /**
   * 绘制收入趋势线图
   */
  drawIncomeLineChart(ctx, width, height) {
    // 模拟收入趋势数据
    const data = [
      { month: '1月', income: 35000 },
      { month: '2月', income: 42000 },
      { month: '3月', income: 38000 },
      { month: '4月', income: 45000 },
      { month: '5月', income: 52000 },
      { month: '6月', income: 48000 }
    ];

    const padding = 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    // 设置样式
    ctx.strokeStyle = '#667eea';
    ctx.fillStyle = '#667eea';
    ctx.lineWidth = 3;
    ctx.font = '12px Arial';

    // 绘制坐标轴
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.strokeStyle = '#e0e0e0';
    ctx.lineWidth = 1;
    ctx.stroke();

    // 计算数据点位置
    const maxIncome = Math.max(...data.map(d => d.income));
    const minIncome = Math.min(...data.map(d => d.income));
    const incomeRange = maxIncome - minIncome;

    const points = data.map((item, index) => {
      const x = padding + (index / (data.length - 1)) * chartWidth;
      const y = height - padding - ((item.income - minIncome) / incomeRange) * chartHeight;
      return { x, y, ...item };
    });

    // 绘制趋势线
    ctx.beginPath();
    ctx.strokeStyle = '#667eea';
    ctx.lineWidth = 3;
    points.forEach((point, index) => {
      if (index === 0) {
        ctx.moveTo(point.x, point.y);
      } else {
        ctx.lineTo(point.x, point.y);
      }
    });
    ctx.stroke();

    // 绘制数据点
    ctx.fillStyle = '#667eea';
    points.forEach(point => {
      ctx.beginPath();
      ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI);
      ctx.fill();
    });

    // 绘制标签
    ctx.fillStyle = '#666';
    ctx.textAlign = 'center';
    points.forEach(point => {
      ctx.fillText(point.month, point.x, height - padding + 20);
      ctx.fillText(`¥${(point.income / 1000).toFixed(0)}k`, point.x, point.y - 10);
    });
  },

  /**
   * 初始化支出分析图表
   */
  initExpenseChart() {
    this.initChartHelper('#expenseChart', this.drawExpensePieChart);
  },

  /**
   * 绘制支出分类饼图
   */
  drawExpensePieChart(ctx, width, height) {
    const { reportData } = this.data;
    const categories = reportData.expenseAnalysis?.categories || [
      { name: '日常开销', amount: 12000, percentage: 38, color: '#667eea' },
      { name: '房租水电', amount: 8000, percentage: 25, color: '#764ba2' },
      { name: '交通出行', amount: 5000, percentage: 16, color: '#f093fb' },
      { name: '餐饮娱乐', amount: 4503, percentage: 14, color: '#f5576c' },
      { name: '其他支出', amount: 2000, percentage: 7, color: '#4facfe' }
    ];

    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 3;

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    let currentAngle = -Math.PI / 2; // 从顶部开始

    categories.forEach((category, index) => {
      const sliceAngle = (category.percentage / 100) * 2 * Math.PI;

      // 绘制扇形
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
      ctx.closePath();

      // 设置颜色
      const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe'];
      ctx.fillStyle = colors[index % colors.length];
      ctx.fill();

      // 绘制边框
      ctx.strokeStyle = '#fff';
      ctx.lineWidth = 2;
      ctx.stroke();

      // 绘制标签（优化位置避免截断）
      const labelAngle = currentAngle + sliceAngle / 2;
      const labelDistance = radius + 25; // 减少距离避免超出边界
      let labelX = centerX + Math.cos(labelAngle) * labelDistance;
      let labelY = centerY + Math.sin(labelAngle) * labelDistance;

      // 边界检查和调整
      const margin = 40;
      labelX = Math.max(margin, Math.min(width - margin, labelX));
      labelY = Math.max(20, Math.min(height - 20, labelY));

      ctx.fillStyle = '#333';
      ctx.font = '11px Arial';

      // 根据位置调整文字对齐方式
      if (labelX < centerX) {
        ctx.textAlign = 'right';
      } else if (labelX > centerX) {
        ctx.textAlign = 'left';
      } else {
        ctx.textAlign = 'center';
      }

      // 绘制分类名称（简化长名称）
      const shortName = category.name.length > 4 ? category.name.substring(0, 4) : category.name;
      ctx.fillText(shortName, labelX, labelY);
      ctx.fillText(`${category.percentage}%`, labelX, labelY + 12);

      currentAngle += sliceAngle;
    });
  },

  /**
   * 初始化分类统计图表
   */
  initCategoryChart() {
    this.initChartHelper('#categoryChart', this.drawCategoryBarChart);
  },

  /**
   * 绘制分类对比柱状图
   */
  drawCategoryBarChart(ctx, width, height) {
    try {
      console.log('=== 开始绘制分类图表 ===');
      console.log('画布尺寸:', width, 'x', height);
      console.log('Canvas上下文:', ctx);

      if (!ctx) {
        console.error('Canvas上下文为空，无法绘制');
        return;
      }

      const { reportData } = this.data;
      console.log('报表数据:', reportData);

      const categories = reportData?.categoryStats || [
        { category: '鹅蛋销售', income: 25000, expense: 5000, net: 20000, icon: '🥚' },
        { category: '鹅肉销售', income: 18000, expense: 8000, net: 10000, icon: '🍖' },
        { category: '鹅苗销售', income: 12000, expense: 3000, net: 9000, icon: '🐣' },
        { category: '饲料采购', income: 0, expense: 15000, net: -15000, icon: '🌾' }
      ];

      console.log('分类数据:', categories);

      // 限制显示的分类数量，避免过于拥挤
      const displayCategories = categories.slice(0, 4);
      console.log('显示的分类:', displayCategories);

      const padding = 30;
      const chartWidth = width - padding * 2;
      const chartHeight = height - padding * 2 - 40; // 为底部标签留空间
      const groupWidth = chartWidth / displayCategories.length;
      const barWidth = Math.min(groupWidth * 0.25, 20); // 限制柱子宽度
      const barSpacing = 3;

      console.log('图表参数:', { padding, chartWidth, chartHeight, groupWidth, barWidth, barSpacing });

      // 清空画布并设置背景
      ctx.clearRect(0, 0, width, height);
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, width, height);

      // 计算最大值用于缩放
      const maxValue = Math.max(...displayCategories.flatMap(c => [c.income, c.expense]));

      console.log('分类图表数据:', displayCategories);
      console.log('最大值:', maxValue);

      if (maxValue === 0) {
        // 如果没有数据，显示提示
        ctx.fillStyle = '#666';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('暂无数据', width / 2, height / 2);
        return;
      }

      // 绘制坐标轴
      ctx.beginPath();
      ctx.moveTo(padding, padding);
      ctx.lineTo(padding, height - padding - 40);
      ctx.lineTo(width - padding, height - padding - 40);
      ctx.strokeStyle = '#e0e0e0';
      ctx.lineWidth = 1;
      ctx.stroke();

      // 绘制Y轴刻度
      const ySteps = 4;
      for (let i = 0; i <= ySteps; i++) {
        const y = height - padding - 40 - (i / ySteps) * chartHeight;
        const value = (maxValue / ySteps) * i;

        // 绘制刻度线
        ctx.beginPath();
        ctx.moveTo(padding - 3, y);
        ctx.lineTo(padding, y);
        ctx.strokeStyle = '#ccc';
        ctx.lineWidth = 1;
        ctx.stroke();

        // 绘制刻度标签
        ctx.fillStyle = '#666';
        ctx.font = '9px Arial';
        ctx.textAlign = 'right';
        ctx.fillText(`¥${(value / 1000).toFixed(0)}k`, padding - 5, y + 3);
      }

      displayCategories.forEach((category, index) => {
        const centerX = padding + 10 + (index + 0.5) * groupWidth;
        const leftBarX = centerX - barWidth - barSpacing / 2;
        const rightBarX = centerX + barSpacing / 2;

        console.log(`绘制分类 ${index}: ${category.category}, 收入: ${category.income}, 支出: ${category.expense}`);

        // 绘制收入柱
        const incomeHeight = (category.income / maxValue) * chartHeight;
        if (incomeHeight > 2) { // 最小高度2px
          ctx.fillStyle = '#4CAF50';
          const incomeY = height - padding - 40 - incomeHeight;
          ctx.fillRect(leftBarX, incomeY, barWidth, incomeHeight);
          console.log(`收入柱: x=${leftBarX}, y=${incomeY}, w=${barWidth}, h=${incomeHeight}`);
        }

        // 绘制支出柱
        const expenseHeight = (category.expense / maxValue) * chartHeight;
        if (expenseHeight > 2) { // 最小高度2px
          ctx.fillStyle = '#F44336';
          const expenseY = height - padding - 40 - expenseHeight;
          ctx.fillRect(rightBarX, expenseY, barWidth, expenseHeight);
          console.log(`支出柱: x=${rightBarX}, y=${expenseY}, w=${barWidth}, h=${expenseHeight}`);
        }

        // 绘制分类标签（简化名称）
        ctx.fillStyle = '#666';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        const shortName = category.category.length > 4 ? category.category.substring(0, 4) : category.category;
        ctx.fillText(shortName, centerX, height - padding - 25);
      });

      // 绘制图例（放在右上角）
      const legendX = width - 80;
      const legendY = padding + 10;

      ctx.fillStyle = '#4CAF50';
      ctx.fillRect(legendX, legendY, 12, 12);
      ctx.fillStyle = '#333';
      ctx.font = '11px Arial';
      ctx.textAlign = 'left';
      ctx.fillText('收入', legendX + 18, legendY + 9);

      ctx.fillStyle = '#F44336';
      ctx.fillRect(legendX, legendY + 20, 12, 12);
      ctx.fillStyle = '#333';
      ctx.fillText('支出', legendX + 18, legendY + 29);

    } catch (error) {
      console.error('绘制分类对比图表失败:', error);
      // 绘制错误提示
      ctx.fillStyle = '#ff0000';
      ctx.font = '14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('图表加载失败', width / 2, height / 2);
    }
  },

  /**
   * 初始化趋势分析图表
   */
  initTrendChart() {
    this.initChartHelper('#trendChart', this.drawTrendLineChart);
  },

  /**
   * 绘制收支趋势对比线图
   */
  drawTrendLineChart(ctx, width, height) {
    try {
      const trendData = [
        { month: '1月', income: 35000, expense: 28000 },
        { month: '2月', income: 42000, expense: 31000 },
        { month: '3月', income: 38000, expense: 29000 },
        { month: '4月', income: 45000, expense: 32000 },
        { month: '5月', income: 52000, expense: 35000 },
        { month: '6月', income: 48000, expense: 33000 }
      ];

      const padding = 50;
      const chartWidth = width - padding * 2;
      const chartHeight = height - padding * 2;

      // 清空画布并设置背景
      ctx.clearRect(0, 0, width, height);
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, width, height);

    // 绘制坐标轴
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.strokeStyle = '#e0e0e0';
    ctx.lineWidth = 1;
    ctx.stroke();

    // 计算数据范围
    const allValues = trendData.flatMap(d => [d.income, d.expense]);
    const maxValue = Math.max(...allValues);
    const minValue = Math.min(...allValues);
    const valueRange = maxValue - minValue;

    // 绘制Y轴刻度和标签
    const ySteps = 5;
    for (let i = 0; i <= ySteps; i++) {
      const value = minValue + (valueRange / ySteps) * i;
      const y = height - padding - (i / ySteps) * chartHeight;

      // 绘制刻度线
      ctx.beginPath();
      ctx.moveTo(padding - 5, y);
      ctx.lineTo(padding, y);
      ctx.strokeStyle = '#ccc';
      ctx.lineWidth = 1;
      ctx.stroke();

      // 绘制刻度标签
      ctx.fillStyle = '#666';
      ctx.font = '10px Arial';
      ctx.textAlign = 'right';
      ctx.fillText(`¥${(value / 1000).toFixed(0)}k`, padding - 8, y + 3);
    }

    // 计算数据点位置
    const incomePoints = trendData.map((item, index) => {
      const x = padding + (index / (trendData.length - 1)) * chartWidth;
      const y = height - padding - ((item.income - minValue) / valueRange) * chartHeight;
      return { x, y, value: item.income, month: item.month };
    });

    const expensePoints = trendData.map((item, index) => {
      const x = padding + (index / (trendData.length - 1)) * chartWidth;
      const y = height - padding - ((item.expense - minValue) / valueRange) * chartHeight;
      return { x, y, value: item.expense, month: item.month };
    });

    // 绘制收入趋势线
    ctx.beginPath();
    ctx.strokeStyle = '#4CAF50';
    ctx.lineWidth = 3;
    incomePoints.forEach((point, index) => {
      if (index === 0) {
        ctx.moveTo(point.x, point.y);
      } else {
        ctx.lineTo(point.x, point.y);
      }
    });
    ctx.stroke();

    // 绘制支出趋势线
    ctx.beginPath();
    ctx.strokeStyle = '#F44336';
    ctx.lineWidth = 3;
    expensePoints.forEach((point, index) => {
      if (index === 0) {
        ctx.moveTo(point.x, point.y);
      } else {
        ctx.lineTo(point.x, point.y);
      }
    });
    ctx.stroke();

    // 绘制收入数据点
    ctx.fillStyle = '#4CAF50';
    incomePoints.forEach(point => {
      ctx.beginPath();
      ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI);
      ctx.fill();
    });

    // 绘制支出数据点
    ctx.fillStyle = '#F44336';
    expensePoints.forEach(point => {
      ctx.beginPath();
      ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI);
      ctx.fill();
    });

    // 绘制月份标签
    ctx.fillStyle = '#666';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    incomePoints.forEach(point => {
      ctx.fillText(point.month, point.x, height - padding + 20);
    });

    // 绘制图例（移到左下角避免遮挡）
    const legendX = padding + 10;
    const legendY = height - padding - 60;

    ctx.fillStyle = '#4CAF50';
    ctx.fillRect(legendX, legendY, 12, 12);
    ctx.fillStyle = '#333';
    ctx.font = '11px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('收入', legendX + 18, legendY + 9);

    ctx.fillStyle = '#F44336';
    ctx.fillRect(legendX + 60, legendY, 12, 12);
    ctx.fillStyle = '#333';
    ctx.fillText('支出', legendX + 78, legendY + 9);

    } catch (error) {
      console.error('绘制趋势线图失败:', error);
      // 绘制错误提示
      ctx.fillStyle = '#ff0000';
      ctx.font = '14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('图表加载失败', width / 2, height / 2);
    }
  },

  /**
   * 初始化对比分析图表
   */
  initComparisonChart() {
    this.initChartHelper('#comparisonChart', this.drawComparisonBarChart);
  },

  /**
   * 绘制同比/环比对比柱状图
   */
  drawComparisonBarChart(ctx, width, height) {
    try {
      console.log('=== 开始绘制对比分析图表 ===');
      console.log('画布尺寸:', width, 'x', height);

      const { reportData } = this.data;
      const comparisonData = reportData.comparisonData || {
        current: { income: 56249, expense: 31503 },
        previous: { income: 48200, expense: 29800 }
      };

      const data = [
        { label: '收入', current: comparisonData.current.income, previous: comparisonData.previous.income },
        { label: '支出', current: comparisonData.current.expense, previous: comparisonData.previous.expense }
      ];

      // 优化padding，减少空白区域
      const padding = Math.min(40, width * 0.1); // 动态padding，最大40px
      const bottomPadding = 50; // 底部留更多空间给标签
      const topPadding = 30; // 顶部留少量空间

      const chartWidth = width - padding * 2;
      const chartHeight = height - bottomPadding - topPadding;

      // 优化柱子宽度计算，让柱子更粗更明显
      const groupSpacing = chartWidth / data.length;
      const barGroupWidth = groupSpacing * 0.7; // 增加组宽度占比
      const barWidth = Math.max(barGroupWidth / 2.5, 25); // 确保最小宽度25px
      const barSpacing = 8; // 柱子间距

      console.log('图表参数:', { padding, chartWidth, chartHeight, barWidth, barSpacing });

      // 清空画布并设置背景
      ctx.clearRect(0, 0, width, height);
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, width, height);

      // 计算最大值用于缩放
      const maxValue = Math.max(...data.flatMap(d => [d.current, d.previous]));
      console.log('最大值:', maxValue);

      if (maxValue === 0) {
        // 如果没有数据，显示提示
        ctx.fillStyle = '#666';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('暂无对比数据', width / 2, height / 2);
        return;
      }

      // 绘制坐标轴
      ctx.beginPath();
      ctx.moveTo(padding, topPadding);
      ctx.lineTo(padding, height - bottomPadding);
      ctx.lineTo(width - padding, height - bottomPadding);
      ctx.strokeStyle = '#e0e0e0';
      ctx.lineWidth = 1;
      ctx.stroke();

      // 绘制Y轴刻度和网格线
      const ySteps = 5; // 增加刻度数量
      for (let i = 0; i <= ySteps; i++) {
        const y = height - bottomPadding - (i / ySteps) * chartHeight;
        const value = (maxValue / ySteps) * i;

        // 绘制网格线（除了底线）
        if (i > 0) {
          ctx.beginPath();
          ctx.moveTo(padding, y);
          ctx.lineTo(width - padding, y);
          ctx.strokeStyle = '#f0f0f0';
          ctx.lineWidth = 0.5;
          ctx.stroke();
        }

        // 绘制刻度线
        ctx.beginPath();
        ctx.moveTo(padding - 3, y);
        ctx.lineTo(padding, y);
        ctx.strokeStyle = '#ccc';
        ctx.lineWidth = 1;
        ctx.stroke();

        // 绘制刻度标签
        ctx.fillStyle = '#666';
        ctx.font = '11px Arial';
        ctx.textAlign = 'right';
        ctx.fillText(`¥${(value / 1000).toFixed(0)}k`, padding - 5, y + 4);
      }

      // 绘制柱状图
      data.forEach((item, index) => {
        const groupCenterX = padding + (index + 0.5) * groupSpacing;
        const leftBarX = groupCenterX - barWidth - barSpacing / 2;
        const rightBarX = groupCenterX + barSpacing / 2;

        console.log(`绘制分组 ${index}: ${item.label}, 本期: ${item.current}, 上期: ${item.previous}`);

        // 绘制本期柱（蓝色渐变）
        const currentHeight = (item.current / maxValue) * chartHeight;
        if (currentHeight > 2) {
          const gradient = ctx.createLinearGradient(0, height - bottomPadding - currentHeight, 0, height - bottomPadding);
          gradient.addColorStop(0, '#667eea');
          gradient.addColorStop(1, '#764ba2');
          ctx.fillStyle = gradient;
          ctx.fillRect(leftBarX, height - bottomPadding - currentHeight, barWidth, currentHeight);

          // 添加柱子顶部高光
          ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
          ctx.fillRect(leftBarX, height - bottomPadding - currentHeight, barWidth, Math.min(currentHeight * 0.3, 10));
        }

        // 绘制上期柱（紫色渐变）
        const previousHeight = (item.previous / maxValue) * chartHeight;
        if (previousHeight > 2) {
          const gradient = ctx.createLinearGradient(0, height - bottomPadding - previousHeight, 0, height - bottomPadding);
          gradient.addColorStop(0, '#f093fb');
          gradient.addColorStop(1, '#f5576c');
          ctx.fillStyle = gradient;
          ctx.fillRect(rightBarX, height - bottomPadding - previousHeight, barWidth, previousHeight);

          // 添加柱子顶部高光
          ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
          ctx.fillRect(rightBarX, height - bottomPadding - previousHeight, barWidth, Math.min(previousHeight * 0.3, 10));
        }

        // 绘制X轴标签
        ctx.fillStyle = '#333';
        ctx.font = 'bold 13px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(item.label, groupCenterX, height - bottomPadding + 20);

        // 绘制数值标签（在柱子上方）
        ctx.font = '10px Arial';
        ctx.fillStyle = '#333';
        if (currentHeight > 20) {
          ctx.fillText(`¥${(item.current / 1000).toFixed(0)}k`, leftBarX + barWidth / 2, height - bottomPadding - currentHeight - 5);
        }
        if (previousHeight > 20) {
          ctx.fillText(`¥${(item.previous / 1000).toFixed(0)}k`, rightBarX + barWidth / 2, height - bottomPadding - previousHeight - 5);
        }
      });

      // 绘制图例（优化位置和样式）
      const legendX = width - 90;
      const legendY = topPadding + 5;

      // 本期图例
      const gradient1 = ctx.createLinearGradient(0, 0, 0, 12);
      gradient1.addColorStop(0, '#667eea');
      gradient1.addColorStop(1, '#764ba2');
      ctx.fillStyle = gradient1;
      ctx.fillRect(legendX, legendY, 14, 12);
      ctx.fillStyle = '#333';
      ctx.font = 'bold 12px Arial';
      ctx.textAlign = 'left';
      ctx.fillText('本期', legendX + 20, legendY + 9);

      // 上期图例
      const gradient2 = ctx.createLinearGradient(0, 0, 0, 12);
      gradient2.addColorStop(0, '#f093fb');
      gradient2.addColorStop(1, '#f5576c');
      ctx.fillStyle = gradient2;
      ctx.fillRect(legendX, legendY + 18, 14, 12);
      ctx.fillStyle = '#333';
      ctx.fillText('上期', legendX + 20, legendY + 27);

      console.log('对比分析图表绘制完成');

    } catch (error) {
      console.error('绘制对比分析图表失败:', error);
      // 绘制错误提示
      ctx.fillStyle = '#ff0000';
      ctx.font = '14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('图表加载失败', width / 2, height / 2);
    }
  },

  /**
   * 重新初始化图表（当报表类型改变时调用）
   */
  reinitCharts() {
    // 清理现有图表
    this.clearCharts();

    // 延迟重新初始化，确保DOM已更新
    setTimeout(() => {
      console.log('重新初始化图表，当前报表类型:', this.data.reportType);
      this.initCharts();
    }, 500); // 增加延迟时间
  },

  /**
   * 清理图表资源
   */
  clearCharts() {
    // 清理图表相关的资源
    // 这里可以添加具体的清理逻辑，比如清除定时器、事件监听器等
    console.log('清理图表资源');
  },

  /**
   * 通用图表初始化辅助方法
   */
  initChartHelper(selector, drawFunction) {
    try {
      console.log(`开始初始化图表: ${selector}`);
      const query = wx.createSelectorQuery().in(this);
      query.select(selector).fields({ node: true, size: true }).exec((res) => {
        console.log(`图表查询结果 ${selector}:`, res);

        if (res[0] && res[0].node) {
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');

          if (!ctx) {
            console.error(`无法获取${selector}的Canvas 2D上下文`);
            return;
          }

          console.log(`成功获取Canvas上下文 ${selector}, 尺寸: ${res[0].width}x${res[0].height}`);

          // 设置画布尺寸
          const deviceInfo = wx.getDeviceInfo();
          const dpr = deviceInfo.pixelRatio || 1;
          canvas.width = res[0].width * dpr;
          canvas.height = res[0].height * dpr;
          ctx.scale(dpr, dpr);

          // 调用绘制函数
          console.log(`开始绘制图表 ${selector}`);
          drawFunction.call(this, ctx, res[0].width, res[0].height);
          console.log(`完成绘制图表 ${selector}`);
        } else {
          console.warn(`图表Canvas元素${selector}未找到, 查询结果:`, res);
          // 如果Canvas未找到，尝试在页面上显示错误信息
          this.setData({
            [`chartError_${selector.replace('#', '')}`]: '图表初始化失败：Canvas元素未找到'
          });
        }
      });
    } catch (error) {
      console.error(`初始化图表${selector}失败:`, error);
      // 设置错误信息到页面数据
      this.setData({
        [`chartError_${selector.replace('#', '')}`]: `图表初始化失败：${error.message}`
      });
    }
  }
})