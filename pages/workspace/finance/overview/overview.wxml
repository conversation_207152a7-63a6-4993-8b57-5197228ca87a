<!--pages/workspace/finance/overview/overview.wxml-->
<view class="container">
  <!-- 头部标题 -->
  <view class="header">
    <text class="title">财务概览</text>
    <text class="subtitle">查看财务数据统计和趋势分析</text>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <view wx:else>

    <!-- 个人财务摘要（普通员工可见） -->
    <view wx:if="{{showPersonalSummary}}" class="personal-summary-section">
      <view class="stats-grid">
        <view class="stat-card">
          <text class="stat-label">总申请数</text>
          <text class="stat-value">{{personalSummary.totalApplications}}</text>
        </view>
        <view class="stat-card">
          <text class="stat-label">待审批</text>
          <text class="stat-value pending">{{personalSummary.pendingApplications}}</text>
        </view>
        <view class="stat-card">
          <text class="stat-label">已批准金额</text>
          <text class="stat-value approved">¥{{personalSummary.approvedAmount}}</text>
        </view>
        <view class="stat-card">
          <text class="stat-label">被退回</text>
          <text class="stat-value rejected">{{personalSummary.rejectedCount}}</text>
        </view>
      </view>
    </view>

    <!-- 完整财务概览统计卡片（财务人员及以上可见） -->
    <view wx:if="{{hasFinanceAccess && !isEmployee}}" class="stats-section">
      <view class="section-title">
        <text class="title-text">财务概览</text>
        <text class="title-desc">实时财务数据统计</text>
      </view>

      <view class="stats-grid">
        <view class="stat-card">
          <text class="stat-label">本月收入</text>
          <text class="stat-value income">¥{{statistics.monthlyIncome}}</text>
        </view>
        <view class="stat-card">
          <text class="stat-label">本月支出</text>
          <text class="stat-value expense">¥{{statistics.monthlyExpense}}</text>
        </view>
        <view class="stat-card">
          <text class="stat-label">待审批金额</text>
          <text class="stat-value pending">¥{{statistics.pendingAmount}}</text>
        </view>
        <view class="stat-card">
          <text class="stat-label">已批准金额</text>
          <text class="stat-value approved">¥{{statistics.approvedAmount}}</text>
        </view>
      </view>
    </view>

    <!-- 财务管理功能模块 -->
    <view class="finance-modules-section">
      <view class="section-title">
        <text class="title-text">财务管理功能</text>
        <text class="title-desc">统一管理所有财务相关业务</text>
      </view>

      <!-- 6宫格功能模块 -->
      <view class="modules-grid-6">
        <view wx:for="{{financeModules}}" wx:key="id"
              class="module-grid-item"
              bindtap="onModuleTap"
              data-url="{{item.url}}">
          <view class="grid-item-icon" style="background-color: {{item.color}}20">
            <text class="icon-text" style="color: {{item.color}}">{{item.icon}}</text>
          </view>
          <text class="grid-item-title">{{item.title}}</text>
        </view>
      </view>
    </view>

    <!-- 业务类型统计 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">业务类型统计</text>
        <text class="section-more" bindtap="onViewReports">查看报表</text>
      </view>
      <view class="business-stats">
        <view
          wx:for="{{businessTypeStats}}"
          wx:key="type"
          class="business-item"
          bindtap="onViewApplications"
          data-type="{{item.type}}"
        >
          <view class="business-info">
            <text class="business-name">{{item.name}}</text>
            <text class="business-count">{{item.count}}笔</text>
          </view>
          <text class="business-amount">¥{{item.amount}}</text>
        </view>
      </view>
    </view>

    <!-- 最近记录 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">最近记录</text>
        <text class="section-more" bindtap="onViewApplications">查看全部</text>
      </view>

      <view wx:if="{{recentRecords.length === 0}}" class="empty-records">
        <text>暂无财务记录</text>
      </view>

      <view wx:else class="records-list">
        <view
          wx:for="{{recentRecords}}"
          wx:key="id"
          class="record-item"
          bindtap="onViewRecord"
          data-id="{{item.id}}"
          data-type="{{item.business_type}}"
        >
          <view class="record-info">
            <text class="record-title">{{businessTypeMap[item.business_type]}}</text>
            <text class="record-desc">{{item.title || item.description}}</text>
            <text class="record-time">{{item.created_at}}</text>
          </view>
          <view class="record-amount">
            <text class="amount">¥{{item.amount}}</text>
            <view class="status-tag {{item.status}}">{{item.status_text}}</view>
          </view>
        </view>
      </view>
    </view>


  </view>
</view>