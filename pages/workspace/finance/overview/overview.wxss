/* pages/workspace/finance/overview/overview.wxss */

/* 个人财务摘要样式 */
.personal-summary-section {
  margin: 30rpx;
}


/* 统计卡片样式调整 */
.stat-card .stat-value.rejected {
  color: #ff4757;
}

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background: linear-gradient(135deg, #1890ff, #36cfc9);
  padding: 40rpx 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  color: white;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  margin-top: 10rpx;
  display: block;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-card {
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
}

.stat-value.income {
  color: #52c41a;
}

.stat-value.expense {
  color: #ff4d4f;
}

.stat-value.pending {
  color: #faad14;
}

.stat-value.approved {
  color: #1890ff;
}

.section {
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 26rpx;
  color: #1890ff;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background: rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
}

.section-more:active {
  background: rgba(24, 144, 255, 0.2);
  transform: scale(0.95);
}

.business-stats {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.business-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.business-item:last-child {
  border-bottom: none;
}

.business-item:active {
  background-color: #f5f5f5;
}

.business-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.business-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.business-count {
  font-size: 24rpx;
  color: #999;
}

.business-amount {
  font-size: 30rpx;
  font-weight: bold;
  color: #1890ff;
}

.records-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item:active {
  background-color: #f5f5f5;
}

.record-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

.record-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.record-desc {
  font-size: 26rpx;
  color: #666;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-amount {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.amount {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.status-tag {
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  color: white;
}

.status-tag.pending {
  background: #faad14;
}

.status-tag.approved {
  background: #52c41a;
}

.status-tag.rejected {
  background: #ff4d4f;
}

.empty-records {
  background: white;
  padding: 60rpx;
  text-align: center;
  border-radius: 16rpx;
  color: #999;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}



/* ==================== 通用区块样式 ==================== */
.section-title {
  margin-bottom: 24rpx;
}

.title-text {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.title-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
}

/* ==================== 财务管理功能模块 ==================== */
.finance-modules-section {
  margin: 32rpx 0;
}

/* 6宫格布局 */
.modules-grid-6 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
  padding: 0 16rpx;
}

.module-grid-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  min-height: 160rpx;
  justify-content: center;
}

.module-grid-item:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

.grid-item-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.icon-text {
  font-size: 32rpx;
}

.grid-item-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

/* 响应式适配 - 小屏幕设备 */
@media (max-width: 750rpx) {
  .modules-grid-6 {
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
  }

  .module-grid-item {
    min-height: 140rpx;
    padding: 24rpx 12rpx;
  }

  .grid-item-icon {
    width: 56rpx;
    height: 56rpx;
  }

  .icon-text {
    font-size: 28rpx;
  }

  .grid-item-title {
    font-size: 24rpx;
  }
}

.stats-section {
  margin: 32rpx 0;
}