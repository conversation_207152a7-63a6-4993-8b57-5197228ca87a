// pages/workspace/finance/overview/overview.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');
const {
  checkPageAccess,
  getCurrentUser,
  hasRoleOr<PERSON>igher,
  ROLES,
  handlePermissionDenied
} = require('../../../../utils/permission-checker.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    refreshing: false,

    // 权限控制
    hasFinanceAccess: false,
    userRole: '',
    isEmployee: false,
    showPersonalSummary: false,

    // 财务统计数据
    statistics: {
      totalIncome: 0,
      totalExpense: 0,
      monthlyIncome: 0,
      monthlyExpense: 0,
      pendingAmount: 0,
      approvedAmount: 0
    },

    // 个人财务摘要（普通员工可见）
    personalSummary: {
      totalApplications: 0,
      pendingApplications: 0,
      approvedAmount: 0,
      rejectedCount: 0
    },



    // 最近财务记录
    recentRecords: [],

    // 月度趋势数据
    monthlyTrend: [],

    // 业务类型统计
    businessTypeStats: [],

    // 业务类型映射
    businessTypeMap: {
      'expense': '费用报销',
      'purchase': '采购申请',
      'payment': '付款申请',
      'contract': '合同申请',
      'activity': '活动经费',
      'reserve': '备用金'
    },

    // 财务管理功能模块
    financeModules: [
      {
        id: 'expense',
        title: '费用报销',
        description: '员工费用报销申请与管理',
        icon: '💰',
        color: '#1890ff',
        url: '/pages/workspace/expense/list/list',
        applyUrl: '/pages/workspace/expense/apply/apply'
      },
      {
        id: 'purchase',
        title: '采购申请',
        description: '物资采购申请与供应商管理',
        icon: '🛒',
        color: '#52c41a',
        url: '/pages/workspace/purchase/list/list',
        applyUrl: '/pages/workspace/purchase/apply/apply'
      },
      {
        id: 'payment',
        title: '付款申请',
        description: '对外付款申请与资金管理',
        icon: '💳',
        color: '#fa8c16',
        url: '/pages/workspace/payment/list/list',
        applyUrl: '/pages/workspace/payment/apply/apply'
      },
      {
        id: 'contract',
        title: '合同申请',
        description: '合同签署申请与档案管理',
        icon: '📄',
        color: '#722ed1',
        url: '/pages/workspace/contract/list/list',
        applyUrl: '/pages/workspace/contract/apply/apply'
      },
      {
        id: 'activity',
        title: '活动经费',
        description: '活动经费申请与预算管理',
        icon: '🎉',
        color: '#eb2f96',
        url: '/pages/workspace/activity/list/list',
        applyUrl: '/pages/workspace/activity/apply/apply'
      },
      {
        id: 'reserve',
        title: '备用金',
        description: '备用金申请与使用管理',
        icon: '🏦',
        color: '#13c2c2',
        url: '/pages/workspace/reserve/list/list',
        applyUrl: '/pages/workspace/reserve/apply/apply'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查页面访问权限
    if (!this.checkPermissions()) {
      return;
    }

    this.loadFinanceOverview();
  },

  /**
   * 检查页面访问权限
   */
  checkPermissions() {
    const currentUser = getCurrentUser();
    const hasAccess = checkPageAccess('finance/overview');

    this.setData({
      hasFinanceAccess: hasAccess,
      userRole: currentUser.role,
      isEmployee: currentUser.role === ROLES.EMPLOYEE,
      showPersonalSummary: currentUser.role === ROLES.EMPLOYEE
    });

    if (!hasAccess) {
      // 普通员工显示个人财务摘要而不是完全禁止访问
      if (currentUser.role === ROLES.EMPLOYEE) {
        this.setData({ showPersonalSummary: true });
        this.loadPersonalSummary();
        return true; // 允许继续，但只显示个人数据
      } else {
        // 其他情况静默跳转到工作台首页
        wx.switchTab({
          url: '/pages/workspace/workspace'
        });
        return false;
      }
    }

    return true;
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.refreshData();
  },

  /**
   * 加载财务概览数据
   */
  async loadFinanceOverview() {
    // 如果是普通员工，只加载个人摘要
    if (this.data.isEmployee) {
      return this.loadPersonalSummary();
    }

    try {
      this.setData({ loading: true });

      // 并行加载多个数据
      const [statisticsRes, recordsRes, trendRes] = await Promise.all([
        request.get(API.API_ENDPOINTS.WORKSPACE.FINANCE.STATISTICS),
        request.get(API.API_ENDPOINTS.WORKSPACE.FINANCE.RECENT_RECORDS, { limit: 10 }),
        request.get(API.API_ENDPOINTS.WORKSPACE.FINANCE.MONTHLY_TREND, { months: 6 })
      ]);

      if (statisticsRes.success) {
        this.setData({ statistics: statisticsRes.data });
      }

      if (recordsRes.success) {
        this.setData({ recentRecords: recordsRes.data.list || [] });
      }

      if (trendRes.success) {
        this.setData({ monthlyTrend: trendRes.data || [] });
      }

      // 计算业务类型统计
      this.calculateBusinessTypeStats();

    } catch (error) {
      console.error('加载财务概览失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载个人财务摘要（普通员工）
   */
  async loadPersonalSummary() {
    try {
      this.setData({ loading: true });

      const response = await request.get(API.API_ENDPOINTS.WORKSPACE.FINANCE.PERSONAL_SUMMARY);

      if (response.success) {
        this.setData({
          personalSummary: response.data,
          // 过滤出用户可以访问的财务模块
          financeModules: this.data.financeModules.filter(module =>
            ['expense', 'payment', 'activity', 'reserve'].includes(module.id)
          )
        });
      }

    } catch (error) {
      console.error('加载个人财务摘要失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 计算业务类型统计
   */
  calculateBusinessTypeStats() {
    const stats = {};
    const records = this.data.recentRecords;

    records.forEach(record => {
      const type = record.business_type;
      if (!stats[type]) {
        stats[type] = { count: 0, amount: 0 };
      }
      stats[type].count++;
      stats[type].amount += parseFloat(record.amount || 0);
    });

    const businessTypeStats = Object.keys(stats).map(type => ({
      type,
      name: this.data.businessTypeMap[type] || type,
      count: stats[type].count,
      amount: stats[type].amount
    }));

    this.setData({ businessTypeStats });
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({ refreshing: true });
    await this.loadFinanceOverview();
    this.setData({ refreshing: false });
  },

  /**
   * 跳转到详细报表
   */
  onViewReports() {
    wx.navigateTo({
      url: '/pages/workspace/finance/reports/reports',
      fail: (error) => {
        console.error('跳转失败:', error);
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 跳转到申请列表
   */
  onViewApplications(e) {
    const { type } = e.currentTarget.dataset;
    if (type) {
      wx.navigateTo({
        url: `/pages/workspace/${type}/list/list`
      });
    }
  },

  /**
   * 财务模块点击
   */
  onModuleTap(e) {
    const { url } = e.currentTarget.dataset;
    if (url) {
      wx.navigateTo({ url });
    }
  },

  /**
   * 快速申请
   */
  onQuickApply(e) {
    const { url } = e.currentTarget.dataset;
    if (url) {
      wx.navigateTo({ url });
    }
  },

  /**
   * 查看记录详情
   */
  onViewRecord(e) {
    const { id, type } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/workspace/${type}/detail/detail?id=${id}`
    });
  },

  /**
   * 格式化金额
   */
  formatAmount(amount) {
    return parseFloat(amount || 0).toFixed(2);
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData().then(() => {
      wx.stopPullDownRefresh();
    });
  }
})