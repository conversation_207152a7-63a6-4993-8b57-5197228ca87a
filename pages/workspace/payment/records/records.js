/**
 * 付款申请记录页面
 */

const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');
const { 
  checkPageAccess, 
  getCurrentUser, 
  checkOperationPermission,
  getDataFilter,
  ROLES,
  handlePermissionDenied 
} = require('../../../../utils/permission-checker.js');

Page({
  data: {
    loading: true,
    refreshing: false,
    loadingMore: false,
    hasMore: true,
    
    // 权限控制
    userRole: '',
    canViewAll: false,
    canApprove: false,
    canEdit: false,
    
    // 列表数据
    records: [],
    total: 0,
    
    // 分页参数
    page: 1,
    pageSize: 20,
    
    // 筛选条件
    filters: {
      status: '',
      dateRange: '',
      amountRange: ''
    },
    
    // 状态选项
    statusOptions: [
      { label: '全部', value: '' },
      { label: '草稿', value: 'draft' },
      { label: '待审批', value: 'pending' },
      { label: '已通过', value: 'approved' },
      { label: '已拒绝', value: 'rejected' },
      { label: '已撤回', value: 'withdrawn' }
    ],
    
    // 状态映射
    statusMap: {
      'draft': { text: '草稿', color: '#999' },
      'pending': { text: '待审批', color: '#1890ff' },
      'approved': { text: '已通过', color: '#52c41a' },
      'rejected': { text: '已拒绝', color: '#ff4d4f' },
      'withdrawn': { text: '已撤回', color: '#d9d9d9' }
    }
  },

  onLoad(options) {
    if (!this.checkPermissions()) {
      return;
    }
    this.loadRecords();
  },

  checkPermissions() {
    const currentUser = getCurrentUser();
    const hasAccess = checkPageAccess('payment/records');
    
    if (!hasAccess) {
      handlePermissionDenied('您没有权限查看付款申请记录');
      return false;
    }
    
    this.setData({
      userRole: currentUser.role,
      canViewAll: currentUser.role !== ROLES.EMPLOYEE,
      canApprove: [ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN].includes(currentUser.role),
      canEdit: true
    });
    
    return true;
  },

  async loadRecords(isRefresh = false) {
    if (isRefresh) {
      this.setData({ 
        page: 1, 
        records: [], 
        hasMore: true,
        refreshing: true 
      });
    } else {
      this.setData({ loading: true });
    }

    try {
      const currentUser = getCurrentUser();
      const params = {
        page: this.data.page,
        pageSize: this.data.pageSize,
        ...this.data.filters
      };
      
      const dataFilter = getDataFilter('payment', currentUser.role);
      Object.assign(params, dataFilter);

      const response = await request.get(API.API_ENDPOINTS.WORKSPACE.PAYMENT.LIST, params);

      if (response.success) {
        const newRecords = response.data.list || [];
        const records = isRefresh ? newRecords : [...this.data.records, ...newRecords];
        
        this.setData({
          records,
          total: response.data.total || 0,
          hasMore: newRecords.length === this.data.pageSize
        });
      } else {
        wx.showToast({
          title: response.message || '加载失败',
          icon: 'none'
        });
      }

    } catch (error) {
      console.error('加载申请记录失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ 
        loading: false, 
        refreshing: false,
        loadingMore: false 
      });
    }
  },

  onPullDownRefresh() {
    this.loadRecords(true).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (!this.data.hasMore || this.data.loadingMore) {
      return;
    }

    this.setData({ 
      page: this.data.page + 1,
      loadingMore: true 
    });
    
    this.loadRecords();
  },

  onViewDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/workspace/payment/detail/detail?id=${id}`
    });
  },

  onEditRecord(e) {
    const { id, status, userId } = e.currentTarget.dataset;
    const currentUser = getCurrentUser();
    
    const canEdit = checkOperationPermission('edit', 'own', currentUser.role, {
      owner_id: userId,
      status: status
    });
    
    if (!canEdit) {
      wx.showToast({
        title: '无权限编辑此记录',
        icon: 'none'
      });
      return;
    }
    
    if (!['draft', 'rejected'].includes(status)) {
      wx.showToast({
        title: '只能编辑草稿或被退回的申请',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/workspace/payment/apply/apply?id=${id}&mode=edit`
    });
  },

  onApproveRecord(e) {
    const { id, status } = e.currentTarget.dataset;
    
    if (!this.data.canApprove) {
      wx.showToast({
        title: '无权限审批',
        icon: 'none'
      });
      return;
    }
    
    if (status !== 'pending') {
      wx.showToast({
        title: '只能审批待审批的申请',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/workspace/payment/approve/approve?id=${id}`
    });
  },

  onDeleteRecord(e) {
    const { id, status, userId } = e.currentTarget.dataset;
    const currentUser = getCurrentUser();
    
    const canDelete = checkOperationPermission('delete', 'own', currentUser.role, {
      owner_id: userId,
      status: status
    });
    
    if (!canDelete) {
      wx.showToast({
        title: '无权限删除此记录',
        icon: 'none'
      });
      return;
    }
    
    if (status !== 'draft') {
      wx.showToast({
        title: '只能删除草稿状态的申请',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这条申请记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.deleteRecord(id);
        }
      }
    });
  },

  async deleteRecord(id) {
    try {
      wx.showLoading({ title: '删除中...' });
      
      const response = await request.delete(`${API.API_ENDPOINTS.WORKSPACE.PAYMENT.DELETE}/${id}`);
      
      if (response.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        this.loadRecords(true);
      } else {
        wx.showToast({
          title: response.message || '删除失败',
          icon: 'none'
        });
      }
      
    } catch (error) {
      console.error('删除申请失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  onStatusFilter(e) {
    const { value } = e.currentTarget.dataset;
    this.setData({
      'filters.status': value
    });
    this.loadRecords(true);
  },

  onCreateNew() {
    wx.navigateTo({
      url: '/pages/workspace/payment/apply/apply'
    });
  },

  formatAmount(amount) {
    return parseFloat(amount || 0).toFixed(2);
  },

  formatDate(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  }
});
