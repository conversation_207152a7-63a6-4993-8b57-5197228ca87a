// pages/workspace/payment/list/list.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    paymentList: [],
    loading: true,
    refreshing: false,
    hasMore: true,
    page: 1,
    limit: 10,

    // 状态映射
    statusMap: {
      'draft': '草稿',
      'pending': '待审批',
      'processing': '审批中',
      'approved': '已通过',
      'rejected': '已拒绝',
      'cancelled': '已取消'
    },

    // 状态颜色映射
    statusColorMap: {
      'draft': '#d9d9d9',
      'pending': '#faad14',
      'processing': '#1890ff',
      'approved': '#52c41a',
      'rejected': '#ff4d4f',
      'cancelled': '#d9d9d9'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadPaymentList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.refreshData();
  },

  /**
   * 加载付款申请列表
   */
  async loadPaymentList() {
    if (!this.data.hasMore && this.data.page > 1) return;

    try {
      this.setData({ loading: true });

      const response = await request.get(API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.LIST, {
        type: 'payment',
        page: this.data.page,
        limit: this.data.limit
      });

      if (response.success) {
        const newList = this.data.page === 1 ? response.data.list : [...this.data.paymentList, ...response.data.list];

        this.setData({
          paymentList: newList,
          hasMore: response.data.pagination.page * response.data.pagination.limit < response.data.pagination.total,
          loading: false
        });
      } else {
        throw new Error(response.message || '加载失败');
      }
    } catch (error) {
      console.error('加载付款申请列表失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({
      page: 1,
      hasMore: true,
      refreshing: true
    });

    await this.loadPaymentList();
    this.setData({ refreshing: false });
  },

  /**
   * 查看详情
   */
  onViewDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/workspace/payment/detail/detail?id=${id}`
    });
  },

  /**
   * 新建申请
   */
  onCreatePayment() {
    wx.navigateTo({
      url: '/pages/workspace/payment/apply/apply'
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.setData({
        page: this.data.page + 1
      });
      this.loadPaymentList();
    }
  }
})