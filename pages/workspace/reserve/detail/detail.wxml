<!--pages/workspace/reserve/detail/detail.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 详情内容 -->
  <view wx:else class="detail-content">
    <!-- 基本信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <view class="title-section">
          <text class="reserve-title">{{reserveDetail.title || '备用金'}}</text>
          <view class="status-tag" style="background-color: {{statusColorMap[reserveDetail.status]}}">
            {{statusMap[reserveDetail.status]}}
          </view>
        </view>
        <text class="reserve-amount">¥{{reserveDetail.amount}}</text>
      </view>
      
      <view class="card-content">
        <view class="info-row">
          <text class="label">申请人：</text>
          <text class="value">{{reserveDetail.applicant_name}}</text>
        </view>
        <view class="info-row">
          <text class="label">申请时间：</text>
          <text class="value">{{reserveDetail.created_at}}</text>
        </view>
        <view class="info-row">
          <text class="label">类型：</text>
          <text class="value">{{reserveDetail.category}}</text>
        </view>
        <view class="info-row" wx:if="{{reserveDetail.description}}">
          <text class="label">申请说明：</text>
          <text class="value description">{{reserveDetail.description}}</text>
        </view>
      </view>
    </view>

    <!-- 明细 -->
    <view class="info-card" wx:if="{{reserveDetail.items && reserveDetail.items.length > 0}}">
      <view class="card-title">明细信息</view>
      <view class="reserve-items">
        <view wx:for="{{reserveDetail.items}}" wx:key="id" class="reserve-item">
          <view class="item-info">
            <text class="item-name">{{item.name}}</text>
            <text class="item-desc" wx:if="{{item.description}}">{{item.description}}</text>
          </view>
          <text class="item-amount">¥{{item.amount}}</text>
        </view>
      </view>
    </view>

    <!-- 附件 -->
    <view class="info-card" wx:if="{{attachments.length > 0}}">
      <view class="card-title">相关附件</view>
      <view class="attachments">
        <view 
          wx:for="{{attachments}}" 
          wx:key="id" 
          class="attachment-item"
          bindtap="onPreviewAttachment"
          data-url="{{item.url}}"
          data-type="{{item.type}}"
        >
          <image 
            wx:if="{{item.type === 'image'}}"
            class="attachment-thumb"
            src="{{item.url}}"
            mode="aspectFill"
          />
          <view wx:else class="attachment-file">
            <text class="file-icon">📄</text>
            <text class="file-name">{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 审批流程 -->
    <view class="info-card" wx:if="{{approvalFlow.length > 0}}">
      <view class="card-title">审批流程</view>
      <view class="approval-flow">
        <view wx:for="{{approvalFlow}}" wx:key="id" class="approval-step">
          <view class="step-icon {{item.status}}">
            <text wx:if="{{item.status === 'approved'}}">✓</text>
            <text wx:elif="{{item.status === 'rejected'}}">✗</text>
            <text wx:else>○</text>
          </view>
          <view class="step-content">
            <view class="step-header">
              <text class="approver-name">{{item.approver_name}}</text>
              <text class="step-time" wx:if="{{item.processed_at}}">{{item.processed_at}}</text>
            </view>
            <text class="step-title">{{item.step_name}}</text>
            <text class="step-comment" wx:if="{{item.comment}}">{{item.comment}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button 
        wx:if="{{reserveDetail.status === 'draft'}}"
        class="action-btn edit-btn"
        bindtap="onEditReserve"
      >
        编辑申请
      </button>
      <button 
        wx:if="{{reserveDetail.status === 'draft'}}"
        class="action-btn submit-btn"
        bindtap="onSubmitReserve"
      >
        提交申请
      </button>
      <button 
        wx:if="{{reserveDetail.status === 'pending' || reserveDetail.status === 'processing'}}"
        class="action-btn cancel-btn"
        bindtap="onCancelReserve"
      >
        撤销申请
      </button>
    </view>
  </view>
</view>