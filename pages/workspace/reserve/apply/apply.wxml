<!--pages/workspace/expense/apply/apply.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 表单内容 -->
  <view wx:else class="form-content">
    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>

      <!-- 申请标题 -->
      <view class="form-item">
        <view class="item-label">
          <text class="label-text">申请标题</text>
          <text class="required">*</text>
        </view>
        <input
          class="form-input {{errors.title ? 'error' : ''}}"
          placeholder="请输入申请标题"
          value="{{formData.title}}"
          data-field="title"
          bindinput="onInputChange"
        />
        <text wx:if="{{errors.title}}" class="error-text">{{errors.title}}</text>
      </view>

      <!-- 备用金类别 -->
      <view class="form-item">
        <view class="item-label">
          <text class="label-text">备用金类别</text>
          <text class="required">*</text>
        </view>
        <picker
          class="form-picker {{errors.category ? 'error' : ''}}"
          range="{{categoryOptions}}"
          range-key="label"
          value="{{selectedCategoryIndex}}"
          bindchange="onCategoryChange"
        >
          <view class="picker-content">
            <text wx:if="{{selectedCategoryLabel}}" class="picker-text selected">{{selectedCategoryLabel}}</text>
            <text wx:else class="picker-text placeholder">请选择备用金类别</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
        <text wx:if="{{errors.category}}" class="error-text">{{errors.category}}</text>
      </view>

      <!-- 申请金额 -->
      <view class="form-item">
        <view class="item-label">
          <text class="label-text">申请金额</text>
          <text class="required">*</text>
        </view>
        <input
          class="form-input {{errors.amount ? 'error' : ''}}"
          placeholder="请输入金额"
          type="digit"
          value="{{formData.amount}}"
          data-field="amount"
          bindinput="onInputChange"
        />
        <text wx:if="{{errors.amount}}" class="error-text">{{errors.amount}}</text>
      </view>

      <!-- 预期日期 -->
      <view class="form-item">
        <view class="item-label">
          <text class="label-text">预期日期</text>
        </view>
        <picker
          class="form-picker"
          mode="date"
          value="{{formData.expectedDate}}"
          bindchange="onDateChange"
        >
          <view class="picker-content">
            <text wx:if="{{formattedDate}}" class="picker-text selected">{{formattedDate}}</text>
            <text wx:else class="picker-text placeholder">请选择日期</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <!-- 备用金申请说明 -->
      <view class="form-item">
        <view class="item-label">
          <text class="label-text">备用金申请说明</text>
          <text class="required">*</text>
        </view>
        <textarea
          class="form-textarea {{errors.description ? 'error' : ''}}"
          placeholder="请详细说明备用金用途和必要性"
          value="{{formData.description}}"
          data-field="description"
          bindinput="onInputChange"
          maxlength="500"
        />
        <text wx:if="{{errors.description}}" class="error-text">{{errors.description}}</text>
      </view>
    </view>

    <!-- 附件上传 -->
    <view class="form-section">
      <view class="section-title">相关附件</view>

      <view class="attachment-container">
        <!-- 已上传的附件 -->
        <view wx:for="{{formData.attachments}}" wx:key="index" class="attachment-item">
          <image
            class="attachment-image"
            src="{{item.url}}"
            mode="aspectFill"
            bindtap="onPreviewAttachment"
            data-index="{{index}}"
          />
          <view
            class="attachment-delete"
            bindtap="onDeleteAttachment"
            data-index="{{index}}"
          >
            ×
          </view>
        </view>

        <!-- 添加附件按钮 -->
        <view
          wx:if="{{formData.attachments.length < 9}}"
          class="attachment-add"
          bindtap="onChooseAttachment"
        >
          <text class="add-icon">+</text>
          <text class="add-text">添加附件</text>
        </view>
      </view>


    </view>

    <!-- 提交按钮 -->
    <view class="submit-buttons">
      <button
        class="submit-btn draft-btn"
        bindtap="onSaveDraft"
        disabled="{{submitting}}"
      >
        保存草稿
      </button>
      <button
        class="submit-btn primary-btn"
        bindtap="onSubmit"
        disabled="{{submitting}}"
      >
        {{submitting ? '提交中...' : '提交申请'}}
      </button>
    </view>
  </view>
</view>