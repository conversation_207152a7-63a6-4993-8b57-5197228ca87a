<!--pages/workspace/expense/approve/approve.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 审批内容 -->
  <view wx:else class="approve-content">
    <!-- 申请信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <view class="title-section">
          <text class="reserve-title">{{expenseDetail.title || '备用金'}}</text>
          <view class="status-tag" style="background-color: {{statusColorMap[expenseDetail.status]}}">
            {{statusMap[expenseDetail.status]}}
          </view>
        </view>
        <text class="reserve-amount">¥{{expenseDetail.amount}}</text>
      </view>

      <view class="card-content">
        <view class="info-row">
          <text class="label">申请人：</text>
          <text class="value">{{expenseDetail.applicant_name}}</text>
        </view>
        <view class="info-row">
          <text class="label">申请时间：</text>
          <text class="value">{{expenseDetail.created_at}}</text>
        </view>
        <view class="info-row">
          <text class="label">备用金类型：</text>
          <text class="value">{{expenseDetail.category}}</text>
        </view>
        <view class="info-row" wx:if="{{expenseDetail.description}}">
          <text class="label">申请说明：</text>
          <text class="value description">{{expenseDetail.description}}</text>
        </view>
      </view>
    </view>

    <!-- 备用金明细 -->
    <view class="info-card" wx:if="{{expenseDetail.items && expenseDetail.items.length > 0}}">
      <view class="card-title">备用金明细</view>
      <view class="reserve-items">
        <view wx:for="{{expenseDetail.items}}" wx:key="id" class="reserve-item">
          <view class="item-info">
            <text class="item-name">{{item.name}}</text>
            <text class="item-desc" wx:if="{{item.description}}">{{item.description}}</text>
          </view>
          <text class="item-amount">¥{{item.amount}}</text>
        </view>
      </view>
    </view>

    <!-- 附件 -->
    <view class="info-card" wx:if="{{attachments.length > 0}}">
      <view class="card-title">相关附件</view>
      <view class="attachments">
        <view
          wx:for="{{attachments}}"
          wx:key="id"
          class="attachment-item"
          bindtap="onPreviewAttachment"
          data-url="{{item.url}}"
          data-type="{{item.type}}"
        >
          <image
            wx:if="{{item.type === 'image'}}"
            class="attachment-thumb"
            src="{{item.url}}"
            mode="aspectFill"
          />
          <view wx:else class="attachment-file">
            <text class="file-icon">📄</text>
            <text class="file-name">{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 审批流程 -->
    <view class="info-card" wx:if="{{approvalFlow.length > 0}}">
      <view class="card-title">审批流程</view>
      <view class="approval-flow">
        <view wx:for="{{approvalFlow}}" wx:key="id" class="approval-step">
          <view class="step-icon {{item.status}}">
            <text wx:if="{{item.status === 'approved'}}">✓</text>
            <text wx:elif="{{item.status === 'rejected'}}">✗</text>
            <text wx:else>○</text>
          </view>
          <view class="step-content">
            <view class="step-header">
              <text class="approver-name">{{item.approver_name}}</text>
              <text class="step-time" wx:if="{{item.processed_at}}">{{item.processed_at}}</text>
            </view>
            <text class="step-title">{{item.step_name}}</text>
            <text class="step-comment" wx:if="{{item.comment}}">{{item.comment}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 审批操作 -->
    <view class="approval-section">
      <view class="section-title">审批操作</view>

      <!-- 审批意见 -->
      <view class="form-item">
        <view class="item-label">
          <text class="label-text">审批意见</text>
        </view>
        <textarea
          class="approval-textarea"
          placeholder="请填写审批意见（拒绝时必填）"
          value="{{approvalForm.comment}}"
          bindinput="onCommentInput"
          maxlength="500"
        />
      </view>

      <!-- 审批按钮 -->
      <view class="approval-buttons">
        <button
          class="approval-btn reject-btn"
          bindtap="onReject"
          disabled="{{submitting}}"
        >
          拒绝
        </button>
        <button
          class="approval-btn approve-btn"
          bindtap="onApprove"
          disabled="{{submitting}}"
        >
          {{submitting ? '处理中...' : '通过'}}
        </button>
      </view>
    </view>
  </view>
</view>