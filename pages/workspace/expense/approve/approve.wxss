/* pages/workspace/expense/approve/approve.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #ff4d4f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

.approve-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 15rpx;
  flex: 1;
}

.expense-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.status-tag {
  color: white;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: normal;
}

.expense-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff4d4f;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-row {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
  flex-shrink: 0;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.description {
  line-height: 1.6;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.expense-items {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.expense-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20rpx;
  background: #fafafa;
  border-radius: 12rpx;
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

.item-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.item-desc {
  font-size: 24rpx;
  color: #666;
}

.item-amount {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff4d4f;
}

.attachments {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.attachment-item {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.attachment-thumb {
  width: 100%;
  height: 100%;
}

.attachment-file {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  padding: 20rpx;
}

.file-icon {
  font-size: 48rpx;
}

.file-name {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  word-break: break-all;
}

.approval-flow {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.approval-step {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.step-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.step-icon.approved {
  background: #52c41a;
  color: white;
}

.step-icon.rejected {
  background: #ff4d4f;
  color: white;
}

.step-icon.pending {
  background: #faad14;
  color: white;
}

.step-icon.waiting {
  background: #d9d9d9;
  color: #999;
}

.step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.approver-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.step-time {
  font-size: 24rpx;
  color: #999;
}

.step-title {
  font-size: 26rpx;
  color: #666;
}

.step-comment {
  font-size: 26rpx;
  color: #333;
  background: #f6f6f6;
  padding: 15rpx;
  border-radius: 8rpx;
  line-height: 1.5;
}

.approval-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-item {
  margin-bottom: 30rpx;
}

.item-label {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.approval-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background: white;
  box-sizing: border-box;
  line-height: 1.6;
}

.approval-buttons {
  display: flex;
  gap: 20rpx;
}

.approval-btn {
  flex: 1;
  padding: 24rpx 0 !important;
  font-size: 28rpx !important;
  border-radius: 25rpx !important;
  border: none !important;
  font-weight: 500;
}

.reject-btn {
  background: #ff4d4f !important;
  color: white !important;
}

.approve-btn {
  background: #52c41a !important;
  color: white !important;
}

.approval-btn[disabled] {
  opacity: 0.6;
}