// pages/workspace/expense/detail/detail.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    expenseId: '',
    expenseDetail: null,
    loading: true,

    // 状态映射
    statusMap: {
      'draft': '草稿',
      'pending': '待审批',
      'processing': '审批中',
      'approved': '已通过',
      'rejected': '已拒绝',
      'cancelled': '已取消'
    },

    // 状态颜色映射
    statusColorMap: {
      'draft': '#d9d9d9',
      'pending': '#faad14',
      'processing': '#1890ff',
      'approved': '#52c41a',
      'rejected': '#ff4d4f',
      'cancelled': '#d9d9d9'
    },

    // 审批流程
    approvalFlow: [],

    // 附件列表
    attachments: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.id) {
      this.setData({ expenseId: options.id });
      this.loadExpenseDetail();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 加载费用报销详情
   */
  async loadExpenseDetail() {
    try {
      this.setData({ loading: true });

      const response = await request.get(API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.DETAIL(this.data.expenseId));

      if (response.success) {
        this.setData({
          expenseDetail: response.data,
          approvalFlow: response.data.approval_flow || [],
          attachments: response.data.attachments || [],
          loading: false
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: response.data.title || '费用报销详情'
        });
      } else {
        throw new Error(response.message || '加载失败');
      }
    } catch (error) {
      console.error('加载费用报销详情失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 编辑申请
   */
  onEditExpense() {
    wx.navigateTo({
      url: `/pages/workspace/expense/apply/apply?id=${this.data.expenseId}&mode=edit`
    });
  },

  /**
   * 提交申请
   */
  async onSubmitExpense() {
    try {
      wx.showLoading({ title: '提交中...' });

      const response = await request.post(API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.SUBMIT(this.data.expenseId));

      if (response.success) {
        wx.showToast({
          title: '提交成功',
          icon: 'success'
        });

        // 刷新详情
        this.loadExpenseDetail();
      } else {
        throw new Error(response.message || '提交失败');
      }
    } catch (error) {
      console.error('提交费用报销失败:', error);
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 撤销申请
   */
  async onCancelExpense() {
    const result = await wx.showModal({
      title: '确认撤销',
      content: '确定要撤销这个费用报销申请吗？',
      confirmText: '撤销',
      confirmColor: '#ff4d4f'
    });

    if (!result.confirm) return;

    try {
      wx.showLoading({ title: '撤销中...' });

      const response = await request.post(API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.CANCEL(this.data.expenseId));

      if (response.success) {
        wx.showToast({
          title: '撤销成功',
          icon: 'success'
        });

        // 刷新详情
        this.loadExpenseDetail();
      } else {
        throw new Error(response.message || '撤销失败');
      }
    } catch (error) {
      console.error('撤销费用报销失败:', error);
      wx.showToast({
        title: error.message || '撤销失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 预览附件
   */
  onPreviewAttachment(e) {
    const { url, type } = e.currentTarget.dataset;

    if (type === 'image') {
      wx.previewImage({
        current: url,
        urls: this.data.attachments.filter(item => item.type === 'image').map(item => item.url)
      });
    } else {
      wx.downloadFile({
        url: url,
        success: (res) => {
          wx.openDocument({
            filePath: res.tempFilePath
          });
        }
      });
    }
  },

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    if (!timeStr) return '';
    const time = new Date(timeStr);
    return time.toLocaleString();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadExpenseDetail().then(() => {
      wx.stopPullDownRefresh();
    });
  }
})