<!--pages/workspace/expense/apply/apply.wxml-->
<permission-check permission="{{requiredPermissions.create}}" mode="show">
  <view class="container">
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 表单内容 -->
    <view wx:else class="form-content">
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>

        <!-- 费用类别 -->
        <view class="form-item">
          <view class="item-label">
            <text class="label-text">费用类别</text>
            <text class="required">*</text>
          </view>

          <picker
            class="form-picker {{errors.category ? 'error' : ''}}"
            range="{{categoryOptions}}"
            range-key="label"
            value="{{selectedCategoryIndex}}"
            bindchange="onCategoryChange"
          >
            <view class="picker-content">
              <text wx:if="{{selectedCategoryLabel}}" class="picker-text selected">{{selectedCategoryLabel}}</text>
              <text wx:else class="picker-text placeholder">请选择费用类别</text>
              <text class="picker-arrow">▼</text>
            </view>

          </picker>

          <text wx:if="{{errors.category}}" class="error-text">{{errors.category}}</text>
        </view>

        <!-- 申请金额 -->
        <view class="form-item">
          <view class="item-label">
            <text class="label-text">申请金额</text>
            <text class="required">*</text>
          </view>
          <view class="amount-input-container">
            <text class="currency-symbol">¥</text>
            <input
              class="form-input amount-input {{errors.amount ? 'error' : ''}}"
              placeholder="0.00"
              type="digit"
              value="{{formData.amount}}"
              data-field="amount"
              bindinput="onInputChange"
            />
          </view>
          <text wx:if="{{errors.amount}}" class="error-text">{{errors.amount}}</text>
        </view>

        <!-- 预期日期 -->
        <view class="form-item">
          <view class="item-label">
            <text class="label-text">预期日期</text>
          </view>

          <picker
            class="form-picker"
            mode="date"
            value="{{formData.expectedDate}}"
            bindchange="onDateChange"
          >
            <view class="picker-content">
              <text wx:if="{{formattedDate}}" class="picker-text selected">{{formattedDate}}</text>
              <text wx:else class="picker-text placeholder">请选择日期</text>
              <text class="picker-arrow">▼</text>
            </view>

          </picker>

        </view>

        <!-- 费用说明 -->
        <view class="form-item">
          <view class="item-label">
            <text class="label-text">费用说明</text>
            <text class="required">*</text>
          </view>
          <textarea
            class="form-textarea {{errors.description ? 'error' : ''}}"
            placeholder="请详细说明费用用途和必要性"
            value="{{formData.description}}"
            data-field="description"
            bindinput="onInputChange"
            maxlength="500"
            auto-height
            show-confirm-bar="{{false}}"
          />
          <view class="textarea-counter">
            <text class="counter-text">{{formData.description.length}}/500</text>
          </view>
          <text wx:if="{{errors.description}}" class="error-text">{{errors.description}}</text>
        </view>
      </view>

      <!-- 附件上传 -->
      <view class="form-section">
        <view class="section-title">
          <text>相关附件</text>
          <text class="section-subtitle">(可选)</text>
        </view>

        <view class="attachment-container">
          <!-- 已上传的附件 -->
          <view wx:for="{{formData.attachments}}" wx:key="index" class="attachment-item">
            <image
              class="attachment-image"
              src="{{item.url}}"
              mode="aspectFill"
              bindtap="onPreviewAttachment"
              data-index="{{index}}"
            />
            <view
              class="attachment-delete"
              bindtap="onDeleteAttachment"
              data-index="{{index}}"
            >
              ×
            </view>
            <view class="attachment-size" wx:if="{{item.size}}">
              {{formatFileSize(item.size)}}
            </view>
          </view>

          <!-- 添加附件按钮 -->
          <view
            wx:if="{{formData.attachments.length < 9}}"
            class="attachment-add"
            bindtap="onChooseAttachment"
          >
            <text class="add-icon">📎</text>
            <text class="add-text">添加附件</text>
          </view>
        </view>


      </view>

      <!-- 提交按钮 -->
      <permission-check permission="{{requiredPermissions.create}}" mode="show">
        <view class="submit-buttons">
          <button
            class="submit-btn draft-btn"
            bindtap="onSaveDraft"
            disabled="{{submitting}}"
            hover-class="btn-hover"
          >
            💾 保存草稿
          </button>
          <button
            class="submit-btn primary-btn"
            bindtap="onSubmit"
            disabled="{{submitting}}"
            hover-class="btn-hover"
          >
            {{submitting ? '提交中...' : '📤 提交申请'}}
          </button>
        </view>
      </permission-check>
    </view>
  </view>
</permission-check>