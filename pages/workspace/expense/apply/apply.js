// pages/workspace/expense/apply/apply.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');
const { getCurrentUserPermissions, PERMISSIONS, FinancePermissionChecker } = require('../../../../utils/role-permission.js');

Page({
  data: {
    // 表单数据
    formData: {
      description: '',
      amount: '',
      expectedDate: '',
      category: '',
      attachments: []
    },

    // 费用类别选项（增强版）
    categoryOptions: [
      { value: 'travel', label: '🚂 差旅费', description: '出差交通、住宿、餐饮等费用' },
      { value: 'office', label: '💼 办公用品', description: '文具、设备、耗材等办公用品' },
      { value: 'communication', label: '📞 通讯费', description: '电话、网络、邮寄等通讯费用' },
      { value: 'entertainment', label: '🍽️ 招待费', description: '客户招待、商务娱乐等费用' },
      { value: 'training', label: '🎓 培训费', description: '员工培训、学习进修等费用' },
      { value: 'maintenance', label: '🔧 维修费', description: '设备维修、保养等费用' },
      { value: 'material', label: '📦 材料费', description: '原材料、配件等采购费用' },
      { value: 'transport', label: '🚚 运输费', description: '货物运输、物流等费用' },
      { value: 'medical', label: '🏥 医疗费', description: '兽医费、药品费等医疗支出' },
      { value: 'insurance', label: '🛡️ 保险费', description: '财产保险、责任保险等' },
      { value: 'other', label: '📝 其他费用', description: '其他未分类的费用支出' }
    ],

    // 选中的类别索引
    selectedCategoryIndex: -1,
    // 选中类别的显示文本
    selectedCategoryLabel: '',
    // 是否已选择类别
    hasSelectedCategory: false,

    // 格式化的日期显示
    formattedDate: '',
    // 是否已选择日期
    hasExpectedDate: false,

    // 表单验证
    errors: {},

    // 页面状态
    loading: false,
    submitting: false,
    
    // 日期范围限制
    minDate: '',
    maxDate: '',
    
    // 权限控制
    userPermissions: null,
    requiredPermissions: {
      create: PERMISSIONS.FINANCE_CREATE,
      edit: PERMISSIONS.FINANCE_EDIT
    }
  },

  async onLoad(options) {
    await this.initPage();
    
    // 如果是编辑模式，加载申请数据
    if (options.id) {
      this.loadApplicationData(options.id);
    }
  },
  
  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 获取用户权限
      const userPermissions = await getCurrentUserPermissions();
      
      // 设置日期范围（过去30天到未来90天）
      const today = new Date();
      const minDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000); // 30天前
      const maxDate = new Date(today.getTime() + 90 * 24 * 60 * 60 * 1000); // 90天后

      this.setData({
        userPermissions,
        minDate: minDate.toISOString().split('T')[0],
        maxDate: maxDate.toISOString().split('T')[0]
      });
      
      // 检查创建权限
      if (!FinancePermissionChecker.canCreateApplication(userPermissions.role)) {
        wx.showModal({
          title: '权限不足',
          content: '您没有创建财务申请的权限',
          showCancel: false,
          complete: () => {
            wx.navigateBack();
          }
        });
        return;
      }
      
    } catch (error) {
      console.error('初始化页面失败:', error);
      wx.showToast({
        title: '初始化失败',
        icon: 'error'
      });
    }
  },

  /**
   * 加载申请数据（编辑模式）
   */
  async loadApplicationData(id) {
    try {
      this.setData({ loading: true });
      
      const response = await request.get(API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.DETAIL(id));
      
      if (response && response.success) {
        const application = response.data;
        
        // 检查编辑权限
        const canEdit = FinancePermissionChecker.canEditApplication(
          this.data.userPermissions.role, 
          application.userId === this.data.userPermissions.userId
        );
        
        if (!canEdit) {
          wx.showModal({
            title: '无编辑权限',
            content: '您无权编辑此申请',
            showCancel: false,
            complete: () => wx.navigateBack()
          });
          return;
        }
        
        // 查找类别索引
        const categoryIndex = this.data.categoryOptions.findIndex(
          item => item.value === (application.metadata?.category || '')
        );
        
        const expectedDate = application.expectedDate || '';
        this.setData({
          formData: {
            description: application.description || '',
            amount: application.amount ? application.amount.toString() : '',
            expectedDate,
            category: application.metadata?.category || '',
            attachments: application.attachments || []
          },
          selectedCategoryIndex: categoryIndex,
          selectedCategoryLabel: categoryIndex >= 0 ? this.data.categoryOptions[categoryIndex].label : '',
          formattedDate: expectedDate ? this.formatDate(expectedDate) : ''
        });
        
        // 更新标题
        wx.setNavigationBarTitle({
          title: '编辑费用申请'
        });
      }
    } catch (error) {
      console.error('加载申请数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 输入框变化处理
   */
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    // 特殊处理金额输入
    if (field === 'amount') {
      // 只允许数字和小数点
      const cleanValue = value.replace(/[^\d.]/g, '');
      // 确保只有一个小数点
      const parts = cleanValue.split('.');
      const formattedValue = parts.length > 2 
        ? parts[0] + '.' + parts.slice(1).join('') 
        : cleanValue;
      
      this.setData({
        [`formData.${field}`]: formattedValue,
        [`errors.${field}`]: ''
      });
    } else {
      this.setData({
        [`formData.${field}`]: value,
        [`errors.${field}`]: ''
      });
    }
  },

  /**
   * 类别选择
   */
  onCategoryChange(e) {
    console.log('onCategoryChange 被调用:', e.detail);

    const index = parseInt(e.detail.value);
    const category = this.data.categoryOptions[index];

    console.log('类别选择:', { index, category, categoryOptions: this.data.categoryOptions });

    if (!category) {
      console.error('未找到对应的类别:', index);
      return;
    }

    console.log('设置前的数据:', {
      selectedCategoryIndex: this.data.selectedCategoryIndex,
      selectedCategoryLabel: this.data.selectedCategoryLabel,
      hasSelectedCategory: this.data.hasSelectedCategory,
      formDataCategory: this.data.formData.category
    });

    this.setData({
      selectedCategoryIndex: index,
      selectedCategoryLabel: category.label,
      hasSelectedCategory: true,
      'formData.category': category.value,
      'errors.category': ''
    }, () => {
      console.log('设置后的数据:', {
        selectedCategoryIndex: this.data.selectedCategoryIndex,
        selectedCategoryLabel: this.data.selectedCategoryLabel,
        hasSelectedCategory: this.data.hasSelectedCategory,
        formDataCategory: this.data.formData.category
      });

      // 强制刷新页面
      this.$forceUpdate && this.$forceUpdate();

      // 尝试重新设置一次
      setTimeout(() => {
        this.setData({
          selectedCategoryLabel: category.label
        });
      }, 100);
    });
  },

  /**
   * 日期选择
   */
  onDateChange(e) {
    console.log('onDateChange 被调用:', e.detail);

    const selectedDate = e.detail.value;
    const formatted = this.formatDate(selectedDate);

    console.log('日期选择:', { selectedDate, formatted });
    console.log('设置前的日期数据:', {
      expectedDate: this.data.formData.expectedDate,
      formattedDate: this.data.formattedDate,
      hasExpectedDate: this.data.hasExpectedDate
    });

    this.setData({
      'formData.expectedDate': selectedDate,
      'formattedDate': formatted,
      hasExpectedDate: true,
      'errors.expectedDate': ''
    }, () => {
      console.log('设置后的日期数据:', {
        expectedDate: this.data.formData.expectedDate,
        formattedDate: this.data.formattedDate,
        hasExpectedDate: this.data.hasExpectedDate
      });
    });
  },





  /**
   * 选择附件
   */
  onChooseAttachment() {
    const that = this;
    
    wx.chooseMedia({
      count: 9 - this.data.formData.attachments.length,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      sizeType: ['compressed'], // 使用压缩图
      success: (res) => {
        wx.showLoading({ title: '上传中...' });
        
        const tempFiles = res.tempFiles;
        const attachments = [...this.data.formData.attachments];
        const uploadPromises = [];
        
        tempFiles.forEach(file => {
          // 检查文件大小（10MB限制）
          if (file.size > 10 * 1024 * 1024) {
            wx.showToast({
              title: '文件太大，请选择小于10MB的图片',
              icon: 'none',
              duration: 3000
            });
            return;
          }
          
          // 添加到列表（先显示本地图片）
          attachments.push({
            type: 'image',
            url: file.tempFilePath,
            size: file.size,
            uploading: true
          });
          
          // TODO: 实现文件上传到服务器的逻辑
          // 这里暂时使用本地图片
          uploadPromises.push(Promise.resolve(file.tempFilePath));
        });
        
        that.setData({
          'formData.attachments': attachments
        });
        
        // 模拟上传完成
        Promise.all(uploadPromises).then(() => {
          const updatedAttachments = attachments.map(item => ({
            ...item,
            uploading: false
          }));
          that.setData({
            'formData.attachments': updatedAttachments
          });
          wx.hideLoading();
        }).catch(error => {
          console.error('文件上传失败:', error);
          wx.hideLoading();
          wx.showToast({
            title: '上传失败',
            icon: 'error'
          });
        });
      },
      fail: (error) => {
        console.error('选择文件失败:', error);
        if (error.errMsg !== 'chooseMedia:fail cancel') {
          wx.showToast({
            title: '选择文件失败',
            icon: 'error'
          });
        }
      }
    });
  },

  /**
   * 删除附件
   */
  onDeleteAttachment(e) {
    const { index } = e.currentTarget.dataset;
    const attachments = [...this.data.formData.attachments];
    
    wx.showModal({
      title: '确认删除',
      content: '确认要删除该附件吗？',
      success: (res) => {
        if (res.confirm) {
          attachments.splice(index, 1);
          this.setData({
            'formData.attachments': attachments
          });
        }
      }
    });
  },

  /**
   * 预览附件
   */
  onPreviewAttachment(e) {
    const { index } = e.currentTarget.dataset;
    const attachments = this.data.formData.attachments;
    const urls = attachments
      .filter(item => item.type === 'image' && !item.uploading)
      .map(item => item.url);
    
    if (urls.length > 0) {
      wx.previewImage({
        current: attachments[index].url,
        urls: urls
      });
    }
  },

  /**
   * 表单验证（增强版）
   */
  validateForm() {
    const { formData } = this.data;
    const errors = {};

    // 费用说明验证
    if (!formData.description.trim()) {
      errors.description = '请输入费用说明';
    } else if (formData.description.trim().length < 10) {
      errors.description = '费用说明至少需要10个字符';
    }
    
    // 金额验证
    const amount = parseFloat(formData.amount);
    if (!formData.amount || isNaN(amount) || amount <= 0) {
      errors.amount = '请输入有效的金额';
    } else if (amount > 999999.99) {
      errors.amount = '单次申请金额不能超过99万元';
    } else if (!/^\d+(\.\d{1,2})?$/.test(formData.amount)) {
      errors.amount = '金额格式不正确，最多保瘀2位小数';
    }
    
    // 类别验证
    if (!formData.category) {
      errors.category = '请选择费用类别';
    }
    
    this.setData({ errors });
    return Object.keys(errors).length === 0;
  },

  /**
   * 保存草稿
   */
  async onSaveDraft() {
    // 草稿只验证基本字段
    const { formData } = this.data;
    if (!formData.description.trim() && !formData.amount) {
      wx.showToast({
        title: '请至少填写一个字段',
        icon: 'none'
      });
      return;
    }
    
    await this.submitApplication('draft');
  },

  /**
   * 提交申请
   */
  async onSubmit() {
    if (!this.validateForm()) {
      // 获取第一个错误字段并显示
      const firstError = Object.values(this.data.errors)[0];
      wx.showToast({
        title: firstError,
        icon: 'none',
        duration: 3000
      });
      return;
    }
    
    await this.submitApplication('pending');
  },

  /**
   * 提交申请数据（增强版）
   */
  async submitApplication(status) {
    try {
      this.setData({ submitting: true });
      
      const { formData, userPermissions } = this.data;
      
      // 构建提交数据
      const submitData = {
        type: 'expense',
        description: formData.description.trim(),
        amount: parseFloat(formData.amount) || 0,
        expectedDate: formData.expectedDate,
        attachments: formData.attachments.filter(item => !item.uploading),
        status,
        metadata: {
          category: formData.category,
          categoryLabel: this.getCategoryLabel(formData.category),
          submitTime: new Date().toISOString(),
          submitterRole: userPermissions.role,
          deviceInfo: {
            platform: wx.getSystemInfoSync().platform,
            version: wx.getSystemInfoSync().version
          }
        }
      };
      
      const response = await request.post(API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.CREATE, submitData);
      
      if (response && response.success) {
        wx.showToast({
          title: status === 'draft' ? '💾 草稿已保存' : '📤 申请已提交',
          icon: 'success',
          duration: 2000
        });
        
        // 可选：发送统计事件
        this.trackSubmissionEvent(status, submitData);
        
        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack({
            delta: 1
          });
        }, status === 'draft' ? 1000 : 1500);
      } else {
        throw new Error(response?.message || '提交失败');
      }
    } catch (error) {
      console.error('提交申请失败:', error);
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'error',
        duration: 3000
      });
    } finally {
      this.setData({ submitting: false });
    }
  },
  
  /**
   * 获取类别显示文本
   */
  getCategoryLabel(value) {
    const option = this.data.categoryOptions.find(item => item.value === value);
    return option ? option.label : '请选择';
  },
  
  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (!bytes) return '0B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + sizes[i];
  },
  
  /**
   * 格式化日期显示
   */
  formatDate(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    // 始终显示具体日期，格式：YYYY年MM月DD日
    return `${year}年${month.toString().padStart(2, '0')}月${day.toString().padStart(2, '0')}日`;
  },
  
  /**
   * 跟踪提交事件（可选）
   */
  trackSubmissionEvent(status, data) {
    try {
      // TODO: 实现统计上报逻辑
      console.log('Expense application submitted:', {
        status,
        type: data.type,
        amount: data.amount,
        category: data.metadata.category,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('统计事件发送失败:', error);
    }
  }
});
