/* 费用报销申请记录页面样式 */

.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 头部区域 */
.header {
  background: white;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.subtitle {
  font-size: 26rpx;
  color: #666;
}

.create-btn {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 15rpx 25rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
}

.create-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}

/* 筛选区域 */
.filter-section {
  background: white;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-scroll {
  white-space: nowrap;
  padding: 0 30rpx;
}

.filter-item {
  display: inline-block;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  background: #f5f5f5;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.filter-item.active {
  background: #1890ff;
  color: white;
}

.filter-item:last-child {
  margin-right: 30rpx;
}



/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

/* 记录列表 */
.records-list {
  padding: 20rpx 30rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-action {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 记录卡片 */
.record-card {
  background: white;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.record-card:active {
  transform: scale(0.98);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.header-left {
  flex: 1;
  margin-right: 20rpx;
}

.record-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.status-badge {
  display: inline-block;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.status-draft {
  background: rgba(153, 153, 153, 0.1);
  color: #999;
}

.status-pending {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.status-approved {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.status-rejected {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.status-withdrawn {
  background: rgba(217, 217, 217, 0.1);
  color: #d9d9d9;
}

.header-right {
  text-align: right;
}

.amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #1890ff;
}

/* 卡片内容 */
.card-content {
  margin-bottom: 25rpx;
}

.info-row {
  display: flex;
  margin-bottom: 12rpx;
  font-size: 26rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}

.info-value {
  color: #333;
  flex: 1;
}

.info-value.description {
  color: #666;
  line-height: 1.4;
}

/* 操作按钮 */
.card-actions {
  display: flex;
  gap: 15rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  flex: 1;
  padding: 15rpx 20rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.view {
  background: #f0f0f0;
  color: #666;
}

.action-btn.edit {
  background: rgba(250, 140, 22, 0.1);
  color: #fa8c16;
}

.action-btn.approve {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.action-btn.delete {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.action-btn:active {
  opacity: 0.7;
}

/* 加载更多 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-more .loading-spinner {
  margin-right: 15rpx;
  margin-bottom: 0;
}

/* 没有更多数据 */
.no-more {
  text-align: center;
  padding: 40rpx 0;
}

.no-more-text {
  font-size: 26rpx;
  color: #999;
}