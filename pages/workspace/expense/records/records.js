/**
 * 费用报销申请记录页面
 */

const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');
const { 
  checkPageAccess, 
  getCurrentUser, 
  checkOperationPermission,
  getDataFilter,
  ROLES,
  handlePermissionDenied 
} = require('../../../../utils/permission-checker.js');

Page({
  data: {
    loading: true,
    refreshing: false,
    loadingMore: false,
    hasMore: true,
    
    // 权限控制
    userRole: '',
    canViewAll: false,
    canApprove: false,
    canEdit: false,
    
    // 列表数据
    records: [],
    total: 0,
    
    // 分页参数
    page: 1,
    pageSize: 20,
    
    // 筛选条件
    filters: {
      status: '', // 状态筛选
      dateRange: '', // 日期范围
      amountRange: '' // 金额范围
    },
    
    // 状态选项
    statusOptions: [
      { label: '全部', value: '' },
      { label: '草稿', value: 'draft' },
      { label: '待审批', value: 'pending' },
      { label: '已通过', value: 'approved' },
      { label: '已拒绝', value: 'rejected' },
      { label: '已撤回', value: 'withdrawn' }
    ],
    
    // 状态映射
    statusMap: {
      'draft': { text: '草稿', color: '#999' },
      'pending': { text: '待审批', color: '#1890ff' },
      'approved': { text: '已通过', color: '#52c41a' },
      'rejected': { text: '已拒绝', color: '#ff4d4f' },
      'withdrawn': { text: '已撤回', color: '#d9d9d9' }
    }
  },

  onLoad(options) {
    // 检查页面访问权限
    if (!this.checkPermissions()) {
      return;
    }
    
    this.loadRecords();
  },

  /**
   * 检查页面访问权限
   */
  checkPermissions() {
    const currentUser = getCurrentUser();
    const hasAccess = checkPageAccess('expense/records');
    
    if (!hasAccess) {
      handlePermissionDenied('您没有权限查看费用报销记录');
      return false;
    }
    
    // 设置权限状态
    this.setData({
      userRole: currentUser.role,
      canViewAll: currentUser.role !== ROLES.EMPLOYEE,
      canApprove: [ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN].includes(currentUser.role),
      canEdit: true // 所有人都可以编辑自己的记录
    });
    
    return true;
  },

  /**
   * 加载申请记录
   */
  async loadRecords(isRefresh = false) {
    if (isRefresh) {
      this.setData({ 
        page: 1, 
        records: [], 
        hasMore: true,
        refreshing: true 
      });
    } else {
      this.setData({ loading: true });
    }

    try {
      const currentUser = getCurrentUser();
      const params = {
        page: this.data.page,
        pageSize: this.data.pageSize,
        ...this.data.filters
      };
      
      // 根据权限添加数据过滤条件
      const dataFilter = getDataFilter('expense', currentUser.role);
      Object.assign(params, dataFilter);

      const response = await request.get(API.API_ENDPOINTS.WORKSPACE.EXPENSE.LIST, params);

      if (response.success) {
        const newRecords = response.data.list || [];
        const records = isRefresh ? newRecords : [...this.data.records, ...newRecords];
        
        this.setData({
          records,
          total: response.data.total || 0,
          hasMore: newRecords.length === this.data.pageSize
        });
      } else {
        wx.showToast({
          title: response.message || '加载失败',
          icon: 'none'
        });
      }

    } catch (error) {
      console.error('加载申请记录失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ 
        loading: false, 
        refreshing: false,
        loadingMore: false 
      });
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadRecords(true).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (!this.data.hasMore || this.data.loadingMore) {
      return;
    }

    this.setData({ 
      page: this.data.page + 1,
      loadingMore: true 
    });
    
    this.loadRecords();
  },

  /**
   * 查看申请详情
   */
  onViewDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/workspace/expense/detail/detail?id=${id}`
    });
  },

  /**
   * 编辑申请
   */
  onEditRecord(e) {
    const { id, status, userId } = e.currentTarget.dataset;
    const currentUser = getCurrentUser();
    
    // 检查编辑权限
    const canEdit = checkOperationPermission('edit', 'own', currentUser.role, {
      owner_id: userId,
      status: status
    });
    
    if (!canEdit) {
      wx.showToast({
        title: '无权限编辑此记录',
        icon: 'none'
      });
      return;
    }
    
    // 只有草稿和被拒绝的申请可以编辑
    if (!['draft', 'rejected'].includes(status)) {
      wx.showToast({
        title: '只能编辑草稿或被退回的申请',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/workspace/expense/apply/apply?id=${id}&mode=edit`
    });
  },

  /**
   * 审批申请
   */
  onApproveRecord(e) {
    const { id, status } = e.currentTarget.dataset;
    
    if (!this.data.canApprove) {
      wx.showToast({
        title: '无权限审批',
        icon: 'none'
      });
      return;
    }
    
    if (status !== 'pending') {
      wx.showToast({
        title: '只能审批待审批的申请',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/workspace/expense/approve/approve?id=${id}`
    });
  },

  /**
   * 删除申请
   */
  onDeleteRecord(e) {
    const { id, status, userId } = e.currentTarget.dataset;
    const currentUser = getCurrentUser();
    
    // 检查删除权限
    const canDelete = checkOperationPermission('delete', 'own', currentUser.role, {
      owner_id: userId,
      status: status
    });
    
    if (!canDelete) {
      wx.showToast({
        title: '无权限删除此记录',
        icon: 'none'
      });
      return;
    }
    
    // 只有草稿状态可以删除
    if (status !== 'draft') {
      wx.showToast({
        title: '只能删除草稿状态的申请',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这条申请记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.deleteRecord(id);
        }
      }
    });
  },

  /**
   * 执行删除操作
   */
  async deleteRecord(id) {
    try {
      wx.showLoading({ title: '删除中...' });
      
      const response = await request.delete(`${API.API_ENDPOINTS.WORKSPACE.EXPENSE.DELETE}/${id}`);
      
      if (response.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        
        // 刷新列表
        this.loadRecords(true);
      } else {
        wx.showToast({
          title: response.message || '删除失败',
          icon: 'none'
        });
      }
      
    } catch (error) {
      console.error('删除申请失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 状态筛选
   */
  onStatusFilter(e) {
    const { value } = e.currentTarget.dataset;
    this.setData({
      'filters.status': value
    });
    this.loadRecords(true);
  },

  /**
   * 新建申请
   */
  onCreateNew() {
    wx.navigateTo({
      url: '/pages/workspace/expense/apply/apply'
    });
  },

  /**
   * 格式化金额
   */
  formatAmount(amount) {
    return parseFloat(amount || 0).toFixed(2);
  },

  /**
   * 格式化日期
   */
  formatDate(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  }
});
