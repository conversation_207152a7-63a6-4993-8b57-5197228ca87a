<!--费用报销申请记录页面-->
<view class="container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="header-content">
      <text class="title">费用报销记录</text>
      <text class="subtitle">共 {{total}} 条记录</text>
    </view>
    <button class="create-btn" bindtap="onCreateNew">
      <text class="create-icon">+</text>
      <text class="create-text">新建</text>
    </button>
  </view>

  <!-- 筛选区域 -->
  <view class="filter-section">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-item" 
            wx:for="{{statusOptions}}" 
            wx:key="value"
            class="filter-item {{filters.status === item.value ? 'active' : ''}}"
            data-value="{{item.value}}"
            bindtap="onStatusFilter">
        <text class="filter-text">{{item.label}}</text>
      </view>
    </scroll-view>
  </view>



  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 记录列表 -->
  <view wx:else class="records-list">
    <!-- 空状态 -->
    <view wx:if="{{records.length === 0}}" class="empty-state">
      <image class="empty-icon" src="/images/icon_empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无申请记录</text>
      <button class="empty-action" bindtap="onCreateNew">立即创建</button>
    </view>

    <!-- 记录卡片 -->
    <view wx:else>
      <view class="record-card" 
            wx:for="{{records}}" 
            wx:key="id"
            bindtap="onViewDetail"
            data-id="{{item.id}}">
        
        <!-- 卡片头部 -->
        <view class="card-header">
          <view class="header-left">
            <text class="record-title">{{item.title}}</text>
            <view class="status-badge status-{{item.status}}">
              <text class="status-text">{{statusMap[item.status].text}}</text>
            </view>
          </view>
          <view class="header-right">
            <text class="amount">¥{{formatAmount(item.amount)}}</text>
          </view>
        </view>

        <!-- 卡片内容 -->
        <view class="card-content">
          <view class="info-row">
            <text class="info-label">申请人：</text>
            <text class="info-value">{{item.applicant_name}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">申请时间：</text>
            <text class="info-value">{{formatDate(item.created_at)}}</text>
          </view>
          <view wx:if="{{item.description}}" class="info-row">
            <text class="info-label">说明：</text>
            <text class="info-value description">{{item.description}}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="card-actions" catchtap="true">
          <!-- 查看详情 -->
          <button class="action-btn view" 
                  bindtap="onViewDetail" 
                  data-id="{{item.id}}">
            查看
          </button>
          
          <!-- 编辑按钮（草稿和被拒绝状态可编辑） -->
          <button wx:if="{{(item.status === 'draft' || item.status === 'rejected') && (canEdit || item.user_id === userInfo.id)}}"
                  class="action-btn edit" 
                  bindtap="onEditRecord" 
                  data-id="{{item.id}}"
                  data-status="{{item.status}}"
                  data-user-id="{{item.user_id}}">
            编辑
          </button>
          
          <!-- 审批按钮（待审批状态且有审批权限） -->
          <button wx:if="{{item.status === 'pending' && canApprove}}"
                  class="action-btn approve" 
                  bindtap="onApproveRecord" 
                  data-id="{{item.id}}"
                  data-status="{{item.status}}">
            审批
          </button>
          
          <!-- 删除按钮（草稿状态可删除） -->
          <button wx:if="{{item.status === 'draft' && (canEdit || item.user_id === userInfo.id)}}"
                  class="action-btn delete" 
                  bindtap="onDeleteRecord" 
                  data-id="{{item.id}}"
                  data-status="{{item.status}}"
                  data-user-id="{{item.user_id}}">
            删除
          </button>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{loadingMore}}" class="loading-more">
      <view class="loading-spinner small"></view>
      <text class="loading-text">加载更多...</text>
    </view>

    <!-- 没有更多数据 -->
    <view wx:if="{{!hasMore && records.length > 0}}" class="no-more">
      <text class="no-more-text">没有更多数据了</text>
    </view>
  </view>
</view>
