// pages/workspace/activity/apply/apply.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');

Page({
  data: {
    // 表单数据
    formData: {
      title: '',
      description: '',
      amount: '',
      expectedDate: '',
      category: '',
      attachments: []
    },
    
    // 活动经费类别选项
    categoryOptions: [
      { value: 'training', label: '📚 培训活动', description: '员工培训、技能提升、专业学习等活动' },
      { value: 'teambuilding', label: '🤝 团建活动', description: '团队建设、拓展训练、集体活动等' },
      { value: 'conference', label: '📋 会议活动', description: '会议组织、研讨会、论坛等活动' },
      { value: 'celebration', label: '🎉 庆典活动', description: '节日庆祝、周年庆典、表彰大会等' },
      { value: 'marketing', label: '📢 营销活动', description: '产品发布、推广活动、展览展示等' },
      { value: 'welfare', label: '🎁 员工福利', description: '员工福利、生日会、节日礼品等' },
      { value: 'charity', label: '❤️ 公益活动', description: '慈善捐赠、志愿服务、社会责任等' },
      { value: 'cultural', label: '🎭 文化活动', description: '文艺演出、文化交流、艺术活动等' },
      { value: 'sports', label: '⚽ 体育活动', description: '体育比赛、健身活动、运动会等' },
      { value: 'other', label: '📝 其他活动', description: '其他未分类的活动经费' }
    ],

    // 选中的类别索引
    selectedCategoryIndex: -1,
    // 选中类别的显示文本
    selectedCategoryLabel: '',
    // 是否已选择类别
    hasSelectedCategory: false,

    // 格式化的日期显示
    formattedDate: '',
    // 是否已选择日期
    hasExpectedDate: false,

    // 表单验证
    errors: {},

    // 页面状态
    loading: false,
    submitting: false,

    // 日期范围限制
    minDate: '',
    maxDate: ''
  },

  onLoad(options) {
    // 初始化日期范围
    this.initDateRange();

    // 如果是编辑模式，加载申请数据
    if (options.id) {
      this.loadApplicationData(options.id);
    }
  },

  /**
   * 初始化日期范围
   */
  initDateRange() {
    const today = new Date();
    const minDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const maxDate = new Date(today.getFullYear() + 1, today.getMonth(), today.getDate());

    this.setData({
      minDate: this.formatDateForPicker(minDate),
      maxDate: this.formatDateForPicker(maxDate)
    });
  },

  /**
   * 加载申请数据（编辑模式）
   */
  async loadApplicationData(id) {
    try {
      this.setData({ loading: true });
      
      const response = await request.get(API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.DETAIL(id));
      
      if (response && response.success) {
        const application = response.data;

        // 查找类别索引
        const categoryIndex = this.data.categoryOptions.findIndex(
          item => item.value === (application.metadata?.category || '')
        );

        const expectedDate = application.expectedDate || '';
        this.setData({
          formData: {
            title: application.title || '',
            description: application.description || '',
            amount: application.amount ? application.amount.toString() : '',
            expectedDate,
            category: application.metadata?.category || '',
            attachments: application.attachments || []
          },
          selectedCategoryIndex: categoryIndex,
          selectedCategoryLabel: categoryIndex >= 0 ? this.data.categoryOptions[categoryIndex].label : '',
          formattedDate: expectedDate ? this.formatDate(expectedDate) : '',
          hasExpectedDate: !!expectedDate,
          hasSelectedCategory: categoryIndex >= 0
        });

        // 更新标题
        wx.setNavigationBarTitle({
          title: application.title ? `编辑活动经费` : '活动经费'
        });
      }
    } catch (error) {
      console.error('加载申请数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 输入框变化处理
   */
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value,
      [`errors.${field}`]: '' // 清除错误信息
    });
  },

  /**
   * 类别选择
   */
  onCategoryChange(e) {
    console.log('onCategoryChange 被调用:', e.detail);

    const index = parseInt(e.detail.value);
    const category = this.data.categoryOptions[index];

    console.log('类别选择:', { index, category, categoryOptions: this.data.categoryOptions });

    if (!category) {
      console.error('未找到对应的类别:', index);
      return;
    }

    this.setData({
      'formData.category': category.value,
      selectedCategoryIndex: index,
      selectedCategoryLabel: category.label,
      hasSelectedCategory: true,
      'errors.category': ''
    });

    console.log('类别选择完成:', {
      category: category.value,
      label: category.label,
      index: index
    });

    // 显示选择反馈
    wx.showToast({
      title: `已选择: ${category.label}`,
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 日期选择
   */
  onDateChange(e) {
    console.log('onDateChange 被调用:', e.detail);

    const selectedDate = e.detail.value;
    const formatted = this.formatDate(selectedDate);

    console.log('日期选择:', { selectedDate, formatted });

    this.setData({
      'formData.expectedDate': selectedDate,
      formattedDate: formatted,
      hasExpectedDate: !!selectedDate,
      'errors.expectedDate': ''
    });

    // 显示选择反馈
    if (selectedDate) {
      wx.showToast({
        title: `已选择: ${formatted}`,
        icon: 'success',
        duration: 1500
      });
    }
  },

  /**
   * 选择附件
   */
  onChooseAttachment() {
    wx.chooseMedia({
      count: 9,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFiles = res.tempFiles;
        const attachments = [...this.data.formData.attachments];
        
        tempFiles.forEach(file => {
          attachments.push({
            type: 'image',
            url: file.tempFilePath,
            size: file.size
          });
        });
        
        this.setData({
          'formData.attachments': attachments
        });
      }
    });
  },

  /**
   * 删除附件
   */
  onDeleteAttachment(e) {
    const { index } = e.currentTarget.dataset;
    const attachments = [...this.data.formData.attachments];
    attachments.splice(index, 1);
    
    this.setData({
      'formData.attachments': attachments
    });
  },

  /**
   * 预览附件
   */
  onPreviewAttachment(e) {
    const { index } = e.currentTarget.dataset;
    const attachments = this.data.formData.attachments;
    const urls = attachments.filter(item => item.type === 'image').map(item => item.url);
    
    wx.previewImage({
      current: attachments[index].url,
      urls: urls
    });
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData } = this.data;
    const errors = {};
    
    if (!formData.title.trim()) {
      errors.title = '请输入申请标题';
    }
    
    if (!formData.description.trim()) {
      errors.description = '请输入活动经费说明';
    }
    
    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      errors.amount = '请输入有效的金额';
    }
    
    if (!formData.category) {
      errors.category = '请选择活动经费类别';
    }
    
    this.setData({ errors });
    return Object.keys(errors).length === 0;
  },

  /**
   * 保存草稿
   */
  async onSaveDraft() {
    if (!this.validateForm()) {
      return;
    }
    
    await this.submitApplication('draft');
  },

  /**
   * 提交申请
   */
  async onSubmit() {
    if (!this.validateForm()) {
      return;
    }
    
    await this.submitApplication('pending');
  },

  /**
   * 提交申请数据
   */
  async submitApplication(status) {
    try {
      this.setData({ submitting: true });
      
      const { formData } = this.data;
      
      // 构建提交数据
      const submitData = {
        type: 'activity',
        title: formData.title.trim(),
        description: formData.description.trim(),
        amount: parseFloat(formData.amount),
        expectedDate: formData.expectedDate,
        attachments: formData.attachments,
        metadata: {
          category: formData.category
        }
      };
      
      const response = await request.post(API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.CREATE, submitData);
      
      if (response && response.success) {
        wx.showToast({
          title: status === 'draft' ? '草稿已保存' : '申请已提交',
          icon: 'success'
        });
        
        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(response?.message || '提交失败');
      }
    } catch (error) {
      console.error('提交申请失败:', error);
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'error'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  /**
   * 获取类别显示文本
   */
  getCategoryText(value) {
    const option = this.data.categoryOptions.find(item => item.value === value);
    return option ? option.label : '请选择';
  },

  /**
   * 格式化日期显示
   */
  formatDate(dateStr) {
    if (!dateStr) return '';

    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}年${month}月${day}日`;
  },

  /**
   * 格式化日期为选择器格式
   */
  formatDateForPicker(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }
});
