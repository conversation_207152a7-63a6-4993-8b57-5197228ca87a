// pages/workspace/activity/detail/detail.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');

Page({
  data: {
    activityId: '',
    activityDetail: null,
    loading: true,
    
    statusMap: {
      'draft': '草稿',
      'pending': '待审批',
      'processing': '审批中',
      'approved': '已通过',
      'rejected': '已拒绝',
      'cancelled': '已取消'
    },
    
    statusColorMap: {
      'draft': '#d9d9d9',
      'pending': '#faad14',
      'processing': '#1890ff',
      'approved': '#52c41a',
      'rejected': '#ff4d4f',
      'cancelled': '#d9d9d9'
    },
    
    approvalFlow: [],
    attachments: []
  },

  onLoad(options) {
    if (options.id) {
      this.setData({ activityId: options.id });
      this.loadActivityDetail();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  async loadActivityDetail() {
    try {
      this.setData({ loading: true });
      
      const response = await request.get(API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.DETAIL(this.data.activityId));
      
      if (response.success) {
        this.setData({
          activityDetail: response.data,
          approvalFlow: response.data.approval_flow || [],
          attachments: response.data.attachments || [],
          loading: false
        });
        
        wx.setNavigationBarTitle({
          title: response.data.title || '活动经费详情'
        });
      } else {
        throw new Error(response.message || '加载失败');
      }
    } catch (error) {
      console.error('加载活动经费详情失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  onEditActivity() {
    wx.navigateTo({
      url: `/pages/workspace/activity/apply/apply?id=${this.data.activityId}&mode=edit`
    });
  },

  async onSubmitActivity() {
    try {
      wx.showLoading({ title: '提交中...' });
      
      const response = await request.post(API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.SUBMIT(this.data.activityId));
      
      if (response.success) {
        wx.showToast({
          title: '提交成功',
          icon: 'success'
        });
        
        this.loadActivityDetail();
      } else {
        throw new Error(response.message || '提交失败');
      }
    } catch (error) {
      console.error('提交活动经费失败:', error);
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  async onCancelActivity() {
    const result = await wx.showModal({
      title: '确认撤销',
      content: '确定要撤销这个活动经费吗？',
      confirmText: '撤销',
      confirmColor: '#eb2f96'
    });
    
    if (!result.confirm) return;
    
    try {
      wx.showLoading({ title: '撤销中...' });
      
      const response = await request.post(API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.CANCEL(this.data.activityId));
      
      if (response.success) {
        wx.showToast({
          title: '撤销成功',
          icon: 'success'
        });
        
        this.loadActivityDetail();
      } else {
        throw new Error(response.message || '撤销失败');
      }
    } catch (error) {
      console.error('撤销活动经费失败:', error);
      wx.showToast({
        title: error.message || '撤销失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  onPreviewAttachment(e) {
    const { url, type } = e.currentTarget.dataset;
    
    if (type === 'image') {
      wx.previewImage({
        current: url,
        urls: this.data.attachments.filter(item => item.type === 'image').map(item => item.url)
      });
    } else {
      wx.downloadFile({
        url: url,
        success: (res) => {
          wx.openDocument({
            filePath: res.tempFilePath
          });
        }
      });
    }
  },

  formatTime(timeStr) {
    if (!timeStr) return '';
    const time = new Date(timeStr);
    return time.toLocaleString();
  },

  onPullDownRefresh() {
    this.loadActivityDetail().then(() => {
      wx.stopPullDownRefresh();
    });
  }
});