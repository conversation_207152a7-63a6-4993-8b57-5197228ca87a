/* pages/workspace/activity/list/list.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background: linear-gradient(135deg, #eb2f96, #f759ab);
  padding: 40rpx 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  color: white;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  margin-top: 10rpx;
  display: block;
}

.create-btn-container {
  margin-bottom: 20rpx;
}

.create-btn {
  background: #eb2f96;
  color: white;
  border-radius: 25rpx;
  border: none;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.create-btn-text {
  color: white;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #eb2f96;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.5;
}

.empty-text {
  margin-top: 20rpx;
  font-size: 32rpx;
  color: #999;
}

.empty-desc {
  margin-top: 10rpx;
  font-size: 26rpx;
  color: #ccc;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.activity-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.activity-item:active {
  transform: scale(0.98);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.item-title {
  display: flex;
  align-items: center;
  gap: 15rpx;
  flex: 1;
}

.activity-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status-tag {
  color: white;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: normal;
}

.item-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #eb2f96;
}

.item-content {
  margin-bottom: 20rpx;
}

.item-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  display: block;
  margin-bottom: 15rpx;
}

.item-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  font-size: 24rpx;
  color: #999;
}

.item-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.action-btn {
  padding: 12rpx 24rpx !important;
  font-size: 26rpx !important;
  border-radius: 20rpx !important;
  border: none !important;
}

.edit-btn {
  background: #faad14 !important;
  color: white !important;
}

.submit-btn {
  background: #eb2f96 !important;
  color: white !important;
}

.load-more {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 26rpx;
}