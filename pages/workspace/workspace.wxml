<!-- pages/workspace/workspace.wxml -->
<view class="workspace-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="workspace-content">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="user-info">
        <image class="user-avatar" src="{{userInfo.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        <view class="user-details">
          <text class="user-name">{{userInfo.name || '用户'}}</text>
        </view>
      </view>
      <view class="header-actions">
        <view class="action-btn" bindtap="refreshData">
          <text class="iconfont icon-refresh {{refreshing ? 'rotating' : ''}}">🔄</text>
        </view>
      </view>
    </view>

    <!-- 统计卡片 -->
    <view class="statistics-section">
      <view class="section-title">
        <text>数据概览</text>
      </view>
      <view class="statistics-grid">
        <view class="stat-card" bindtap="onStatisticTap" data-type="applications">
          <view class="stat-icon">📋</view>
          <view class="stat-content">
            <text class="stat-number">{{statistics.myApplications}}</text>
            <text class="stat-label">我的申请</text>
          </view>
        </view>
        
        <view wx:if="{{permissions.canApprove}}" class="stat-card" bindtap="onStatisticTap" data-type="approvals">
          <view class="stat-icon">✅</view>
          <view class="stat-content">
            <text class="stat-number">{{statistics.pendingApprovals}}</text>
            <text class="stat-label">待审批</text>
          </view>
        </view>

        <view wx:if="{{permissions.canViewFinanceData}}" class="stat-card" bindtap="onStatisticTap" data-type="finance">
          <view class="stat-icon">💰</view>
          <view class="stat-content">
            <text class="stat-number">{{statistics.monthlyExpense}}</text>
            <text class="stat-label">本月支出</text>
          </view>
        </view>
        
        <view class="stat-card" bindtap="onStatisticTap" data-type="finance">
          <view class="stat-icon">📈</view>
          <view class="stat-content">
            <text class="stat-number">{{statistics.netIncome}}</text>
            <text class="stat-label">净收入</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能模块 -->
    <view class="function-modules-section">
      <view class="section-title">
        <text>功能模块</text>
      </view>
      <view class="modules-grid">
        <view wx:for="{{functionModules}}" wx:key="id"
              class="module-card"
              bindtap="onModuleTap"
              data-url="{{item.url}}"
              style="border-left-color: {{item.color}}">
          <view class="module-icon" style="color: {{item.color}}">{{item.icon}}</view>
          <view class="module-content">
            <text class="module-title">{{item.title}}</text>
            <text class="module-desc">{{item.description}}</text>
          </view>
          <view class="module-arrow">></view>
        </view>
      </view>
    </view>

    <!-- 快速操作 -->
    <view wx:if="{{quickActions.length > 0}}" class="quick-actions-section">
      <view class="section-title">
        <text>快速申请</text>
      </view>
      <view class="quick-actions-grid">
        <view wx:for="{{quickActions}}" wx:key="id"
              class="quick-action-item"
              bindtap="onQuickActionTap"
              data-url="{{item.url}}">
          <view class="action-icon" style="background-color: {{item.color}}">{{item.icon}}</view>
          <text class="action-title">{{item.title}}</text>
        </view>
      </view>
    </view>

    <!-- 最近活动 -->
    <view class="recent-activities-section">
      <view class="section-header">
        <text class="section-title">最近活动</text>
        <text class="section-more" bindtap="onViewAllApplications">查看全部</text>
      </view>
      
      <view wx:if="{{recentActivities.length > 0}}" class="activities-list">
        <view wx:for="{{recentActivities}}" wx:key="id" 
              class="activity-item" 
              bindtap="onActivityTap" 
              data-id="{{item.id}}" 
              data-type="{{item.type}}">
          <view class="activity-icon">
            <text class="activity-type-icon">{{item.type === 'application' ? '📄' : '🔔'}}</text>
          </view>
          <view class="activity-content">
            <text class="activity-title">{{item.title}}</text>
            <text class="activity-desc">{{item.description}}</text>
            <view class="activity-meta">
              <text class="activity-time">{{formatTime(item.time)}}</text>
              <view class="activity-status {{getStatusClass(item.status)}}">
                <text>{{getStatusText(item.status)}}</text>
              </view>
            </view>
          </view>
          <view class="activity-arrow">
            <text class="arrow-icon">→</text>
          </view>
        </view>
      </view>
      
      <view wx:else class="empty-activities">
        <view class="empty-icon">📭</view>
        <text class="empty-text">暂无最近活动</text>
      </view>
    </view>

    <!-- 功能入口 -->
    <view class="function-entries-section">
      <view class="section-title">
        <text>功能入口</text>
      </view>
      <view class="function-entries">
        <view class="function-item" bindtap="onViewAllApprovals">
          <view class="function-icon">⏳</view>
          <view class="function-content">
            <text class="function-title">待办审批</text>
            <text class="function-desc">处理待审批的申请</text>
          </view>
          <view class="function-badge" wx:if="{{statistics.pendingApprovals > 0}}">
            <text>{{statistics.pendingApprovals}}</text>
          </view>
        </view>
        
        <view class="function-item" bindtap="onViewFinanceReports">
          <view class="function-icon">📊</view>
          <view class="function-content">
            <text class="function-title">财务报表</text>
            <text class="function-desc">查看财务数据和报表</text>
          </view>
        </view>
        
        <view wx:if="{{permissions.canViewAll}}" class="function-item">
          <view class="function-icon">👥</view>
          <view class="function-content">
            <text class="function-title">团队管理</text>
            <text class="function-desc">管理团队成员和权限</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 自定义导航栏 -->
<view class="custom-navbar">
  <text class="navbar-title">工作台</text>
</view>
