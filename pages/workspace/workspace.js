// pages/workspace/workspace.js
const { request } = require('../../utils/api');
const API = require('../../constants/api-unified.constants');
const { getUserInfo } = require('../../utils/user-info');
const {
  getCurrentUser,
  hasRole<PERSON>r<PERSON>igher,
  getAccessibleApplicationTypes,
  checkPageAccess,
  ROLES
} = require('../../utils/permission-checker');

Page({
  data: {
    // 用户信息
    userInfo: null,
    userRole: '',
    userRoleName: '',

    // 加载状态
    loading: true,
    refreshing: false,

    // 统计数据
    statistics: {
      myApplications: 0,
      pendingApprovals: 0,
      monthlyExpense: 0,
      monthlyIncome: 0,
      netIncome: 0
    },

    // 功能模块（根据权限动态显示）
    functionModules: [],

    // 快速操作（根据权限动态显示）
    quickActions: [],

    // 最近活动
    recentActivities: [],

    // 权限信息
    permissions: {
      canViewAll: false,
      canApprove: false,
      canExport: false,
      canManageUsers: false,
      canViewFinanceData: false,
      canCreateApplication: true,
      canViewFinanceOverview: false,
      canViewReports: false
    },

    // 可访问的申请类型
    accessibleApplicationTypes: []
  },

  onLoad() {
    this.initPage();
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadDashboardData();
  },

  /**
   * 初始化权限和功能模块
   */
  initPermissions() {
    const currentUser = getCurrentUser();
    const userRole = currentUser.role || ROLES.EMPLOYEE;

    // 设置用户信息
    this.setData({
      userRole: userRole,
      userRoleName: '',
      accessibleApplicationTypes: getAccessibleApplicationTypes(userRole)
    });

    // 设置权限
    const permissions = {
      canViewAll: userRole !== ROLES.EMPLOYEE,
      canApprove: [ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN].includes(userRole),
      canExport: [ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN].includes(userRole),
      canManageUsers: [ROLES.MANAGER, ROLES.ADMIN].includes(userRole),
      canViewFinanceData: [ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN].includes(userRole),
      canCreateApplication: true, // 所有用户都可以创建申请
      canViewFinanceOverview: checkPageAccess('finance/overview', userRole),
      canViewReports: [ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN].includes(userRole)
    };

    this.setData({ permissions });

    // 初始化功能模块
    this.initFunctionModules();

    // 初始化快速操作
    this.initQuickActions();
  },



  /**
   * 初始化功能模块
   */
  initFunctionModules() {
    const { permissions, accessibleApplicationTypes } = this.data;
    const modules = [];

    // 财务概览模块（财务人员及以上可见）
    if (permissions.canViewFinanceOverview) {
      modules.push({
        id: 'finance-overview',
        title: '财务概览',
        description: '查看财务统计和趋势分析',
        icon: '📊',
        color: '#1890ff',
        url: '/pages/workspace/finance/overview/overview'
      });
    }

    // 我的申请模块（所有用户可见）
    modules.push({
      id: 'my-applications',
      title: '我的申请',
      description: '查看和管理我的申请记录',
      icon: '📝',
      color: '#52c41a',
      url: '/pages/workspace/my-applications/my-applications'
    });

    // 申请审批模块（有审批权限的用户可见）
    if (permissions.canApprove) {
      modules.push({
        id: 'pending-approvals',
        title: '待审批',
        description: '处理待审批的申请',
        icon: '✅',
        color: '#fa8c16',
        url: '/pages/workspace/approval/pending/pending'
      });
    }

    // 申请记录模块（根据权限显示不同类型）
    if (accessibleApplicationTypes.includes('expense')) {
      modules.push({
        id: 'expense-records',
        title: '费用报销',
        description: '费用报销申请记录',
        icon: '💰',
        color: '#1890ff',
        url: '/pages/workspace/expense/records/records'
      });
    }

    if (accessibleApplicationTypes.includes('payment')) {
      modules.push({
        id: 'payment-records',
        title: '付款申请',
        description: '付款申请记录',
        icon: '💳',
        color: '#fa8c16',
        url: '/pages/workspace/payment/records/records'
      });
    }

    if (accessibleApplicationTypes.includes('contract')) {
      modules.push({
        id: 'contract-records',
        title: '合同申请',
        description: '合同申请记录',
        icon: '📄',
        color: '#722ed1',
        url: '/pages/workspace/contract/records/records'
      });
    }

    if (accessibleApplicationTypes.includes('activity')) {
      modules.push({
        id: 'activity-records',
        title: '活动经费',
        description: '活动经费申请记录',
        icon: '🎉',
        color: '#eb2f96',
        url: '/pages/workspace/activity/records/records'
      });
    }

    if (accessibleApplicationTypes.includes('reserve')) {
      modules.push({
        id: 'reserve-records',
        title: '备用金',
        description: '备用金申请记录',
        icon: '🏦',
        color: '#13c2c2',
        url: '/pages/workspace/reserve/records/records'
      });
    }

    if (accessibleApplicationTypes.includes('purchase')) {
      modules.push({
        id: 'purchase-records',
        title: '采购申请',
        description: '采购申请记录',
        icon: '🛒',
        color: '#52c41a',
        url: '/pages/workspace/purchase/records/records'
      });
    }

    // 报表模块（有查看报表权限的用户可见）
    if (permissions.canViewReports) {
      modules.push({
        id: 'reports',
        title: '数据报表',
        description: '查看各类统计报表',
        icon: '📈',
        color: '#9254de',
        url: '/pages/workspace/reports/reports'
      });
    }

    this.setData({ functionModules: modules });
  },

  /**
   * 初始化快速操作
   */
  initQuickActions() {
    const { accessibleApplicationTypes } = this.data;
    const actions = [];

    // 根据可访问的申请类型添加快速申请操作
    if (accessibleApplicationTypes.includes('expense')) {
      actions.push({
        id: 'quick-expense',
        title: '费用报销',
        icon: '💰',
        color: '#1890ff',
        url: '/pages/workspace/expense/apply/apply'
      });
    }

    if (accessibleApplicationTypes.includes('payment')) {
      actions.push({
        id: 'quick-payment',
        title: '付款申请',
        icon: '💳',
        color: '#fa8c16',
        url: '/pages/workspace/payment/apply/apply'
      });
    }

    if (accessibleApplicationTypes.includes('activity')) {
      actions.push({
        id: 'quick-activity',
        title: '活动经费',
        icon: '🎉',
        color: '#eb2f96',
        url: '/pages/workspace/activity/apply/apply'
      });
    }

    if (accessibleApplicationTypes.includes('reserve')) {
      actions.push({
        id: 'quick-reserve',
        title: '备用金',
        icon: '🏦',
        color: '#13c2c2',
        url: '/pages/workspace/reserve/apply/apply'
      });
    }

    // 限制快速操作数量，最多显示4个
    this.setData({ quickActions: actions.slice(0, 4) });
  },

  /**
   * 功能模块点击事件
   */
  onModuleTap(e) {
    const { url } = e.currentTarget.dataset;
    if (url) {
      wx.navigateTo({ url });
    }
  },

  /**
   * 快速操作点击事件
   */
  onQuickActionTap(e) {
    const { url } = e.currentTarget.dataset;
    if (url) {
      wx.navigateTo({ url });
    }
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 获取用户信息
      const userInfo = await getUserInfo();
      this.setData({
        userInfo,
        userRole: userInfo.role || 'employee'
      });

      // 初始化权限和功能模块
      this.initPermissions();

      // 加载工作台数据
      await this.loadDashboardData();
    } catch (error) {
      console.error('初始化工作台失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载工作台数据
   */
  async loadDashboardData() {
    try {
      const response = await request.get(API.ENDPOINTS.WORKSPACE.DASHBOARD);
      
      if (response && response.success) {
        const { statistics, recentActivities, quickActions, permissions } = response.data;
        
        this.setData({
          statistics: statistics || this.data.statistics,
          recentActivities: recentActivities || [],
          quickActions: quickActions || this.data.quickActions, // 使用默认配置作为后备
          permissions: permissions || this.data.permissions
        });
      }
    } catch (error) {
      console.error('加载工作台数据失败:', error);
      // 不显示错误提示，避免影响用户体验
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({ refreshing: true });
    
    try {
      await this.loadDashboardData();
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      });
    } catch (error) {
      wx.showToast({
        title: '刷新失败',
        icon: 'error'
      });
    } finally {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 财务管理入口点击
   */
  onFinanceEntryTap() {
    wx.navigateTo({
      url: '/pages/workspace/finance/overview/overview'
    });
  },

  /**
   * 统计卡片点击
   */
  onStatisticTap(e) {
    const { type } = e.currentTarget.dataset;
    
    switch (type) {
      case 'applications':
        wx.navigateTo({
          url: '/pages/workspace/applications/list'
        });
        break;
      case 'approvals':
        wx.navigateTo({
          url: '/pages/workspace/approval/pending/pending'
        });
        break;
      case 'finance':
        wx.navigateTo({
          url: '/pages/workspace/finance/overview/overview'
        });
        break;
      default:
        break;
    }
  },

  /**
   * 最近活动点击
   */
  onActivityTap(e) {
    const { id, type } = e.currentTarget.dataset;
    
    if (type === 'application' && id) {
      wx.navigateTo({
        url: `/pages/workspace/applications/detail?id=${id}`
      });
    }
  },

  /**
   * 查看所有申请
   */
  onViewAllApplications() {
    wx.navigateTo({
      url: '/pages/workspace/applications/list'
    });
  },

  /**
   * 查看所有待审批
   */
  onViewAllApprovals() {
    wx.navigateTo({
      url: '/pages/workspace/approval/pending/pending'
    });
  },

  /**
   * 查看财务报表
   */
  onViewFinanceReports() {
    wx.navigateTo({
      url: '/pages/workspace/reports/reports'
    });
  },

  /**
   * 格式化金额
   */
  formatAmount(amount) {
    if (!amount) return '0.00';
    return (amount / 100).toFixed(2);
  },

  /**
   * 格式化时间
   */
  formatTime(time) {
    if (!time) return '';
    
    const now = new Date();
    const target = new Date(time);
    const diff = now - target;
    
    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return target.toLocaleDateString();
    }
  },

  /**
   * 获取申请类型文本
   */
  getApplicationTypeText(type) {
    const typeMap = {
      expense: '费用报销',

      payment: '付款申请',
      contract: '合同申请',
      activity: '活动经费',
      reserve: '备用金'
    };
    return typeMap[type] || type;
  },

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      draft: '草稿',
      pending: '待审批',
      processing: '审批中',
      approved: '已批准',
      rejected: '已拒绝',
      cancelled: '已取消',
      completed: '已完成'
    };
    return statusMap[status] || status;
  },

  /**
   * 获取状态样式类
   */
  getStatusClass(status) {
    const classMap = {
      draft: 'status-draft',
      pending: 'status-pending',
      processing: 'status-processing',
      approved: 'status-approved',
      rejected: 'status-rejected',
      cancelled: 'status-cancelled',
      completed: 'status-completed'
    };
    return classMap[status] || 'status-default';
  }
});
