/* pages/workspace/workspace.wxss */



/* 功能模块样式 */
.function-modules-section {
  margin: 30rpx;
}

.modules-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.module-card {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  border-left: 6rpx solid #1890ff;
  transition: all 0.3s ease;
}

.module-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.module-icon {
  font-size: 48rpx;
  margin-right: 25rpx;
}

.module-content {
  flex: 1;
}

.module-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.module-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.module-arrow {
  font-size: 28rpx;
  color: #999;
}

/* 快速操作样式 */
.quick-actions-section {
  margin: 30rpx;
}

.quick-actions-grid {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.quick-action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 25rpx 15rpx;
  background: white;
  border-radius: 15rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.quick-action-item:active {
  transform: scale(0.95);
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: white;
  margin-bottom: 15rpx;
}

.action-title {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

@import '../../styles/workspace-common.wxss';

.workspace-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding-top: 88rpx;
  /* 为自定义导航栏留出空间 */
}

/* ==================== 自定义导航栏 ==================== */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.navbar-title {
  color: white;
  font-size: 36rpx;
  font-weight: 600;
}

/* ==================== 加载状态 ==================== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  color: #999;
  font-size: 28rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* ==================== 用户头部 ==================== */
.user-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.user-role {
  font-size: 24rpx;
  color: #666;
  background: #f0f2f5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  align-self: flex-start;
}

.header-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
  font-size: 28rpx;
}

.rotating {
  animation: spin 1s linear infinite;
}

/* ==================== 统计卡片 ==================== */
.statistics-section {
  margin: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.statistics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stat-card {
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.stat-card:active {
  transform: scale(0.98);
}

.stat-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* ==================== 财务管理入口 ==================== */
.finance-entry-section {
  margin: 20rpx;
}

.finance-entry-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.finance-entry-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.2);
}

.entry-icon {
  font-size: 56rpx;
  margin-right: 24rpx;
}

.entry-content {
  flex: 1;
}

.entry-title {
  display: block;
  font-size: 32rpx;
  color: #fff;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.entry-desc {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.entry-arrow {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: bold;
}

/* ==================== 最近活动 ==================== */
.recent-activities-section {
  margin: 20rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.section-more {
  font-size: 26rpx;
  color: #667eea;
}

.activities-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f2f5;
  transition: background-color 0.2s ease;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item:active {
  background: #f8f9fa;
}

.activity-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
  border-radius: 50%;
  margin-right: 20rpx;
  font-size: 28rpx;
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.activity-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.activity-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.activity-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.activity-time {
  font-size: 22rpx;
  color: #999;
}

.activity-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-approved {
  background: #d4edda;
  color: #155724;
}

.status-rejected {
  background: #f8d7da;
  color: #721c24;
}

.status-processing {
  background: #cce7ff;
  color: #004085;
}

.activity-arrow {
  margin-left: 20rpx;
  color: #ccc;
  font-size: 24rpx;
}

.empty-activities {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* ==================== 功能入口 ==================== */
.function-entries-section {
  margin: 20rpx;
  margin-bottom: 40rpx;
}

.function-entries {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.function-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f2f5;
  position: relative;
  transition: background-color 0.2s ease;
}

.function-item:last-child {
  border-bottom: none;
}

.function-item:active {
  background: #f8f9fa;
}

.function-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
  border-radius: 50%;
  margin-right: 20rpx;
  font-size: 28rpx;
}

.function-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.function-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.function-desc {
  font-size: 24rpx;
  color: #666;
}

.function-badge {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  min-width: 32rpx;
  text-align: center;
}