<!--pages/workspace/purchase/detail/detail.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 详情内容 -->
  <view wx:else class="detail-content">
    <!-- 基本信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <view class="title-section">
          <text class="purchase-title">{{purchaseDetail.title || '采购申请'}}</text>
          <view class="status-tag {{statusClassMap[purchaseDetail.status]}}">
            {{statusMap[purchaseDetail.status]}}
          </view>
        </view>
        <text class="purchase-amount">¥{{purchaseDetail.totalAmount}}</text>
      </view>

      <view class="card-content">
        <view class="info-row">
          <text class="label">申请人：</text>
          <text class="value">{{purchaseDetail.applicant_name}}</text>
        </view>
        <view class="info-row">
          <text class="label">申请时间：</text>
          <text class="value">{{purchaseDetail.created_at}}</text>
        </view>
        <view class="info-row">
          <text class="label">期望完成日期：</text>
          <text class="value">{{purchaseDetail.expectedDate}}</text>
        </view>
        <view class="info-row">
          <text class="label">紧急程度：</text>
          <text class="value">{{purchaseDetail.urgency}}</text>
        </view>
        <view class="info-row" wx:if="{{purchaseDetail.supplier}}">
          <text class="label">供应商：</text>
          <text class="value">{{purchaseDetail.supplier}}</text>
        </view>
        <view class="info-row" wx:if="{{purchaseDetail.supplierContact}}">
          <text class="label">供应商联系方式：</text>
          <text class="value">{{purchaseDetail.supplierContact}}</text>
        </view>
        <view class="info-row" wx:if="{{purchaseDetail.description}}">
          <text class="label">采购说明：</text>
          <text class="value description">{{purchaseDetail.description}}</text>
        </view>
      </view>
    </view>

    <!-- 采购项目明细 -->
    <view class="info-card" wx:if="{{purchaseDetail.items && purchaseDetail.items.length > 0}}">
      <view class="card-title">采购项目明细</view>
      <view class="purchase-items">
        <view wx:for="{{purchaseDetail.items}}" wx:key="id" class="purchase-item">
          <view class="item-header">
            <text class="item-name">{{item.name}}</text>
            <text class="item-subtotal">¥{{item.subtotal}}</text>
          </view>
          <view class="item-details">
            <text class="item-spec" wx:if="{{item.specification}}">规格：{{item.specification}}</text>
            <text class="item-quantity">数量：{{item.quantity}} {{item.unit || '个'}}</text>
            <text class="item-price">单价：¥{{item.unitPrice}}</text>
          </view>
          <text class="item-remark" wx:if="{{item.remark}}">备注：{{item.remark}}</text>
        </view>
      </view>
    </view>

    <!-- 附件信息 -->
    <view class="info-card" wx:if="{{purchaseDetail.attachments && purchaseDetail.attachments.length > 0}}">
      <view class="card-title">相关附件</view>
      <view class="attachments">
        <view wx:for="{{purchaseDetail.attachments}}" wx:key="id" class="attachment-item" bindtap="onPreviewAttachment" data-url="{{item.url}}">
          <image class="attachment-icon" src="/assets/icons/file.svg"></image>
          <text class="attachment-name">{{item.name}}</text>
          <text class="attachment-size">{{item.size}}</text>
        </view>
      </view>
    </view>

    <!-- 审批流程 -->
    <view class="info-card" wx:if="{{approvalFlow.length > 0}}">
      <view class="card-title">审批流程</view>
      <view class="approval-flow">
        <view wx:for="{{approvalFlow}}" wx:key="id" class="approval-step">
          <view class="step-icon {{item.status}}">
            <text wx:if="{{item.status === 'approved'}}">✓</text>
            <text wx:elif="{{item.status === 'rejected'}}">✗</text>
            <text wx:else>○</text>
          </view>
          <view class="step-content">
            <view class="step-header">
              <text class="approver-name">{{item.approver_name}}</text>
              <text class="step-time" wx:if="{{item.processed_at}}">{{item.processed_at}}</text>
            </view>
            <text class="step-title">{{item.step_name}}</text>
            <text class="step-comment" wx:if="{{item.comment}}">{{item.comment}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons" wx:if="{{purchaseDetail.status === 'draft'}}">
      <button class="action-btn edit-btn" bindtap="onEdit">
        编辑申请
      </button>
      <button class="action-btn submit-btn" bindtap="onSubmit">
        提交申请
      </button>
    </view>
  </view>
</view>