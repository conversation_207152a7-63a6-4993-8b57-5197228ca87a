/* pages/workspace/purchase/detail/detail.wxss */
.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 32rpx;
}

/* ==================== 加载状态 ==================== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #52c41a;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* ==================== 信息卡片 ==================== */
.info-card {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-section {
  flex: 1;
  margin-right: 16rpx;
}

.purchase-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: block;
}

.status-tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.purchase-amount {
  font-size: 40rpx;
  font-weight: 700;
  color: #52c41a;
  text-align: right;
}

.card-content {
  padding: 32rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-row {
  display: flex;
  margin-bottom: 24rpx;
  align-items: flex-start;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
  font-weight: 500;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 1.5;
}

.value.description {
  background: #f9f9f9;
  padding: 16rpx;
  border-radius: 8rpx;
  line-height: 1.6;
}

/* ==================== 状态样式 ==================== */
.status-draft {
  background: #f0f0f0;
  color: #666;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-processing {
  background: #e6f7ff;
  color: #1890ff;
}

.status-approved {
  background: #f6ffed;
  color: #52c41a;
}

.status-rejected {
  background: #fff2f0;
  color: #ff4d4f;
}

.status-cancelled {
  background: #f0f0f0;
  color: #999;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
}

/* ==================== 采购项目明细 ==================== */
.purchase-items {
  padding: 0 32rpx 32rpx;
}

.purchase-item {
  padding: 24rpx;
  border: 1rpx solid #f0f0f0;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  background: #fafafa;
}

.purchase-item:last-child {
  margin-bottom: 0;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.item-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.item-subtotal {
  font-size: 32rpx;
  font-weight: 700;
  color: #52c41a;
}

.item-details {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.item-spec,
.item-quantity,
.item-price {
  font-size: 26rpx;
  color: #666;
  background: #fff;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
}

.item-remark {
  font-size: 24rpx;
  color: #999;
  font-style: italic;
}

/* ==================== 附件信息 ==================== */
.attachments {
  padding: 0 32rpx 32rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  border: 1rpx solid #f0f0f0;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
  background: #fafafa;
}

.attachment-item:last-child {
  margin-bottom: 0;
}

.attachment-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.attachment-name {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.attachment-size {
  font-size: 24rpx;
  color: #999;
}

/* ==================== 审批流程 ==================== */
.approval-flow {
  padding: 0 32rpx 32rpx;
}

.approval-step {
  display: flex;
  margin-bottom: 32rpx;
}

.approval-step:last-child {
  margin-bottom: 0;
}

.step-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.step-icon.approved {
  background: #f6ffed;
  color: #52c41a;
  border: 2rpx solid #52c41a;
}

.step-icon.rejected {
  background: #fff2f0;
  color: #ff4d4f;
  border: 2rpx solid #ff4d4f;
}

.step-icon.pending {
  background: #fff7e6;
  color: #fa8c16;
  border: 2rpx solid #fa8c16;
}

.step-content {
  flex: 1;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.approver-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.step-time {
  font-size: 24rpx;
  color: #999;
}

.step-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

.step-comment {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
  background: #f9f9f9;
  padding: 12rpx;
  border-radius: 6rpx;
  display: block;
}

/* ==================== 操作按钮 ==================== */
.action-buttons {
  display: flex;
  gap: 24rpx;
  margin-top: 32rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  border: none;
}

.edit-btn {
  background: #fff;
  color: #52c41a;
  border: 2rpx solid #52c41a;
}

.edit-btn:active {
  background: #f6ffed;
}

.submit-btn {
  background: linear-gradient(135deg, #52c41a, #389e0d);
  color: #fff;
}

.submit-btn:active {
  background: linear-gradient(135deg, #389e0d, #237804);
}
