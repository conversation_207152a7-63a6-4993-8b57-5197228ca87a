// pages/workspace/purchase/apply/apply.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: false,
    isEdit: false,
    purchaseId: null,
    
    // 表单数据
    formData: {
      title: '',
      description: '',
      category: '',
      amount: '',
      urgencyLevel: 'normal', // low, normal, high, urgent
      expectedDate: '',
      supplier: '',
      supplierContact: ''
    },

    // 采购类别选项 - 养殖相关物料
    categoryOptions: [
      { value: 'feed', label: '🌾 饲料采购', description: '各类动物饲料、营养添加剂、饲料原料等' },
      { value: 'medicine', label: '💊 兽药疫苗', description: '动物疫苗、兽药、保健品、消毒用品等' },
      { value: 'equipment', label: '🔧 养殖设备', description: '饲养设备、自动化设备、监控设备等' },
      { value: 'facility', label: '🏗️ 基础设施', description: '栏舍建设、围栏、水电设施、通风设备等' },
      { value: 'breeding', label: '🐄 种畜种禽', description: '种牛、种猪、种鸡、种鸭等繁殖用动物' },
      { value: 'bedding', label: '🛏️ 垫料用品', description: '稻草、木屑、垫料、清洁用品等' },
      { value: 'tools', label: '🛠️ 工具器械', description: '养殖工具、检测仪器、维修工具等' },
      { value: 'transport', label: '🚛 运输工具', description: '运输车辆、运输笼具、物流设备等' },
      { value: 'safety', label: '🦺 安全防护', description: '防护服、消毒设备、安全标识等' },
      { value: 'office', label: '📝 办公用品', description: '记录本、标签、办公设备等管理用品' },
      { value: 'other', label: '📋 其他物料', description: '其他未分类的养殖相关物料' }
    ],

    // 选中的类别索引
    selectedCategoryIndex: -1,
    // 选中类别的显示文本
    selectedCategoryLabel: '',
    // 是否已选择类别
    hasSelectedCategory: false,

    // 紧急程度选项
    urgencyOptions: [
      { value: 'low', label: '低' },
      { value: 'normal', label: '普通' },
      { value: 'high', label: '高' },
      { value: 'urgent', label: '紧急' }
    ],

    // 紧急程度选择器的索引和标签
    urgencySelectedIndex: 1, // 默认选择"普通"
    urgencySelectedLabel: '普通',

    // 格式化的日期显示
    formattedDate: '',
    // 是否已选择日期
    hasExpectedDate: false,

    // 日期范围限制
    minDate: '',
    maxDate: '',
    
    // 表单验证错误
    errors: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 初始化日期范围
    this.initDateRange();

    if (options.id) {
      this.setData({
        isEdit: true,
        purchaseId: options.id
      });
      this.loadPurchaseDetail(options.id);
    } else {
      // 设置默认的期望日期为一周后
      const expectedDate = new Date();
      expectedDate.setDate(expectedDate.getDate() + 7);
      const formattedDate = this.formatDateForDisplay(expectedDate);

      console.log('设置默认日期:', {
        expectedDate: this.formatDateForPicker(expectedDate),
        formattedDate: formattedDate
      });

      this.setData({
        'formData.expectedDate': this.formatDateForPicker(expectedDate),
        formattedDate: formattedDate,
        hasExpectedDate: true
      });
    }
  },

  /**
   * 初始化日期范围
   */
  initDateRange() {
    const today = new Date();
    // 设置最小日期为今天
    const minDate = new Date(today);
    // 设置最大日期为两年后，给用户更多选择空间
    const maxDate = new Date(today);
    maxDate.setFullYear(today.getFullYear() + 2);

    const minDateStr = this.formatDateForPicker(minDate);
    const maxDateStr = this.formatDateForPicker(maxDate);

    console.log('初始化日期范围:', {
      today: this.formatDateForPicker(today),
      minDate: minDateStr,
      maxDate: maxDateStr
    });

    this.setData({
      minDate: minDateStr,
      maxDate: maxDateStr
    });
  },

  /**
   * 加载采购申请详情
   */
  async loadPurchaseDetail(id) {
    try {
      wx.showLoading({ title: '加载中...' });
      
      const response = await request.get(`${API.API_ENDPOINTS.WORKSPACE.PURCHASE.DETAIL}/${id}`);
      
      if (response.success) {
        const data = response.data;
        const urgencyIndex = this.data.urgencyOptions.findIndex(option => option.value === data.urgencyLevel);
        const urgencyLabel = urgencyIndex >= 0 ? this.data.urgencyOptions[urgencyIndex].label : '普通';

        // 查找类别索引
        const categoryIndex = this.data.categoryOptions.findIndex(
          item => item.value === (data.category || '')
        );

        const expectedDate = data.expectedDate || '';
        this.setData({
          formData: {
            ...data,
            category: data.category || '',
            expectedDate: expectedDate
          },
          urgencySelectedIndex: urgencyIndex >= 0 ? urgencyIndex : 1,
          urgencySelectedLabel: urgencyLabel,
          selectedCategoryIndex: categoryIndex,
          selectedCategoryLabel: categoryIndex >= 0 ? this.data.categoryOptions[categoryIndex].label : '',
          formattedDate: expectedDate ? this.formatDateForDisplay(expectedDate) : '',
          hasExpectedDate: !!expectedDate,
          hasSelectedCategory: categoryIndex >= 0
        });
      } else {
        wx.showToast({
          title: response.message || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载采购申请详情失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 表单输入处理
   */
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value,
      [`errors.${field}`]: '' // 清除错误信息
    });
  },

  /**
   * 类别选择
   */
  onCategoryChange(e) {
    console.log('onCategoryChange 被调用:', e.detail);

    const index = parseInt(e.detail.value);
    const category = this.data.categoryOptions[index];

    console.log('类别选择:', { index, category, categoryOptions: this.data.categoryOptions });

    if (!category) {
      console.error('未找到对应的类别:', index);
      return;
    }

    this.setData({
      'formData.category': category.value,
      selectedCategoryIndex: index,
      selectedCategoryLabel: category.label,
      hasSelectedCategory: true,
      'errors.category': ''
    });

    console.log('类别选择完成:', {
      category: category.value,
      label: category.label,
      index: index
    });

    // 显示选择反馈
    wx.showToast({
      title: `已选择: ${category.label}`,
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 紧急程度选择
   */
  onUrgencyChange(e) {
    const index = e.detail.value;
    const selectedOption = this.data.urgencyOptions[index];
    this.setData({
      'formData.urgencyLevel': selectedOption.value,
      urgencySelectedIndex: index,
      urgencySelectedLabel: selectedOption.label
    });
  },

  /**
   * 期望日期选择
   */
  onExpectedDateChange(e) {
    console.log('onExpectedDateChange 被调用:', e.detail);

    const selectedDate = e.detail.value;
    const formatted = this.formatDateForDisplay(selectedDate);

    console.log('日期选择:', { selectedDate, formatted });

    this.setData({
      'formData.expectedDate': selectedDate,
      formattedDate: formatted,
      hasExpectedDate: !!selectedDate,
      'errors.expectedDate': ''
    });

    // 显示选择反馈
    if (selectedDate) {
      wx.showToast({
        title: `已选择: ${formatted}`,
        icon: 'success',
        duration: 1500
      });
    }
  },



  /**
   * 表单验证
   */
  validateForm() {
    const { formData } = this.data;
    const errors = {};
    
    if (!formData.title.trim()) {
      errors.title = '请输入采购标题';
    }
    
    if (!formData.description.trim()) {
      errors.description = '请输入采购说明';
    }
    
    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      errors.amount = '请输入有效的采购金额';
    }

    if (!formData.expectedDate) {
      errors.expectedDate = '请选择期望完成日期';
    }
    
    this.setData({ errors });
    return Object.keys(errors).length === 0;
  },

  /**
   * 保存草稿
   */
  async onSaveDraft() {
    if (!this.validateForm()) {
      wx.showToast({
        title: '请完善表单信息',
        icon: 'none'
      });
      return;
    }
    
    await this.submitForm('draft');
  },

  /**
   * 提交申请
   */
  async onSubmit() {
    if (!this.validateForm()) {
      wx.showToast({
        title: '请完善表单信息',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '确认提交',
      content: '提交后将无法修改，确定要提交采购申请吗？',
      success: async (res) => {
        if (res.confirm) {
          await this.submitForm('pending');
        }
      }
    });
  },

  /**
   * 提交表单
   */
  async submitForm(status) {
    try {
      this.setData({ loading: true });
      wx.showLoading({ title: '提交中...' });
      
      const submitData = {
        ...this.data.formData,
        status
      };
      
      let response;
      if (this.data.isEdit) {
        response = await request.put(`${API.API_ENDPOINTS.WORKSPACE.PURCHASE.UPDATE}/${this.data.purchaseId}`, submitData);
      } else {
        response = await request.post(API.API_ENDPOINTS.WORKSPACE.PURCHASE.CREATE, submitData);
      }
      
      if (response.success) {
        wx.showToast({
          title: status === 'draft' ? '保存成功' : '提交成功',
          icon: 'success'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: response.message || '操作失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('提交采购申请失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
      wx.hideLoading();
    }
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    if (!date) return '';
    const d = new Date(date);
    return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
  },

  /**
   * 格式化日期显示
   */
  formatDateForDisplay(dateStr) {
    if (!dateStr) return '';

    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}年${month}月${day}日`;
  },

  /**
   * 格式化日期为选择器格式
   */
  formatDateForPicker(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }
});
