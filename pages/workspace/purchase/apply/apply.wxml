<!--pages/workspace/purchase/apply/apply.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 表单内容 -->
  <view wx:else class="form-content">
    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>

      <!-- 采购标题 -->
      <view class="form-item">
        <view class="item-label">
          <text class="label-text">采购标题</text>
          <text class="required">*</text>
        </view>
        <input
          class="form-input {{errors.title ? 'error' : ''}}"
          placeholder="请输入采购标题"
          value="{{formData.title}}"
          data-field="title"
          bindinput="onInputChange"
        />
        <text wx:if="{{errors.title}}" class="error-text">{{errors.title}}</text>
      </view>

      <!-- 采购类别 -->
      <view class="form-item">
        <view class="item-label">
          <text class="label-text">采购类别</text>
          <text class="required">*</text>
        </view>
        <picker
          class="form-picker {{errors.category ? 'error' : ''}}"
          range="{{categoryOptions}}"
          range-key="label"
          value="{{selectedCategoryIndex}}"
          bindchange="onCategoryChange"
        >
          <view class="picker-content">
            <text wx:if="{{selectedCategoryLabel}}" class="picker-text selected">{{selectedCategoryLabel}}</text>
            <text wx:else class="picker-text placeholder">请选择采购类别</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
        <text wx:if="{{errors.category}}" class="error-text">{{errors.category}}</text>
      </view>

      <!-- 采购说明 -->
      <view class="form-item">
        <view class="item-label">
          <text class="label-text">采购说明</text>
          <text class="required">*</text>
        </view>
        <textarea
          class="form-textarea {{errors.description ? 'error' : ''}}"
          placeholder="请详细描述采购需求、用途等信息"
          value="{{formData.description}}"
          data-field="description"
          bindinput="onInputChange"
          maxlength="500"
          auto-height
        />
        <text wx:if="{{errors.description}}" class="error-text">{{errors.description}}</text>
      </view>

      <!-- 期望完成日期 -->
      <view class="form-item">
        <view class="item-label">
          <text class="label-text">期望完成日期</text>
          <text class="required">*</text>
        </view>
        <picker
          class="form-picker"
          mode="date"
          value="{{formData.expectedDate}}"
          bindchange="onExpectedDateChange"
        >
          <view class="picker-content">
            <text wx:if="{{formattedDate}}" class="picker-text selected">{{formattedDate}}</text>
            <text wx:else class="picker-text placeholder">请选择日期</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
        <text wx:if="{{errors.expectedDate}}" class="error-text">{{errors.expectedDate}}</text>
      </view>

      <!-- 供应商信息 -->
      <view class="form-item">
        <view class="item-label">
          <text class="label-text">供应商信息</text>
        </view>
        <input
          class="form-input"
          placeholder="供应商名称（可选）"
          value="{{formData.supplier}}"
          data-field="supplier"
          bindinput="onInputChange"
        />
      </view>
    </view>

    <!-- 采购金额 -->
    <view class="form-section">
      <view class="form-item">
        <view class="item-label">
          <text class="label-text">采购金额</text>
          <text class="required">*</text>
        </view>
        <input
          class="form-input {{errors.amount ? 'error' : ''}}"
          type="digit"
          placeholder="请输入采购金额"
          value="{{formData.amount}}"
          data-field="amount"
          bindinput="onInputChange"
        />
        <text wx:if="{{errors.amount}}" class="error-text">{{errors.amount}}</text>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-buttons">
      <button
        class="submit-btn draft-btn"
        bindtap="onSaveDraft"
        disabled="{{submitting}}"
      >
        保存草稿
      </button>
      <button
        class="submit-btn primary-btn"
        bindtap="onSubmit"
        disabled="{{submitting}}"
      >
        {{submitting ? '提交中...' : '提交申请'}}
      </button>
    </view>
  </view>
</view>
