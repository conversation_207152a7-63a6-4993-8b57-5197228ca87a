// pages/workspace/purchase/list/list.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    refreshing: false,
    
    // 采购申请列表
    purchaseList: [],
    
    // 分页信息
    pagination: {
      page: 1,
      limit: 20,
      total: 0,
      hasMore: true
    },
    
    // 筛选条件
    filters: {
      status: 'all', // all, draft, pending, approved, rejected, cancelled
      dateRange: 'all' // all, week, month, quarter
    },
    
    // 状态映射
    statusMap: {
      'draft': '草稿',
      'pending': '待审批',
      'processing': '审批中',
      'approved': '已批准',
      'rejected': '已拒绝',
      'cancelled': '已取消',
      'completed': '已完成'
    },
    
    // 状态样式映射
    statusClassMap: {
      'draft': 'status-draft',
      'pending': 'status-pending',
      'processing': 'status-processing',
      'approved': 'status-approved',
      'rejected': 'status-rejected',
      'cancelled': 'status-cancelled',
      'completed': 'status-completed'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadPurchaseList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 刷新列表数据
    this.refreshData();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.loadMoreData();
  },

  /**
   * 加载采购申请列表
   */
  async loadPurchaseList(isLoadMore = false) {
    try {
      if (!isLoadMore) {
        this.setData({ loading: true });
      }

      const { page, limit } = this.data.pagination;
      const { status, dateRange } = this.data.filters;
      
      const params = {
        page: isLoadMore ? page + 1 : 1,
        limit,
        status: status !== 'all' ? status : undefined,
        dateRange: dateRange !== 'all' ? dateRange : undefined
      };

      const response = await request.get(API.API_ENDPOINTS.WORKSPACE.PURCHASE.LIST, params);
      
      if (response.success) {
        const { list, total, page: currentPage, hasMore } = response.data;
        
        this.setData({
          purchaseList: isLoadMore ? [...this.data.purchaseList, ...list] : list,
          pagination: {
            ...this.data.pagination,
            page: currentPage,
            total,
            hasMore
          }
        });
      } else {
        wx.showToast({
          title: response.message || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载采购申请列表失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false, refreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({ 
      refreshing: true,
      pagination: { ...this.data.pagination, page: 1 }
    });
    await this.loadPurchaseList();
  },

  /**
   * 加载更多数据
   */
  async loadMoreData() {
    if (!this.data.pagination.hasMore || this.data.loading) {
      return;
    }
    await this.loadPurchaseList(true);
  },

  /**
   * 状态筛选
   */
  onStatusFilter(e) {
    const { status } = e.currentTarget.dataset;
    this.setData({
      'filters.status': status,
      pagination: { ...this.data.pagination, page: 1 }
    });
    this.loadPurchaseList();
  },

  /**
   * 日期范围筛选
   */
  onDateRangeFilter(e) {
    const { range } = e.currentTarget.dataset;
    this.setData({
      'filters.dateRange': range,
      pagination: { ...this.data.pagination, page: 1 }
    });
    this.loadPurchaseList();
  },

  /**
   * 查看详情
   */
  onViewDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/workspace/purchase/detail/detail?id=${id}`
    });
  },

  /**
   * 新建采购申请
   */
  onCreatePurchase() {
    wx.navigateTo({
      url: '/pages/workspace/purchase/apply/apply'
    });
  },

  /**
   * 编辑采购申请
   */
  onEditPurchase(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/workspace/purchase/apply/apply?id=${id}`
    });
  },

  /**
   * 删除采购申请
   */
  onDeletePurchase(e) {
    const { id, title } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除采购申请"${title}"吗？`,
      confirmText: '删除',
      confirmColor: '#ff4d4f',
      success: async (res) => {
        if (res.confirm) {
          await this.deletePurchase(id);
        }
      }
    });
  },

  /**
   * 执行删除操作
   */
  async deletePurchase(id) {
    try {
      wx.showLoading({ title: '删除中...' });
      
      const response = await request.delete(`${API.API_ENDPOINTS.WORKSPACE.PURCHASE.DELETE}/${id}`);
      
      if (response.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        this.refreshData();
      } else {
        wx.showToast({
          title: response.message || '删除失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('删除采购申请失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 格式化金额
   */
  formatAmount(amount) {
    return parseFloat(amount || 0).toFixed(2);
  },

  /**
   * 格式化日期
   */
  formatDate(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  }
});
