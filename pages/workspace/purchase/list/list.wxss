/* pages/workspace/purchase/list/list.wxss */
.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 0 32rpx;
}

/* ==================== 头部标题 ==================== */
.header {
  background: #fff;
  padding: 40rpx 32rpx 32rpx;
  margin: 0 -32rpx 32rpx;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

/* ==================== 新建按钮 ==================== */
.create-btn-container {
  margin-bottom: 32rpx;
}

.create-btn {
  width: 100%;
  background: linear-gradient(135deg, #52c41a, #389e0d);
  color: #fff;
  border: none;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
}

.create-btn:active {
  background: linear-gradient(135deg, #389e0d, #237804);
  transform: translateY(1rpx);
}

.create-btn-text {
  font-size: 32rpx;
}

/* ==================== 空状态 ==================== */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

/* ==================== 加载状态 ==================== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #52c41a;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 24rpx;
  font-size: 28rpx;
  color: #999;
}

/* ==================== 采购申请列表 ==================== */
.purchase-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.purchase-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.purchase-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.item-title {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.purchase-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.status-tag {
  display: inline-block;
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  align-self: flex-start;
}

.item-amount {
  font-size: 36rpx;
  font-weight: 700;
  color: #52c41a;
  text-align: right;
}

.status-draft {
  background: #f0f0f0;
  color: #666;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-processing {
  background: #e6f7ff;
  color: #1890ff;
}

.status-approved {
  background: #f6ffed;
  color: #52c41a;
}

.status-rejected {
  background: #fff2f0;
  color: #ff4d4f;
}

.status-cancelled {
  background: #f0f0f0;
  color: #999;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
}

.item-content {
  margin-bottom: 24rpx;
}

.item-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

.apply-time {
  flex: 1;
}

.applicant {
  margin-left: 16rpx;
}

/* ==================== 操作按钮 ==================== */
.item-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
  margin-top: 16rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
}

.edit-btn {
  background: #e6f7ff;
  color: #1890ff;
}

.edit-btn:active {
  background: #bae7ff;
}

.delete-btn {
  background: #fff2f0;
  color: #ff4d4f;
}

.delete-btn:active {
  background: #ffccc7;
}

/* ==================== 加载更多 ==================== */
.load-more {
  text-align: center;
  padding: 40rpx 0;
  font-size: 28rpx;
  color: #999;
}