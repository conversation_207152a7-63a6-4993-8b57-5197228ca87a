/* components/permission-check/permission-check.wxss */

.permission-disabled {
  position: relative;
  opacity: 0.5;
  pointer-events: none;
}

.permission-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
}

.permission-tip {
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
}

.no-permission {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx dashed #dee2e6;
  color: #6c757d;
}

.no-permission-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.no-permission-text {
  font-size: 28rpx;
  text-align: center;
  line-height: 1.5;
}

.permission-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  color: #6c757d;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e9ecef;
  border-top: 4rpx solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #6c757d;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .no-permission {
    padding: 40rpx 20rpx;
  }
  
  .no-permission-icon {
    font-size: 60rpx;
  }
  
  .no-permission-text {
    font-size: 26rpx;
  }
}