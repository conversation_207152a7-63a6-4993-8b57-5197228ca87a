/**
 * 权限检查组件
 * Permission Check Component
 * 
 * 用于在页面中进行权限检查和条件渲染
 */

const { getCurrentUserPermissions, hasPermission, FinancePermissionChecker } = require('../../utils/role-permission');

Component({
  /**
   * 组件属性列表
   */
  properties: {
    // 必需的权限（字符串或数组）
    permission: {
      type: null, // 可以是String或Array
      value: null
    },
    
    // 是否需要所有权限（当permission为数组时）
    requireAll: {
      type: Boolean,
      value: false
    },
    
    // 权限检查模式
    mode: {
      type: String,
      value: 'show', // 'show' | 'hide' | 'disable'
      optionalTypes: [String]
    },
    
    // 无权限时的提示文本
    noPermissionText: {
      type: String,
      value: '权限不足'
    },
    
    // 是否显示无权限提示
    showNoPermissionTip: {
      type: Boolean,
      value: true
    },
    
    // 检查的资源ID（用于资源级权限控制）
    resourceId: {
      type: String,
      value: ''
    },
    
    // 资源拥有者ID（用于判断是否为资源拥有者）
    resourceOwnerId: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件数据
   */
  data: {
    hasPermission: false,
    loading: true,
    userInfo: null
  },

  /**
   * 组件方法列表
   */
  methods: {
    /**
     * 检查权限
     */
    async checkPermission() {
      try {
        this.setData({ loading: true });
        
        const userPermissions = await getCurrentUserPermissions();
        const { permission, requireAll, resourceId, resourceOwnerId } = this.properties;
        
        if (!permission) {
          this.setData({
            hasPermission: true,
            loading: false,
            userInfo: userPermissions
          });
          return;
        }
        
        let hasRequiredPermission = false;
        
        // 检查基础权限
        if (Array.isArray(permission)) {
          if (requireAll) {
            hasRequiredPermission = permission.every(perm => 
              hasPermission(userPermissions.role, perm)
            );
          } else {
            hasRequiredPermission = permission.some(perm => 
              hasPermission(userPermissions.role, perm)
            );
          }
        } else {
          hasRequiredPermission = hasPermission(userPermissions.role, permission);
        }
        
        // 检查资源级权限（如果提供了资源信息）
        if (hasRequiredPermission && resourceId && resourceOwnerId) {
          // 如果不是管理员，检查是否为资源拥有者
          if (!userPermissions.isAdmin) {
            const currentUserId = userPermissions.userId || wx.getStorageSync('userId');
            hasRequiredPermission = resourceOwnerId === currentUserId;
          }
        }

        this.setData({
          hasPermission: hasRequiredPermission,
          loading: false,
          userInfo: userPermissions
        });

        // 触发权限检查完成事件
        this.triggerEvent('permissionChecked', {
          hasPermission: hasRequiredPermission,
          userInfo: userPermissions
        });

      } catch (error) {
        console.error('权限检查失败:', error);
        this.setData({
          hasPermission: false,
          loading: false
        });
      }
    },

    /**
     * 处理无权限点击（静默处理）
     */
    onNoPermissionTap() {
      // 静默处理，不显示提示
      console.warn('权限不足:', this.properties.noPermissionText);

      this.triggerEvent('noPermission', {
        permission: this.properties.permission,
        userInfo: this.data.userInfo
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    /**
     * 组件实例被创建时执行
     */
    created() {
      // 组件实例被创建
    },

    /**
     * 组件实例进入页面节点树时执行
     */
    attached() {
      this.checkPermission();
    },

    /**
     * 组件实例被移动到节点树另一个位置时执行
     */
    moved() {
      // 组件实例被移动到节点树另一个位置
    },

    /**
     * 组件实例从页面节点树移除时执行
     */
    detached() {
      // 组件实例从页面节点树移除
    }
  },

  /**
   * 组件所在页面生命周期
   */
  pageLifetimes: {
    /**
     * 组件所在页面被显示时执行
     */
    show() {
      // 页面显示时重新检查权限
      this.checkPermission();
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'permission, requireAll, resourceId, resourceOwnerId': function(permission, requireAll, resourceId, resourceOwnerId) {
      // 当权限相关属性变化时重新检查权限
      if (this.data.loading === false) {
        this.checkPermission();
      }
    }
  }
});