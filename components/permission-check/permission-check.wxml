<!--components/permission-check/permission-check.wxml-->
<block wx:if="{{!loading}}">
  <!-- 有权限时显示内容 -->
  <block wx:if="{{hasPermission && mode === 'show'}}">
    <slot></slot>
  </block>
  
  <!-- 无权限时隐藏内容（mode='hide'时） -->
  <block wx:elif="{{!hasPermission && mode === 'hide'}}">
    <!-- 不显示任何内容 -->
  </block>
  
  <!-- 无权限时禁用内容（mode='disable'时） -->
  <view wx:elif="{{!hasPermission && mode === 'disable'}}" 
        class="permission-disabled" 
        bind:tap="onNoPermissionTap">
    <slot></slot>
    <view wx:if="{{showNoPermissionTip}}" class="permission-mask">
      <view class="permission-tip">{{noPermissionText}}</view>
    </view>
  </view>
  
  <!-- 默认模式：有权限显示，无权限显示提示 -->
  <block wx:else>
    <block wx:if="{{hasPermission}}">
      <slot></slot>
    </block>
    <view wx:else class="no-permission" bind:tap="onNoPermissionTap">
      <view class="no-permission-icon">🔒</view>
      <view class="no-permission-text">{{noPermissionText}}</view>
    </view>
  </block>
</block>

<!-- 加载状态 -->
<view wx:else class="permission-loading">
  <view class="loading-icon"></view>
  <view class="loading-text">权限检查中...</view>
</view>