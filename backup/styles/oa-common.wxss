/* OA模块公共样式 */
@import './oa-loading.wxss';

/* ==================== 通用布局 ==================== */
/* .oa-container 样式已移至各页面文件中以支持自定义设计 */

.oa-section {
  margin: var(--space-md);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.oa-section-header {
  padding: var(--space-lg);
  border-bottom: 1rpx solid var(--border-light);
}

.oa-section-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.oa-section-content {
  padding: var(--space-lg);
}

/* ==================== 通用组件 ==================== */
.oa-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin-bottom: var(--space-md);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.oa-button {
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--space-sm) var(--space-lg);
  font-size: var(--text-base);
  font-weight: 500;
  transition: all 0.3s ease;
}

.oa-button:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 102, 204, 0.3);
}

.oa-button--secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1rpx solid var(--border-light);
}

.oa-button--success {
  background: var(--success);
}

.oa-button--warning {
  background: var(--warning);
}

.oa-button--error {
  background: var(--error);
}

/* ==================== 通用列表 ==================== */
.oa-list {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.oa-list-item {
  display: flex;
  align-items: center;
  padding: var(--space-lg);
  border-bottom: 1rpx solid var(--border-light);
  transition: background-color 0.3s ease;
}

.oa-list-item:last-child {
  border-bottom: none;
}

.oa-list-item:active {
  background: var(--bg-secondary);
}

.oa-list-item-content {
  flex: 1;
  margin-right: var(--space-md);
}

.oa-list-item-title {
  font-size: var(--text-base);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.oa-list-item-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.oa-list-item-right {
  color: var(--text-tertiary);
  font-size: var(--text-sm);
}

/* ==================== 通用状态 ==================== */
.oa-status {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
  text-align: center;
}

.oa-status--pending {
  background: var(--warning-light);
  color: var(--warning);
}

.oa-status--approved {
  background: var(--success-light);
  color: var(--success);
}

.oa-status--rejected {
  background: var(--error-light);
  color: var(--error);
}

.oa-status--processing {
  background: var(--info-light);
  color: var(--info);
}

/* ==================== 通用空状态 ==================== */
.oa-empty {
  text-align: center;
  padding: var(--space-4xl) var(--space-lg);
  color: var(--text-secondary);
}

.oa-empty-icon {
  font-size: 120rpx;
  margin-bottom: var(--space-lg);
  opacity: 0.5;
}

.oa-empty-title {
  font-size: var(--text-lg);
  font-weight: 500;
  margin-bottom: var(--space-sm);
  color: var(--text-primary);
}

.oa-empty-desc {
  font-size: var(--text-base);
  color: var(--text-secondary);
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 750rpx) {
  .oa-section {
    margin: var(--space-sm);
  }

  .oa-section-header,
  .oa-section-content {
    padding: var(--space-md);
  }

  .oa-card {
    padding: var(--space-md);
  }

  .oa-list-item {
    padding: var(--space-md);
  }
}