/**
 * OA分包 - RBAC权限映射与校验
 * - 将前端角色代码(admin/manager/finance/user)映射到后端权限点集合
 * - 提供 hasPermission / hasAny / hasAll API 供页面与组件使用
 */

// 与后端保持一致的权限点定义（子集，前端用于UI权限栅格）
const PERMISSIONS = {
  PLATFORM: {
    TENANT_MANAGE: 'platform:tenant:manage',
    ANALYTICS: 'platform:analytics:view'
  },
  OA: {
    ACCESS: 'oa:access',
    // 请假管理
    LEAVE_APPLY: 'oa:leave:apply',
    LEAVE_VIEW_ALL: 'oa:leave:view:all',
    LEAVE_VIEW_OWN: 'oa:leave:view:own',
    // 采购管理
    PURCHASE_APPLY: 'oa:purchase:apply',
    PURCHASE_VIEW_ALL: 'oa:purchase:view:all',
    PURCHASE_VIEW_OWN: 'oa:purchase:view:own',
    PURCHASE_APPROVE: 'oa:purchase:approve',
    // 财务管理
    FINANCE_VIEW: 'oa:finance:view',
    FINANCE_MANAGE: 'oa:finance:manage',
    REIMBURSEMENT_APPLY: 'oa:reimbursement:apply',
    REIMBURSEMENT_VIEW_ALL: 'oa:reimbursement:view:all',
    REIMBURSEMENT_VIEW_OWN: 'oa:reimbursement:view:own',
    REIMBURSEMENT_APPROVE: 'oa:reimbursement:approve',
    // 审批管理
    APPROVAL_PROCESS: 'oa:approval:process',
    APPROVAL_FINANCE: 'oa:approval:finance',
    // 员工管理
    EMPLOYEE_MANAGE: 'oa:employee:manage',
    WORKFLOW_CONFIG: 'oa:workflow:config'
  },
  PRODUCTION: {
    VIEW: 'production:view',
    MANAGE: 'production:manage',
    RECORDS_EDIT: 'production:records:edit',
    INVENTORY_MANAGE: 'production:inventory:manage'
  },
  SHOP: {
    VIEW: 'shop:view',
    MANAGE: 'shop:manage',
    ORDER_PROCESS: 'shop:order:process',
    PRODUCT_MANAGE: 'shop:product:manage'
  },
  USER: {
    VIEW: 'user:view',
    MANAGE: 'user:manage'
  }
};

// 将前端角色代码映射到后端权限组（最小可用集合）
const ROLE_TO_PERMISSIONS = {
  admin: [
    ...Object.values(PERMISSIONS.OA),
    ...Object.values(PERMISSIONS.PRODUCTION),
    ...Object.values(PERMISSIONS.SHOP),
    ...Object.values(PERMISSIONS.USER),
    PERMISSIONS.PLATFORM.TENANT_MANAGE,
    PERMISSIONS.PLATFORM.ANALYTICS
  ],
  manager: [
    PERMISSIONS.OA.ACCESS,
    // 请假管理 - 只能查看所有，不能申请
    PERMISSIONS.OA.LEAVE_VIEW_ALL,
    // 采购管理 - 只能查看所有和审批，不能申请
    PERMISSIONS.OA.PURCHASE_VIEW_ALL,
    PERMISSIONS.OA.PURCHASE_APPROVE,
    // 财务管理 - 需要申请报销，能查看所有，能审批
    PERMISSIONS.OA.FINANCE_VIEW,
    PERMISSIONS.OA.REIMBURSEMENT_APPLY,
    PERMISSIONS.OA.REIMBURSEMENT_VIEW_ALL,
    PERMISSIONS.OA.REIMBURSEMENT_APPROVE,
    // 审批管理 - 完整权限
    PERMISSIONS.OA.APPROVAL_PROCESS,
    // 员工管理
    PERMISSIONS.OA.EMPLOYEE_MANAGE,
    PERMISSIONS.OA.WORKFLOW_CONFIG,
    PERMISSIONS.PRODUCTION.VIEW,
    PERMISSIONS.PRODUCTION.MANAGE,
    PERMISSIONS.SHOP.VIEW,
    PERMISSIONS.USER.VIEW
  ],
  finance: [
    PERMISSIONS.OA.ACCESS,
    // 请假管理 - 需要申请，只能查看自己的
    PERMISSIONS.OA.LEAVE_APPLY,
    PERMISSIONS.OA.LEAVE_VIEW_OWN,
    // 采购管理 - 需要申请，只能查看自己的
    PERMISSIONS.OA.PURCHASE_APPLY,
    PERMISSIONS.OA.PURCHASE_VIEW_OWN,
    // 财务管理 - 最高权限
    PERMISSIONS.OA.FINANCE_VIEW,
    PERMISSIONS.OA.FINANCE_MANAGE,
    PERMISSIONS.OA.REIMBURSEMENT_APPLY,
    PERMISSIONS.OA.REIMBURSEMENT_VIEW_ALL,
    PERMISSIONS.OA.REIMBURSEMENT_APPROVE,
    // 审批管理 - 只有财务相关审批权限
    PERMISSIONS.OA.APPROVAL_FINANCE
  ],
  user: [
    PERMISSIONS.OA.ACCESS,
    // 请假管理 - 需要申请，只能查看自己的
    PERMISSIONS.OA.LEAVE_APPLY,
    PERMISSIONS.OA.LEAVE_VIEW_OWN,
    // 采购管理 - 需要申请，只能查看自己的
    PERMISSIONS.OA.PURCHASE_APPLY,
    PERMISSIONS.OA.PURCHASE_VIEW_OWN,
    // 财务管理 - 需要申请报销，只能查看自己的
    PERMISSIONS.OA.REIMBURSEMENT_APPLY,
    PERMISSIONS.OA.REIMBURSEMENT_VIEW_OWN,
    PERMISSIONS.PRODUCTION.VIEW,
    PERMISSIONS.SHOP.VIEW
  ]
};

function getUserPermissions(userInfo) {
  const roleCode = userInfo?.roleCode || 'user';
  return ROLE_TO_PERMISSIONS[roleCode] || [];
}

function hasPermission(userInfo, permission) {
  const permissions = getUserPermissions(userInfo);
  return permissions.includes(permission);
}

function hasAll(userInfo, requiredPermissions) {
  const permissions = getUserPermissions(userInfo);
  return requiredPermissions.every(p => permissions.includes(p));
}

function hasAny(userInfo, requiredPermissions) {
  const permissions = getUserPermissions(userInfo);
  return requiredPermissions.some(p => permissions.includes(p));
}

module.exports = {
  PERMISSIONS,
  ROLE_TO_PERMISSIONS,
  getUserPermissions,
  hasPermission,
  hasAll,
  hasAny
};
