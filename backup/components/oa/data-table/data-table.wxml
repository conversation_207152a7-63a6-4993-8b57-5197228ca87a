<!-- components/oa/data-table/data-table.wxml -->

<view class="data-table {{bordered ? 'bordered' : ''}} {{striped ? 'striped' : ''}}">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="table-loading">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 表格内容 -->
  <view wx:else class="table-container">
    <!-- 表头 -->
    <view class="table-header">
      <view class="table-row header-row">
        <!-- 选择列 -->
        <view wx:if="{{selectable}}" class="table-cell select-cell">
          <checkbox 
            checked="{{selectAll}}" 
            bindchange="onSelectAll"
            class="select-checkbox"
          />
        </view>
        
        <!-- 数据列 -->
        <view 
          wx:for="{{columns}}" 
          wx:key="key"
          class="table-cell header-cell {{item.sortable ? 'sortable' : ''}}"
          style="{{item.width ? 'width: ' + item.width : ''}}"
          data-column="{{item}}"
          bindtap="onHeaderTap"
        >
          <text class="header-text">{{item.title}}</text>
          <text wx:if="{{item.sortable}}" class="sort-icon">{{getSortIcon(item)}}</text>
        </view>
      </view>
    </view>

    <!-- 表格主体 -->
    <view class="table-body">
      <!-- 空数据状态 -->
      <view wx:if="{{data.length === 0}}" class="empty-state">
        <text class="empty-text">{{emptyText}}</text>
      </view>

      <!-- 数据行 -->
      <view 
        wx:else
        wx:for="{{data}}" 
        wx:key="index"
        class="table-row data-row {{isRowSelected(item) ? 'selected' : ''}}"
        data-index="{{index}}"
        bindtap="onRowTap"
      >
        <!-- 选择列 -->
        <view wx:if="{{selectable}}" class="table-cell select-cell">
          <checkbox 
            checked="{{isRowSelected(item)}}" 
            data-index="{{index}}"
            bindchange="onRowSelect"
            class="select-checkbox"
            catchtap="stopPropagation"
          />
        </view>
        
        <!-- 数据列 -->
        <view 
          wx:for="{{columns}}" 
          wx:for-item="column"
          wx:key="key"
          class="table-cell data-cell"
          style="{{column.width ? 'width: ' + column.width : ''}}"
        >
          <!-- 普通文本 -->
          <text 
            wx:if="{{!column.type || column.type === 'text'}}"
            class="cell-text"
          >
            {{getCellValue(item, column)}}
          </text>
          
          <!-- 状态标签 -->
          <view 
            wx:elif="{{column.type === 'status'}}"
            class="cell-status"
            style="{{getStatusStyle(item[column.key], column.statusMap)}}"
          >
            {{formatCellData(item[column.key], column)}}
          </view>
          
          <!-- 货币格式 -->
          <text 
            wx:elif="{{column.type === 'currency'}}"
            class="cell-currency"
          >
            {{formatCellData(item[column.key], column)}}
          </text>
          
          <!-- 日期格式 -->
          <text 
            wx:elif="{{column.type === 'date' || column.type === 'datetime'}}"
            class="cell-date"
          >
            {{formatCellData(item[column.key], column)}}
          </text>
          
          <!-- 自定义渲染 -->
          <view wx:elif="{{column.render}}" class="cell-custom">
            <slot name="{{column.key}}" row="{{item}}" column="{{column}}"></slot>
          </view>
        </view>
      </view>
    </view>

    <!-- 分页器 -->
    <view wx:if="{{showPagination && pagination.total > 0}}" class="table-pagination">
      <view class="pagination-info">
        <text>共 {{pagination.total}} 条记录</text>
      </view>
      <view class="pagination-controls">
        <button 
          class="pagination-btn"
          disabled="{{pagination.page <= 1}}"
          bindtap="onPageChange"
          data-page="{{pagination.page - 1}}"
        >
          上一页
        </button>
        <text class="page-info">{{pagination.page}} / {{Math.ceil(pagination.total / pagination.limit)}}</text>
        <button 
          class="pagination-btn"
          disabled="{{pagination.page >= Math.ceil(pagination.total / pagination.limit)}}"
          bindtap="onPageChange"
          data-page="{{pagination.page + 1}}"
        >
          下一页
        </button>
      </view>
    </view>
  </view>
</view>
