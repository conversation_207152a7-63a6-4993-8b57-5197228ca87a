/* components/oa/data-card/data-card.wxss */

.data-card {
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

.data-card.bordered {
  border: 1rpx solid #E5E5E7;
}

.data-card.shadow {
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.data-card.clickable {
  cursor: pointer;
}

.data-card.clickable:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 尺寸变体 */
.data-card.small {
  padding: 24rpx;
}

.data-card.medium {
  padding: 32rpx;
}

.data-card.large {
  padding: 40rpx;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #E5E5E7;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 卡片内容 */
.card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 头部区域 */
.card-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.card-icon {
  margin-right: 16rpx;
  font-size: 32rpx;
  line-height: 1;
}

.card-title-area {
  flex: 1;
}

.card-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #1D1D1F;
  line-height: 1.4;
}

.card-subtitle {
  display: block;
  font-size: 24rpx;
  color: #8E8E93;
  margin-top: 8rpx;
  line-height: 1.3;
}

/* 数值区域 */
.card-value-area {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

.main-value {
  display: flex;
  align-items: baseline;
}

.value {
  font-size: 48rpx;
  font-weight: 600;
  color: #1D1D1F;
  line-height: 1;
}

.unit {
  font-size: 24rpx;
  color: #8E8E93;
  margin-left: 8rpx;
}

/* 趋势指示器 */
.trend-indicator {
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.trend-icon {
  margin-right: 4rpx;
  font-weight: bold;
}

.trend-value {
  font-weight: 500;
}

/* 小尺寸适配 */
.data-card.small .card-title {
  font-size: 26rpx;
}

.data-card.small .card-subtitle {
  font-size: 22rpx;
}

.data-card.small .value {
  font-size: 40rpx;
}

.data-card.small .unit {
  font-size: 22rpx;
}

.data-card.small .trend-indicator {
  font-size: 22rpx;
}

/* 大尺寸适配 */
.data-card.large .card-title {
  font-size: 32rpx;
}

.data-card.large .card-subtitle {
  font-size: 26rpx;
}

.data-card.large .value {
  font-size: 56rpx;
}

.data-card.large .unit {
  font-size: 26rpx;
}

.data-card.large .trend-indicator {
  font-size: 26rpx;
}
