<!-- components/oa/status-tag/status-tag.wxml -->

<view 
  class="status-tag {{size}} {{shape}} {{plain ? 'plain' : ''}} {{disabled ? 'disabled' : ''}}"
  style="{{getTagStyle()}}"
  bindtap="onTagTap"
>
  <!-- 圆点指示器 -->
  <view wx:if="{{dot}}" class="tag-dot" style="{{getDotStyle()}}"></view>
  
  <!-- 标签文本 -->
  <text class="tag-text">{{text}}</text>
  
  <!-- 关闭按钮 -->
  <view wx:if="{{closable}}" class="tag-close" bindtap="onCloseTap">
    <text class="close-icon">×</text>
  </view>
</view>
