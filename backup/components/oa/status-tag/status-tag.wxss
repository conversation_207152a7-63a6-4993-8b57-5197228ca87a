/* components/oa/status-tag/status-tag.wxss */

.status-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid transparent;
  font-weight: 500;
  line-height: 1;
  white-space: nowrap;
  transition: all 0.2s ease;
  vertical-align: middle;
}

/* 尺寸变体 */
.status-tag.small {
  padding: 8rpx 16rpx;
  font-size: 20rpx;
  height: 40rpx;
}

.status-tag.medium {
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  height: 48rpx;
}

.status-tag.large {
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  height: 56rpx;
}

/* 形状变体 */
.status-tag.square {
  border-radius: 4rpx;
}

.status-tag.round {
  border-radius: 8rpx;
}

.status-tag.circle {
  border-radius: var(--radius-xl);
}

/* 空心样式 */
.status-tag.plain {
  background-color: transparent !important;
  border-width: 1rpx;
  border-style: solid;
}

/* 禁用状态 */
.status-tag.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 圆点指示器 */
.tag-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.status-tag.small .tag-dot {
  width: 10rpx;
  height: 10rpx;
  margin-right: 6rpx;
}

.status-tag.large .tag-dot {
  width: 14rpx;
  height: 14rpx;
  margin-right: 10rpx;
}

/* 标签文本 */
.tag-text {
  flex: 1;
  text-align: center;
}

/* 关闭按钮 */
.tag-close {
  margin-left: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.1);
  transition: background-color 0.2s ease;
}

.tag-close:active {
  background-color: rgba(0, 0, 0, 0.2);
}

.close-icon {
  font-size: 20rpx;
  line-height: 1;
  color: inherit;
}

.status-tag.small .tag-close {
  width: 20rpx;
  height: 20rpx;
  margin-left: 6rpx;
}

.status-tag.small .close-icon {
  font-size: 16rpx;
}

.status-tag.large .tag-close {
  width: 28rpx;
  height: 28rpx;
  margin-left: 10rpx;
}

.status-tag.large .close-icon {
  font-size: 24rpx;
}