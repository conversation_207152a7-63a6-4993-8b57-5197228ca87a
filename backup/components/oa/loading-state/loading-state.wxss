/* components/oa/loading-state/loading-state.wxss */

.oa-loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 64rpx;
  min-height: 300rpx;
}

.oa-loading-state.full-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  z-index: 9999;
}

.state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
  text-align: center;
}

.state-icon {
  font-size: 120rpx;
  line-height: 1;
  opacity: 0.6;
}

.state-text {
  font-size: 30rpx;
  color: #666;
  max-width: 400rpx;
  line-height: 1.6;
}

.state-content.loading .state-text {
  color: #007aff;
}

.state-content.error .state-text {
  color: #ff3b30;
}

.state-content.success .state-text {
  color: #34c759;
}

.oa-action-btn.small {
  height: 64rpx;
  padding: 0 32rpx;
  font-size: 26rpx;
  min-width: 120rpx;
}