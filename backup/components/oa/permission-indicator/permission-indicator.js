// components/oa/permission-indicator/permission-indicator.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 权限级别: admin, manager, user, guest, denied
    level: {
      type: String,
      value: 'user'
    },
    // 权限状态: granted, pending, denied
    status: {
      type: String,
      value: 'granted'
    },
    // 权限描述文本
    description: {
      type: String,
      value: ''
    },
    // 是否显示操作按钮
    showAction: {
      type: Boolean,
      value: false
    },
    // 操作类型: request, modify
    actionType: {
      type: String,
      value: 'request'
    },
    // 操作按钮文本
    actionText: {
      type: String,
      value: '申请权限'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    levelTextMap: {
      admin: '管理员',
      manager: '经理',
      user: '用户',
      guest: '访客',
      denied: '禁止访问'
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 处理操作按钮点击
     */
    onActionTap() {
      this.triggerEvent('action', {
        level: this.properties.level,
        status: this.properties.status,
        actionType: this.properties.actionType
      });
    }
  },

  /**
   * 组件的生命周期函数
   */
  lifetimes: {
    attached() {
      // 设置权限级别文本
      this.setData({
        levelText: this.data.levelTextMap[this.properties.level] || '未知'
      });
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'level': function(newLevel) {
      this.setData({
        levelText: this.data.levelTextMap[newLevel] || '未知'
      });
    }
  }
});