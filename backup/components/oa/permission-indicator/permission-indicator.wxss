/* OA权限指示器组件样式 */

.permission-indicator-container {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
}

.permission-level-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  line-height: 1;
  transition: all 0.2s ease;
}

/* 权限级别样式 */
.permission-level-badge--admin {
  background: var(--permission-admin-bg);
  color: var(--permission-admin);
  border: 1rpx solid var(--permission-admin);
}

.permission-level-badge--manager {
  background: var(--permission-manager-bg);
  color: var(--permission-manager);
  border: 1rpx solid var(--permission-manager);
}

.permission-level-badge--user {
  background: var(--permission-user-bg);
  color: var(--permission-user);
  border: 1rpx solid var(--permission-user);
}

.permission-level-badge--guest {
  background: var(--permission-guest-bg);
  color: var(--permission-guest);
  border: 1rpx solid var(--permission-guest);
}

.permission-level-badge--denied {
  background: var(--permission-denied-bg);
  color: var(--permission-denied);
  border: 1rpx solid var(--permission-denied);
}

/* 权限状态图标 */
.permission-status-icon {
  width: 12rpx;
  height: 12rpx;
  border-radius: var(--radius-xs);
  flex-shrink: 0;
}

.permission-status-icon--granted {
  background-color: var(--success);
}

.permission-status-icon--pending {
  background-color: var(--warning);
}

.permission-status-icon--denied {
  background-color: var(--error);
}

/* 权限描述文本 */
.permission-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-top: var(--space-xs);
  line-height: var(--leading-normal);
}

/* 权限操作按钮 */
.permission-action-btn {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  border: 1rpx solid transparent;
  background: transparent;
  transition: all 0.2s ease;
  cursor: pointer;
}

.permission-action-btn--request {
  color: var(--primary);
  border-color: var(--primary);
}

.permission-action-btn--request:active {
  background-color: var(--primary-subtle);
}

.permission-action-btn--modify {
  color: var(--warning);
  border-color: var(--warning);
}

.permission-action-btn--modify:active {
  background-color: var(--warning-bg);
}