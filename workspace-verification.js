#!/usr/bin/env node

/**
 * 工作台模块重构验证脚本
 * 验证OA模块到工作台模块的重构是否完成
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始验证工作台模块重构...\n');

// 验证结果统计
let passedTests = 0;
let totalTests = 0;

function test(description, condition) {
  totalTests++;
  if (condition) {
    console.log(`✅ ${description}`);
    passedTests++;
  } else {
    console.log(`❌ ${description}`);
  }
}

// 1. 验证个人中心页面配置
console.log('📱 1. 个人中心页面配置验证');
try {
  const profileJs = fs.readFileSync('pages/profile/profile.js', 'utf8');
  const profileWxml = fs.readFileSync('pages/profile/profile.wxml', 'utf8');
  
  test('个人中心使用workspaceModules而非oaModules', profileJs.includes('workspaceModules') && !profileJs.includes('oaModules:'));
  test('个人中心使用onWorkspaceTap而非onOATap', profileJs.includes('onWorkspaceTap') && !profileJs.includes('onOATap:'));
  test('个人中心WXML使用workspaceModules', profileWxml.includes('{{workspaceModules}}'));
  test('个人中心WXML使用onWorkspaceTap', profileWxml.includes('onWorkspaceTap'));
  test('个人中心不再引用OA导航工具', !profileJs.includes('oa-navigation.js'));
  
} catch (error) {
  console.log(`❌ 个人中心页面验证失败: ${error.message}`);
}

console.log('');

// 2. 验证更多页面配置
console.log('📋 2. 更多页面配置验证');
try {
  const moreWxml = fs.readFileSync('pages/more/more.wxml', 'utf8');
  
  test('更多页面指向工作台主页', moreWxml.includes('/pages/workspace/workspace'));
  test('更多页面财务管理指向工作台', moreWxml.includes('/pages/workspace/finance/overview/overview'));
  test('更多页面采购管理指向工作台', moreWxml.includes('/pages/workspace/purchase/list/list'));
  test('更多页面审批流程指向工作台', moreWxml.includes('/pages/workspace/approval/pending/pending'));
  test('更多页面不再引用OA路径', !moreWxml.includes('/pages/oa/'));
  
} catch (error) {
  console.log(`❌ 更多页面验证失败: ${error.message}`);
}

console.log('');

// 3. 验证后端快捷操作配置
console.log('🔧 3. 后端快捷操作配置验证');
try {
  const homeController = fs.readFileSync('backend/controllers/home.controller.js', 'utf8');
  

  test('后端财务报表指向工作台', homeController.includes('/pages/workspace/finance/reports/reports'));
  test('后端不再引用OA路径', !homeController.includes('/pages/oa/'));
  
} catch (error) {
  console.log(`❌ 后端快捷操作验证失败: ${error.message}`);
}

console.log('');

// 4. 验证工作台页面存在性
console.log('📁 4. 工作台页面存在性验证');

const workspacePages = [
  'pages/workspace/workspace.js',
  'pages/workspace/finance/overview/overview.js',

  'pages/workspace/approval/pending/pending.js',

  'pages/workspace/finance/reports/reports.js'
];

workspacePages.forEach(pagePath => {
  test(`${pagePath} 存在`, fs.existsSync(pagePath));
});

console.log('');

// 5. 验证OA残留清理
console.log('🧹 5. OA残留清理验证');

test('OA组件目录已删除', !fs.existsSync('components/oa'));
test('OA导航工具已删除', !fs.existsSync('utils/oa-navigation.js'));
test('OA权限工具已删除', !fs.existsSync('utils/oa-permissions.js'));
test('OA后端路由已删除', !fs.existsSync('backend/routes/oa.routes.js'));
test('OA后端控制器已删除', !fs.existsSync('backend/controllers/oa.controller.js'));
test('验证脚本已删除', !fs.existsSync('verify-oa-fixes.js'));

console.log('');

// 6. 验证app.json配置
console.log('⚙️ 6. app.json配置验证');
try {
  const appJson = JSON.parse(fs.readFileSync('app.json', 'utf8'));
  const workspaceSubPackage = appJson.subPackages.find(pkg => pkg.root === 'pages/workspace');
  
  test('app.json包含工作台分包', !!workspaceSubPackage);
  test('工作台分包包含主页面', workspaceSubPackage && workspaceSubPackage.pages.includes('workspace'));
  test('工作台分包包含财务概览', workspaceSubPackage && workspaceSubPackage.pages.includes('finance/overview/overview'));
  test('工作台分包包含采购列表', workspaceSubPackage && workspaceSubPackage.pages.includes('purchase/list/list'));
  test('工作台分包包含审批待办', workspaceSubPackage && workspaceSubPackage.pages.includes('approval/pending/pending'));
  
} catch (error) {
  console.log(`❌ app.json配置验证失败: ${error.message}`);
}

console.log('');

// 输出验证结果
console.log('📊 验证结果统计');
console.log(`通过测试: ${passedTests}/${totalTests}`);
console.log(`成功率: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 工作台模块重构验证通过！');
  console.log('✨ 所有OA模块已成功重构为工作台模块');
  console.log('🚀 用户现在可以正常访问办公管理功能');
} else {
  console.log('\n⚠️ 部分验证未通过，请检查上述失败项');
}

console.log('\n📋 使用说明:');
console.log('1. 个人中心 → 办公管理 → 各个功能模块');
console.log('2. 更多页面 → OA办公 → 各个功能入口');
console.log('3. 所有链接现在都指向工作台页面');
