/**
 * API迁移映射表
 * 用于从旧版本API平滑迁移到统一API规范
 * 
 * 使用方式：
 * 1. 在过渡期，新配置文件中包含旧API映射
 * 2. 逐步更新前端页面使用新API
 * 3. 后端保持向后兼容，同时支持新旧路径
 * 4. 完成迁移后移除旧API配置
 */

// 导入新的统一API配置
const { API_ENDPOINTS } = require('./api-unified.constants.js');

/**
 * 旧API到新API的映射关系
 * 格式：{ 旧API路径: 新API路径 }
 */
const API_MIGRATION_MAP = {
  
  // ===== 认证相关迁移 =====
  '/api/v1/auth/userinfo': API_ENDPOINTS.AUTH.PROFILE,
  '/api/v1/auth/wechat-login': API_ENDPOINTS.AUTH.WECHAT_LOGIN,
  
  // ===== 鹅群管理迁移 (V2 -> V1统一) =====
  '/api/v2/flocks': API_ENDPOINTS.FLOCKS.LIST,
  '/api/v2/flocks/batch': API_ENDPOINTS.FLOCKS.BATCH,
  
  // ===== 健康管理迁移 (V2 -> V1统一) =====
  '/api/v2/health/records': API_ENDPOINTS.HEALTH.RECORDS,
  '/api/v2/health/stats': API_ENDPOINTS.HEALTH.STATISTICS,
  '/api/v1/health/knowledge': API_ENDPOINTS.HEALTH.KNOWLEDGE,
  '/api/v1/health/ai-diagnosis': API_ENDPOINTS.HEALTH.AI_DIAGNOSIS,
  '/api/v1/health/report': API_ENDPOINTS.HEALTH.REPORTS,
  
  // ===== 生产管理迁移 (V2 -> V1统一) =====
  '/api/v2/production/records': API_ENDPOINTS.PRODUCTION.RECORDS,
  '/api/v2/production/trends': API_ENDPOINTS.PRODUCTION.TRENDS,
  '/api/v2/production/stats': API_ENDPOINTS.PRODUCTION.STATISTICS,
  '/api/v1/production/environment': API_ENDPOINTS.PRODUCTION.ENVIRONMENT,
  '/api/v1/production/finance': API_ENDPOINTS.PRODUCTION.FINANCE,

  '/api/v1/production/reimbursement-requests': API_ENDPOINTS.OA.REIMBURSEMENT.REQUESTS,
  
  // ===== 库存管理迁移 (V2 -> V1统一) =====
  '/api/v2/inventory': API_ENDPOINTS.INVENTORY.ITEMS,
  '/api/v2/inventory/categories': API_ENDPOINTS.INVENTORY.CATEGORIES,
  '/api/v2/inventory/low-stock': API_ENDPOINTS.INVENTORY.LOW_STOCK,
  '/api/v2/inventory/batch-update': API_ENDPOINTS.INVENTORY.BATCH_UPDATE,
  
  // ===== OA系统迁移 =====
  // 报销管理
  '/api/v1/oa/reimbursements': API_ENDPOINTS.OA.REIMBURSEMENT.REQUESTS,
  '/api/v1/oa/reimbursements/statistics': API_ENDPOINTS.OA.REIMBURSEMENT.STATISTICS,
  

  

  
  // 审批流程
  '/api/v1/oa/approvals/pending': API_ENDPOINTS.OA.APPROVALS.PENDING,
  '/api/v1/oa/approvals/urgent': API_ENDPOINTS.OA.APPROVALS.URGENT,
  '/api/v1/oa/approvals/history': API_ENDPOINTS.OA.APPROVALS.HISTORY,
  '/api/v1/oa/approvals/approve': API_ENDPOINTS.OA.APPROVALS.PROCESS, // 需要改为RESTful
  '/api/v1/oa/approvals/reject': API_ENDPOINTS.OA.APPROVALS.PROCESS,  // 需要改为RESTful
  
  // 财务管理
  '/api/v1/finance/overview': API_ENDPOINTS.OA.FINANCE.OVERVIEW,
  '/api/v1/oa/finance/reports': API_ENDPOINTS.OA.FINANCE.REPORTS,
  '/api/v1/oa/finance/export': API_ENDPOINTS.OA.FINANCE.EXPORT,
  '/api/v1/oa/finance/stats': API_ENDPOINTS.OA.FINANCE.STATISTICS,
  '/api/v1/oa/finance/activities': API_ENDPOINTS.OA.FINANCE.ACTIVITIES,
  '/api/v1/oa/finance/alerts': API_ENDPOINTS.OA.FINANCE.ALERTS,
  
  // 工作流管理
  '/api/v1/oa/workflows/templates': API_ENDPOINTS.OA.WORKFLOWS.TEMPLATES,
  '/api/v1/oa/workflows/templates/statistics': API_ENDPOINTS.OA.WORKFLOWS.STATISTICS,
  
  // 权限管理
  '/api/v1/oa/permissions/users': API_ENDPOINTS.OA.PERMISSIONS.USERS,
  '/api/v1/oa/permissions/users/statistics': API_ENDPOINTS.OA.PERMISSIONS.STATISTICS,
  '/api/v1/oa/permissions/roles': API_ENDPOINTS.OA.PERMISSIONS.ROLES,
  '/api/v1/oa/departments': API_ENDPOINTS.OA.PERMISSIONS.DEPARTMENTS,
  
  // ===== 租户管理迁移 (TENANT -> 统一) =====
  '/api/v1/tenant/info': API_ENDPOINTS.TENANTS.DETAIL,
  '/api/v1/tenant/config': API_ENDPOINTS.SETTINGS.SYSTEM,
  '/api/v1/tenant/switch': API_ENDPOINTS.TENANTS.LIST,
  
  // ===== 管理员API迁移 (ADMIN -> 统一) =====
  '/api/v1/admin/saas/tenants': API_ENDPOINTS.TENANTS.LIST,
  '/api/v1/admin/saas/platform-stats': API_ENDPOINTS.ADMIN.STATISTICS,
  '/api/v1/admin/saas/usage-analytics': API_ENDPOINTS.ADMIN.ANALYTICS,
  '/api/v1/admin/saas/revenue-analytics': API_ENDPOINTS.ADMIN.REVENUE,
  '/api/v1/admin/saas/system-health': API_ENDPOINTS.ADMIN.HEALTH,
  '/api/v1/admin/saas/error-logs': API_ENDPOINTS.ADMIN.LOGS,
  '/api/v1/admin/saas/subscriptions': API_ENDPOINTS.ADMIN.SUBSCRIPTIONS,
  '/api/v1/admin/saas/subscription-plans': API_ENDPOINTS.ADMIN.PLANS,
  '/api/v1/admin/saas/billing': API_ENDPOINTS.ADMIN.BILLING,
  
  // ===== 商城系统迁移 =====
  '/api/v1/shop/products': API_ENDPOINTS.SHOP.PRODUCTS,
  '/api/v1/shop/categories': API_ENDPOINTS.SHOP.CATEGORIES,
  '/api/v1/shop/cart': API_ENDPOINTS.SHOP.CART,
  '/api/v1/shop/orders': API_ENDPOINTS.SHOP.ORDERS,
  '/api/v1/shop/payment': API_ENDPOINTS.SHOP.PAYMENT,
  
  // ===== 其他系统迁移 =====
  '/api/v1/home/<USER>': API_ENDPOINTS.HOME.STATISTICS,
  '/api/v1/home/<USER>': API_ENDPOINTS.HOME.ANNOUNCEMENTS,
  '/api/v1/home/<USER>': API_ENDPOINTS.HOME.WEATHER,
  '/api/v1/home/<USER>': API_ENDPOINTS.HOME.QUICK_ACTIONS,
  
  '/api/v1/profile/settings': API_ENDPOINTS.SETTINGS.SYSTEM,
  '/api/v1/profile/help': API_ENDPOINTS.HELP.ARTICLES,
  '/api/v1/profile/feedback': API_ENDPOINTS.HELP.ARTICLES,
  
  '/api/v1/system/version': API_ENDPOINTS.SETTINGS.VERSION,
  '/api/v1/system/config': API_ENDPOINTS.SETTINGS.CONFIG
};

/**
 * 需要特殊处理的API迁移
 * 这些API在迁移过程中需要额外的处理逻辑
 */
const SPECIAL_MIGRATIONS = {
  
  // 审批操作从动词改为RESTful状态更新
  APPROVAL_ACTIONS: {
    '/api/v1/oa/approvals/approve': {
      newEndpoint: API_ENDPOINTS.OA.APPROVALS.PROCESS,
      method: 'PUT',
      bodyTransform: (oldData) => ({ ...oldData, action: 'approve' })
    },
    '/api/v1/oa/approvals/reject': {
      newEndpoint: API_ENDPOINTS.OA.APPROVALS.PROCESS,
      method: 'PUT', 
      bodyTransform: (oldData) => ({ ...oldData, action: 'reject' })
    }
  },
  
  // 报销取消操作改为状态更新
  CANCEL_OPERATIONS: {
  },
  
  // 资源详情API路径标准化
  DETAIL_ENDPOINTS: {
    // 动态ID路径的迁移需要特殊处理
    '/api/v2/flocks/{id}': API_ENDPOINTS.FLOCKS.DETAIL,
    '/api/v2/health/records/{id}': API_ENDPOINTS.HEALTH.RECORD_DETAIL,
    '/api/v2/production/records/{id}': API_ENDPOINTS.PRODUCTION.RECORD_DETAIL,
    '/api/v1/oa/reimbursements/{id}': API_ENDPOINTS.OA.REIMBURSEMENT.REQUEST_DETAIL,


  }
};

/**
 * 迁移阶段配置
 */
const MIGRATION_PHASES = {
  PHASE_1: {
    name: '准备阶段',
    description: '创建新API配置，保持旧API兼容',
    actions: [
      '创建统一API配置文件',
      '创建迁移映射表',
      '后端路由同时支持新旧路径'
    ]
  },
  
  PHASE_2: {
    name: '逐步迁移阶段', 
    description: '前端页面逐步迁移到新API',
    actions: [
      '更新前端页面使用新API',
      '测试新API功能完整性',
      '保持旧API兼容性'
    ]
  },
  
  PHASE_3: {
    name: '清理阶段',
    description: '移除旧API配置和路由',
    actions: [
      '确认所有页面已使用新API',
      '移除旧API路由',
      '清理旧API配置文件'
    ]
  }
};

/**
 * 获取迁移后的API端点
 * @param {string} oldEndpoint 旧的API端点
 * @returns {string} 新的API端点
 */
function getMigratedEndpoint(oldEndpoint) {
  return API_MIGRATION_MAP[oldEndpoint] || oldEndpoint;
}

/**
 * 检查是否需要特殊迁移处理
 * @param {string} oldEndpoint 旧的API端点
 * @returns {object|null} 特殊迁移配置或null
 */
function getSpecialMigration(oldEndpoint) {
  // 检查审批操作
  if (SPECIAL_MIGRATIONS.APPROVAL_ACTIONS[oldEndpoint]) {
    return SPECIAL_MIGRATIONS.APPROVAL_ACTIONS[oldEndpoint];
  }
  
  // 检查取消操作
  for (const pattern in SPECIAL_MIGRATIONS.CANCEL_OPERATIONS) {
    if (oldEndpoint.match(pattern.replace('{id}', '\\d+'))) {
      return SPECIAL_MIGRATIONS.CANCEL_OPERATIONS[pattern];
    }
  }
  
  return null;
}

/**
 * 验证API迁移完整性
 * @returns {object} 验证结果
 */
function validateMigration() {
  const unmapped = [];
  const duplicates = [];
  const newEndpoints = Object.values(API_ENDPOINTS).flat();
  
  // 检查是否有未映射的旧API
  // 这里可以添加具体的验证逻辑
  
  return {
    success: unmapped.length === 0 && duplicates.length === 0,
    unmapped,
    duplicates,
    totalMapped: Object.keys(API_MIGRATION_MAP).length
  };
}

module.exports = {
  API_MIGRATION_MAP,
  SPECIAL_MIGRATIONS,
  MIGRATION_PHASES,
  getMigratedEndpoint,
  getSpecialMigration,
  validateMigration
};