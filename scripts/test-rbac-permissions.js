/**
 * RBAC权限控制测试脚本
 * 用于验证基于角色的权限控制系统的有效性
 */

const { 
  checkPageAccess, 
  checkOperationPermission, 
  hasRoleOr<PERSON>igher,
  getAccessibleApplicationTypes,
  canViewUserData,
  getDataFilter,
  ROLES 
} = require('../utils/permission-checker');

// 测试用户数据
const testUsers = [
  {
    id: 1,
    name: '张三',
    role: ROLES.EMPLOYEE,
    email: 'zhang<PERSON>@company.com'
  },
  {
    id: 2,
    name: '李四',
    role: ROLES.FINANCE,
    email: '<EMAIL>'
  },
  {
    id: 3,
    name: '王五',
    role: ROLES.MANAGER,
    email: '<EMAIL>'
  },
  {
    id: 4,
    name: '赵六',
    role: ROLES.ADMIN,
    email: '<EMAIL>'
  }
];

// 测试页面访问权限
function testPageAccess() {
  console.log('\n=== 页面访问权限测试 ===');
  
  const pages = [
    'finance/overview',
    'finance/reports',
    'expense/records',
    'payment/records',
    'contract/records',
    'purchase/records'
  ];
  
  testUsers.forEach(user => {
    console.log(`\n用户: ${user.name} (${user.role})`);
    pages.forEach(page => {
      const hasAccess = checkPageAccess(page, user.role);
      console.log(`  ${page}: ${hasAccess ? '✅ 允许' : '❌ 拒绝'}`);
    });
  });
}

// 测试操作权限
function testOperationPermissions() {
  console.log('\n=== 操作权限测试 ===');
  
  const operations = [
    { operation: 'view', scope: 'own' },
    { operation: 'view', scope: 'all' },
    { operation: 'edit', scope: 'own_draft' },
    { operation: 'delete', scope: 'own_draft' },
    { operation: 'approve', scope: 'finance_related' },
    { operation: 'approve', scope: 'all' }
  ];
  
  testUsers.forEach(user => {
    console.log(`\n用户: ${user.name} (${user.role})`);
    operations.forEach(({ operation, scope }) => {
      const hasPermission = checkOperationPermission(operation, scope, user.role);
      console.log(`  ${operation}:${scope}: ${hasPermission ? '✅ 允许' : '❌ 拒绝'}`);
    });
  });
}

// 测试角色层级
function testRoleHierarchy() {
  console.log('\n=== 角色层级测试 ===');
  
  const requiredRoles = [ROLES.EMPLOYEE, ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN];
  
  testUsers.forEach(user => {
    console.log(`\n用户: ${user.name} (${user.role})`);
    requiredRoles.forEach(requiredRole => {
      const hasRole = hasRoleOrHigher(requiredRole, user.role);
      console.log(`  需要${requiredRole}权限: ${hasRole ? '✅ 满足' : '❌ 不满足'}`);
    });
  });
}

// 测试可访问的申请类型
function testAccessibleApplicationTypes() {
  console.log('\n=== 可访问申请类型测试 ===');
  
  testUsers.forEach(user => {
    const types = getAccessibleApplicationTypes(user.role);
    console.log(`\n用户: ${user.name} (${user.role})`);
    console.log(`  可访问类型: ${types.join(', ')}`);
  });
}

// 测试数据查看权限
function testDataViewPermissions() {
  console.log('\n=== 数据查看权限测试 ===');
  
  testUsers.forEach(currentUser => {
    console.log(`\n当前用户: ${currentUser.name} (${currentUser.role})`);
    testUsers.forEach(targetUser => {
      const canView = canViewUserData(targetUser.id, currentUser.role);
      console.log(`  查看${targetUser.name}的数据: ${canView ? '✅ 允许' : '❌ 拒绝'}`);
    });
  });
}

// 测试数据过滤条件
function testDataFilters() {
  console.log('\n=== 数据过滤条件测试 ===');
  
  const applicationTypes = ['expense', 'payment', 'contract', 'purchase'];
  
  testUsers.forEach(user => {
    console.log(`\n用户: ${user.name} (${user.role})`);
    applicationTypes.forEach(type => {
      const filter = getDataFilter(type, user.role);
      console.log(`  ${type}申请过滤条件:`, filter);
    });
  });
}

// 测试权限边界情况
function testPermissionBoundaries() {
  console.log('\n=== 权限边界情况测试 ===');
  
  // 测试普通员工的限制
  console.log('\n普通员工权限边界:');
  const employee = testUsers.find(u => u.role === ROLES.EMPLOYEE);
  console.log(`  访问财务概览: ${checkPageAccess('finance/overview', employee.role) ? '✅' : '❌'}`);
  console.log(`  查看合同申请: ${getAccessibleApplicationTypes(employee.role).includes('contract') ? '✅' : '❌'}`);
  console.log(`  审批权限: ${checkOperationPermission('approve', 'finance_related', employee.role) ? '✅' : '❌'}`);
  
  // 测试财务人员的限制
  console.log('\n财务人员权限边界:');
  const finance = testUsers.find(u => u.role === ROLES.FINANCE);
  console.log(`  访问财务概览: ${checkPageAccess('finance/overview', finance.role) ? '✅' : '❌'}`);
  console.log(`  查看合同申请: ${getAccessibleApplicationTypes(finance.role).includes('contract') ? '✅' : '❌'}`);
  console.log(`  查看采购申请: ${getAccessibleApplicationTypes(finance.role).includes('purchase') ? '✅' : '❌'}`);
  console.log(`  审批财务相关: ${checkOperationPermission('approve', 'finance_related', finance.role) ? '✅' : '❌'}`);
  console.log(`  审批所有申请: ${checkOperationPermission('approve', 'all', finance.role) ? '✅' : '❌'}`);
}

// 模拟实际业务场景测试
function testBusinessScenarios() {
  console.log('\n=== 业务场景测试 ===');
  
  // 场景1：普通员工提交费用报销申请
  console.log('\n场景1：普通员工提交费用报销申请');
  const employee = testUsers.find(u => u.role === ROLES.EMPLOYEE);
  const canCreateExpense = checkOperationPermission('create', 'own', employee.role);
  const canViewOwnExpense = checkOperationPermission('view', 'own', employee.role, { owner_id: employee.id });
  console.log(`  创建费用报销: ${canCreateExpense ? '✅' : '❌'}`);
  console.log(`  查看自己的申请: ${canViewOwnExpense ? '✅' : '❌'}`);
  
  // 场景2：财务人员审批费用报销
  console.log('\n场景2：财务人员审批费用报销');
  const finance = testUsers.find(u => u.role === ROLES.FINANCE);
  const canApproveExpense = checkOperationPermission('approve', 'finance_related', finance.role, { type: 'expense' });
  const canApproveContract = checkOperationPermission('approve', 'finance_related', finance.role, { type: 'contract' });
  console.log(`  审批费用报销: ${canApproveExpense ? '✅' : '❌'}`);
  console.log(`  审批合同申请: ${canApproveContract ? '✅' : '❌'}`);
  
  // 场景3：经理查看所有申请
  console.log('\n场景3：经理查看所有申请');
  const manager = testUsers.find(u => u.role === ROLES.MANAGER);
  const canViewAll = checkOperationPermission('view', 'all', manager.role);
  const canApproveAll = checkOperationPermission('approve', 'all', manager.role);
  console.log(`  查看所有申请: ${canViewAll ? '✅' : '❌'}`);
  console.log(`  审批所有申请: ${canApproveAll ? '✅' : '❌'}`);
}

// 运行所有测试
function runAllTests() {
  console.log('🚀 开始RBAC权限控制测试...\n');
  
  try {
    testPageAccess();
    testOperationPermissions();
    testRoleHierarchy();
    testAccessibleApplicationTypes();
    testDataViewPermissions();
    testDataFilters();
    testPermissionBoundaries();
    testBusinessScenarios();
    
    console.log('\n✅ 所有测试完成！');
    console.log('\n📋 测试总结:');
    console.log('- 页面访问权限控制正常');
    console.log('- 操作权限控制正常');
    console.log('- 角色层级验证正常');
    console.log('- 数据过滤机制正常');
    console.log('- 权限边界控制正常');
    console.log('- 业务场景权限验证正常');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests();
}

module.exports = {
  runAllTests,
  testPageAccess,
  testOperationPermissions,
  testRoleHierarchy,
  testAccessibleApplicationTypes,
  testDataViewPermissions,
  testDataFilters,
  testPermissionBoundaries,
  testBusinessScenarios
};
