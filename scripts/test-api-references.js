#!/usr/bin/env node
/**
 * 测试API引用是否正确
 */

const fs = require('fs');

console.log('🔍 测试API引用...\n');

// 测试常量导入
try {
  const constants = require('../constants/index.js');
  
  console.log('✅ 常量文件导入成功');
  console.log('API对象结构:', Object.keys(constants.API));
  
  if (constants.API.API_ENDPOINTS) {
    console.log('✅ API_ENDPOINTS 存在');
    
    if (constants.API.API_ENDPOINTS.WORKSPACE) {
      console.log('✅ WORKSPACE 端点存在');
      
      if (constants.API.API_ENDPOINTS.WORKSPACE.APPROVALS) {
        console.log('✅ APPROVALS 端点存在');
        console.log('  - PENDING:', constants.API.API_ENDPOINTS.WORKSPACE.APPROVALS.PENDING);
        console.log('  - HISTORY:', constants.API.API_ENDPOINTS.WORKSPACE.APPROVALS.HISTORY);
      } else {
        console.log('❌ APPROVALS 端点不存在');
      }
      
      if (constants.API.API_ENDPOINTS.WORKSPACE.FINANCE) {
        console.log('✅ FINANCE 端点存在');
        console.log('  - STATISTICS:', constants.API.API_ENDPOINTS.WORKSPACE.FINANCE.STATISTICS);
        console.log('  - RECENT_RECORDS:', constants.API.API_ENDPOINTS.WORKSPACE.FINANCE.RECENT_RECORDS);
        console.log('  - MONTHLY_TREND:', constants.API.API_ENDPOINTS.WORKSPACE.FINANCE.MONTHLY_TREND);
      } else {
        console.log('❌ FINANCE 端点不存在');
      }
      
      if (constants.API.API_ENDPOINTS.WORKSPACE.APPLICATIONS) {
        console.log('✅ APPLICATIONS 端点存在');
        console.log('  - APPROVE function:', typeof constants.API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.APPROVE);
      } else {
        console.log('❌ APPLICATIONS 端点不存在');
      }
    } else {
      console.log('❌ WORKSPACE 端点不存在');
    }
  } else {
    console.log('❌ API_ENDPOINTS 不存在');
  }
  
} catch (error) {
  console.log('❌ 常量文件导入失败:', error.message);
}

console.log('\n🔍 检查图标文件...');

const iconFiles = [
  'assets/icons/empty-approval.svg',
  'assets/icons/empty-history.svg', 
  'assets/icons/expense.svg',
  'assets/icons/purchase.svg',
  'assets/icons/payment.svg',
  'assets/icons/report.svg'
];

iconFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} 存在`);
  } else {
    console.log(`❌ ${file} 不存在`);
  }
});

console.log('\n🎉 API引用测试完成！');
