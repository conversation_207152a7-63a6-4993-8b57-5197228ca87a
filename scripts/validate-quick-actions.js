#!/usr/bin/env node
/**
 * 验证财务管理快捷操作功能
 */

const fs = require('fs');

console.log('🔍 验证财务管理快捷操作功能...\n');

// 需要验证的财务子模块
const modules = ['expense', 'payment', 'contract', 'activity', 'reserve'];

let passedTests = 0;
let totalTests = 0;

function test(description, condition) {
  totalTests++;
  if (condition) {
    console.log(`✅ ${description}`);
    passedTests++;
  } else {
    console.log(`❌ ${description}`);
  }
}

console.log('📋 1. 验证列表页面文件完整性');
modules.forEach(module => {
  const basePath = `pages/workspace/${module}/list`;
  
  test(`${module} 列表页面 JS 文件存在`, fs.existsSync(`${basePath}/list.js`));
  test(`${module} 列表页面 WXML 文件存在`, fs.existsSync(`${basePath}/list.wxml`));
  test(`${module} 列表页面 WXSS 文件存在`, fs.existsSync(`${basePath}/list.wxss`));
  
  // 检查文件内容不为空
  if (fs.existsSync(`${basePath}/list.js`)) {
    const jsContent = fs.readFileSync(`${basePath}/list.js`, 'utf8');
    test(`${module} 列表页面 JS 文件有实际内容`, jsContent.length > 1000);
    test(`${module} 列表页面包含正确的API调用`, jsContent.includes('API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.LIST'));
  }
  
  if (fs.existsSync(`${basePath}/list.wxml`)) {
    const wxmlContent = fs.readFileSync(`${basePath}/list.wxml`, 'utf8');
    test(`${module} 列表页面 WXML 文件有实际内容`, wxmlContent.length > 500);
    test(`${module} 列表页面包含正确的标题`, wxmlContent.includes('class="title"'));
  }
});

console.log('\n🎨 2. 验证图标文件');
modules.forEach(module => {
  test(`${module} 图标文件存在`, fs.existsSync(`assets/icons/${module}.svg`));
});

console.log('\n🔗 3. 验证财务概览页面的快捷操作配置');
try {
  const overviewWxml = fs.readFileSync('pages/workspace/finance/overview/overview.wxml', 'utf8');
  
  test('财务概览页面包含快捷操作区域', overviewWxml.includes('quick-actions'));
  test('费用报销快捷操作配置正确', overviewWxml.includes('data-type="expense"'));
  test('采购申请快捷操作配置正确', overviewWxml.includes('data-type="purchase"'));
  test('付款申请快捷操作配置正确', overviewWxml.includes('data-type="payment"'));
  
  // 检查图标引用
  modules.forEach(module => {
    if (overviewWxml.includes(`data-type="${module}"`)) {
      test(`${module} 快捷操作图标引用正确`, overviewWxml.includes(`/assets/icons/${module}.svg`));
    }
  });
  
} catch (error) {
  console.log(`❌ 财务概览页面验证失败: ${error.message}`);
}

console.log('\n⚙️ 4. 验证app.json配置');
try {
  const appJson = JSON.parse(fs.readFileSync('app.json', 'utf8'));
  const workspaceSubPackage = appJson.subPackages.find(pkg => pkg.root === 'pages/workspace');
  
  test('app.json包含工作台分包', !!workspaceSubPackage);
  
  if (workspaceSubPackage) {
    modules.forEach(module => {
      test(`${module}/list/list 页面在app.json中配置`, 
        workspaceSubPackage.pages.includes(`${module}/list/list`)
      );
    });
  }
  
} catch (error) {
  console.log(`❌ app.json配置验证失败: ${error.message}`);
}

console.log('\n📊 验证结果统计');
console.log(`通过测试: ${passedTests}/${totalTests}`);
console.log(`成功率: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 财务管理快捷操作功能验证通过！');
  console.log('✨ 所有6个财务子模块的列表页面都已创建');
  console.log('✨ 所有图标文件都已创建');
  console.log('✨ 财务概览页面的快捷操作配置正确');
  console.log('✨ app.json配置完整');
  console.log('\n🚀 用户现在可以：');
  console.log('1. 点击财务管理查看完整的财务概览页面');
  console.log('2. 点击快捷操作中的任何模块查看对应的列表页面');
  console.log('3. 在列表页面中查看、创建和管理各类财务申请');
  console.log('4. 享受统一、美观的界面设计');
} else {
  console.log('\n⚠️ 部分验证未通过，请检查上述失败项');
  process.exit(1);
}

console.log('\n📋 功能说明:');
console.log('• 费用报销：管理日常费用报销申请');
console.log('• 采购申请：管理物品采购申请');
console.log('• 付款申请：管理各类付款申请');
console.log('• 合同申请：管理合同签署申请');
console.log('• 活动经费：管理活动经费申请');
console.log('• 备用金：管理备用金申请');
