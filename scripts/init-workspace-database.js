#!/usr/bin/env node

/**
 * 工作台数据库初始化脚本
 * 创建工作台相关的数据表和初始数据
 */

const { sequelize } = require('../backend/config/database');
const {
  FinanceApplication,
  ApprovalWorkflow,
  ApprovalTemplate,
  FinanceRecord,
  defineAssociations,
  syncTables,
  initializeDefaultData
} = require('../backend/models/workspace');

async function initWorkspaceDatabase() {
  try {
    console.log('🚀 开始初始化工作台数据库...');
    
    // 1. 测试数据库连接
    console.log('📡 测试数据库连接...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 2. 定义模型关联
    console.log('🔗 定义模型关联关系...');
    defineAssociations();
    console.log('✅ 模型关联定义完成');
    
    // 3. 同步数据表
    console.log('📊 同步数据表结构...');
    await syncTables({ force: false }); // 不强制重建表
    console.log('✅ 数据表同步完成');
    
    // 4. 初始化默认数据
    console.log('📝 初始化默认数据...');
    await initializeDefaultData();
    console.log('✅ 默认数据初始化完成');
    
    // 5. 验证数据表
    console.log('🔍 验证数据表结构...');
    await validateTables();
    console.log('✅ 数据表验证通过');
    
    console.log('🎉 工作台数据库初始化完成！');
    
    // 显示统计信息
    await showStatistics();
    
  } catch (error) {
    console.error('❌ 工作台数据库初始化失败:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

/**
 * 验证数据表结构
 */
async function validateTables() {
  const tables = [
    'finance_applications',
    'approval_workflows', 
    'approval_templates',
    'finance_records'
  ];
  
  for (const tableName of tables) {
    try {
      const [results] = await sequelize.query(`DESCRIBE ${tableName}`);
      console.log(`   ✓ 表 ${tableName} 存在，包含 ${results.length} 个字段`);
    } catch (error) {
      throw new Error(`表 ${tableName} 不存在或结构异常: ${error.message}`);
    }
  }
}

/**
 * 显示统计信息
 */
async function showStatistics() {
  try {
    console.log('\n📈 数据库统计信息:');
    
    const [applicationCount, workflowCount, templateCount, recordCount] = await Promise.all([
      FinanceApplication.count(),
      ApprovalWorkflow.count(),
      ApprovalTemplate.count(),
      FinanceRecord.count()
    ]);
    
    console.log(`   📋 财务申请: ${applicationCount} 条`);
    console.log(`   🔄 审批流程: ${workflowCount} 条`);
    console.log(`   📝 流程模板: ${templateCount} 条`);
    console.log(`   💰 财务记录: ${recordCount} 条`);
    
    // 显示审批模板信息
    if (templateCount > 0) {
      console.log('\n📋 审批流程模板:');
      const templates = await ApprovalTemplate.findAll({
        attributes: ['name', 'applicationType', 'isActive']
      });
      
      templates.forEach(template => {
        const status = template.isActive ? '✅' : '❌';
        console.log(`   ${status} ${template.name} (${template.applicationType})`);
      });
    }
    
  } catch (error) {
    console.error('获取统计信息失败:', error);
  }
}

/**
 * 创建示例数据（可选）
 */
async function createSampleData() {
  try {
    console.log('📝 创建示例数据...');
    
    // 创建示例申请
    const sampleApplication = await FinanceApplication.create({
      type: 'expense',
      title: '办公用品采购报销',
      description: '购买打印纸、文具等办公用品',
      amount: 500.00,
      applicantId: 1,
      status: 'pending',
      applicationDate: new Date(),
      metadata: {
        category: 'office',
        items: ['打印纸', '签字笔', '文件夹']
      }
    });
    
    console.log(`   ✓ 创建示例申请: ${sampleApplication.applicationNo}`);
    
    // 创建示例财务记录
    const sampleRecord = await FinanceRecord.create({
      type: 'expense',
      category: 'office_expense',
      amount: 300.00,
      recordDate: new Date(),
      description: '办公用品采购',
      sourceType: 'manual',
      userId: 1,
      status: 'confirmed'
    });
    
    console.log(`   ✓ 创建示例财务记录: ${sampleRecord.recordNo}`);
    
  } catch (error) {
    console.error('创建示例数据失败:', error);
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
const shouldCreateSampleData = args.includes('--sample-data');

// 执行初始化
(async () => {
  await initWorkspaceDatabase();
  
  if (shouldCreateSampleData) {
    await createSampleData();
  }
})();

module.exports = {
  initWorkspaceDatabase,
  validateTables,
  showStatistics,
  createSampleData
};
