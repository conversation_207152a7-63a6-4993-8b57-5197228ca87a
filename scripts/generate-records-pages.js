/**
 * 批量生成申请记录页面的脚本
 */

const fs = require('fs');
const path = require('path');

// 申请模块配置
const modules = [
  {
    name: 'contract',
    title: '合同申请',
    color: '#722ed1',
    financeRelated: false
  },
  {
    name: 'activity',
    title: '活动经费',
    color: '#eb2f96',
    financeRelated: true
  },
  {
    name: 'reserve',
    title: '备用金',
    color: '#13c2c2',
    financeRelated: true
  },
  {
    name: 'purchase',
    title: '采购申请',
    color: '#52c41a',
    financeRelated: false
  }
];

// JS文件模板
const jsTemplate = (module) => `/**
 * ${module.title}记录页面
 */

const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');
const { 
  checkPageAccess, 
  getCurrentUser, 
  checkOperationPermission,
  getDataFilter,
  ROLES,
  handlePermissionDenied 
} = require('../../../../utils/permission-checker.js');

Page({
  data: {
    loading: true,
    refreshing: false,
    loadingMore: false,
    hasMore: true,
    
    // 权限控制
    userRole: '',
    canViewAll: false,
    canApprove: false,
    canEdit: false,
    
    // 列表数据
    records: [],
    total: 0,
    
    // 分页参数
    page: 1,
    pageSize: 20,
    
    // 筛选条件
    filters: {
      status: '',
      dateRange: '',
      amountRange: ''
    },
    
    // 状态选项
    statusOptions: [
      { label: '全部', value: '' },
      { label: '草稿', value: 'draft' },
      { label: '待审批', value: 'pending' },
      { label: '已通过', value: 'approved' },
      { label: '已拒绝', value: 'rejected' },
      { label: '已撤回', value: 'withdrawn' }
    ],
    
    // 状态映射
    statusMap: {
      'draft': { text: '草稿', color: '#999' },
      'pending': { text: '待审批', color: '#1890ff' },
      'approved': { text: '已通过', color: '#52c41a' },
      'rejected': { text: '已拒绝', color: '#ff4d4f' },
      'withdrawn': { text: '已撤回', color: '#d9d9d9' }
    }
  },

  onLoad(options) {
    if (!this.checkPermissions()) {
      return;
    }
    this.loadRecords();
  },

  checkPermissions() {
    const currentUser = getCurrentUser();
    const hasAccess = checkPageAccess('${module.name}/records');
    
    if (!hasAccess) {
      handlePermissionDenied('您没有权限查看${module.title}记录');
      return false;
    }
    
    this.setData({
      userRole: currentUser.role,
      canViewAll: currentUser.role !== ROLES.EMPLOYEE,
      canApprove: ${module.financeRelated ? 
        '[ROLES.FINANCE, ROLES.MANAGER, ROLES.ADMIN].includes(currentUser.role)' : 
        '[ROLES.MANAGER, ROLES.ADMIN].includes(currentUser.role)'
      },
      canEdit: true
    });
    
    return true;
  },

  async loadRecords(isRefresh = false) {
    if (isRefresh) {
      this.setData({ 
        page: 1, 
        records: [], 
        hasMore: true,
        refreshing: true 
      });
    } else {
      this.setData({ loading: true });
    }

    try {
      const currentUser = getCurrentUser();
      const params = {
        page: this.data.page,
        pageSize: this.data.pageSize,
        ...this.data.filters
      };
      
      const dataFilter = getDataFilter('${module.name}', currentUser.role);
      Object.assign(params, dataFilter);

      const response = await request.get(API.API_ENDPOINTS.WORKSPACE.${module.name.toUpperCase()}.LIST, params);

      if (response.success) {
        const newRecords = response.data.list || [];
        const records = isRefresh ? newRecords : [...this.data.records, ...newRecords];
        
        this.setData({
          records,
          total: response.data.total || 0,
          hasMore: newRecords.length === this.data.pageSize
        });
      } else {
        wx.showToast({
          title: response.message || '加载失败',
          icon: 'none'
        });
      }

    } catch (error) {
      console.error('加载申请记录失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ 
        loading: false, 
        refreshing: false,
        loadingMore: false 
      });
    }
  },

  onPullDownRefresh() {
    this.loadRecords(true).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (!this.data.hasMore || this.data.loadingMore) {
      return;
    }

    this.setData({ 
      page: this.data.page + 1,
      loadingMore: true 
    });
    
    this.loadRecords();
  },

  onViewDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: \`/pages/workspace/${module.name}/detail/detail?id=\${id}\`
    });
  },

  onEditRecord(e) {
    const { id, status, userId } = e.currentTarget.dataset;
    const currentUser = getCurrentUser();
    
    const canEdit = checkOperationPermission('edit', 'own', currentUser.role, {
      owner_id: userId,
      status: status
    });
    
    if (!canEdit) {
      wx.showToast({
        title: '无权限编辑此记录',
        icon: 'none'
      });
      return;
    }
    
    if (!['draft', 'rejected'].includes(status)) {
      wx.showToast({
        title: '只能编辑草稿或被退回的申请',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: \`/pages/workspace/${module.name}/apply/apply?id=\${id}&mode=edit\`
    });
  },

  onApproveRecord(e) {
    const { id, status } = e.currentTarget.dataset;
    
    if (!this.data.canApprove) {
      wx.showToast({
        title: '无权限审批',
        icon: 'none'
      });
      return;
    }
    
    if (status !== 'pending') {
      wx.showToast({
        title: '只能审批待审批的申请',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: \`/pages/workspace/${module.name}/approve/approve?id=\${id}\`
    });
  },

  onDeleteRecord(e) {
    const { id, status, userId } = e.currentTarget.dataset;
    const currentUser = getCurrentUser();
    
    const canDelete = checkOperationPermission('delete', 'own', currentUser.role, {
      owner_id: userId,
      status: status
    });
    
    if (!canDelete) {
      wx.showToast({
        title: '无权限删除此记录',
        icon: 'none'
      });
      return;
    }
    
    if (status !== 'draft') {
      wx.showToast({
        title: '只能删除草稿状态的申请',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这条申请记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.deleteRecord(id);
        }
      }
    });
  },

  async deleteRecord(id) {
    try {
      wx.showLoading({ title: '删除中...' });
      
      const response = await request.delete(\`\${API.API_ENDPOINTS.WORKSPACE.${module.name.toUpperCase()}.DELETE}/\${id}\`);
      
      if (response.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        this.loadRecords(true);
      } else {
        wx.showToast({
          title: response.message || '删除失败',
          icon: 'none'
        });
      }
      
    } catch (error) {
      console.error('删除申请失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  onStatusFilter(e) {
    const { value } = e.currentTarget.dataset;
    this.setData({
      'filters.status': value
    });
    this.loadRecords(true);
  },

  onCreateNew() {
    wx.navigateTo({
      url: '/pages/workspace/${module.name}/apply/apply'
    });
  },

  formatAmount(amount) {
    return parseFloat(amount || 0).toFixed(2);
  },

  formatDate(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return \`\${date.getFullYear()}-\${String(date.getMonth() + 1).padStart(2, '0')}-\${String(date.getDate()).padStart(2, '0')}\`;
  }
});`;

// WXML文件模板
const wxmlTemplate = (module) => `<!--${module.title}记录页面-->
<view class="container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="header-content">
      <text class="title">${module.title}记录</text>
      <text class="subtitle">共 {{total}} 条记录</text>
    </view>
    <button class="create-btn" bindtap="onCreateNew">
      <text class="create-icon">+</text>
      <text class="create-text">新建</text>
    </button>
  </view>

  <!-- 筛选区域 -->
  <view class="filter-section">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-item" 
            wx:for="{{statusOptions}}" 
            wx:key="value"
            class="filter-item {{filters.status === item.value ? 'active' : ''}}"
            data-value="{{item.value}}"
            bindtap="onStatusFilter">
        <text class="filter-text">{{item.label}}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 权限提示 -->
  <view wx:if="{{userRole === 'employee'}}" class="permission-notice">
    <text class="notice-icon">👤</text>
    <text class="notice-text">您只能查看自己提交的申请记录</text>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 记录列表 -->
  <view wx:else class="records-list">
    <!-- 空状态 -->
    <view wx:if="{{records.length === 0}}" class="empty-state">
      <image class="empty-icon" src="/images/icon_empty.png" mode="aspectFit"></image>
      <text class="empty-text">暂无申请记录</text>
      <button class="empty-action" bindtap="onCreateNew">立即创建</button>
    </view>

    <!-- 记录卡片 -->
    <view wx:else>
      <view class="record-card" 
            wx:for="{{records}}" 
            wx:key="id"
            bindtap="onViewDetail"
            data-id="{{item.id}}">
        
        <!-- 卡片头部 -->
        <view class="card-header">
          <view class="header-left">
            <text class="record-title">{{item.title}}</text>
            <view class="status-badge status-{{item.status}}">
              <text class="status-text">{{statusMap[item.status].text}}</text>
            </view>
          </view>
          <view class="header-right">
            <text class="amount">¥{{formatAmount(item.amount)}}</text>
          </view>
        </view>

        <!-- 卡片内容 -->
        <view class="card-content">
          <view class="info-row">
            <text class="info-label">申请人：</text>
            <text class="info-value">{{item.applicant_name}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">申请时间：</text>
            <text class="info-value">{{formatDate(item.created_at)}}</text>
          </view>
          <view wx:if="{{item.description}}" class="info-row">
            <text class="info-label">说明：</text>
            <text class="info-value description">{{item.description}}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="card-actions" catchtap="true">
          <!-- 查看详情 -->
          <button class="action-btn view" 
                  bindtap="onViewDetail" 
                  data-id="{{item.id}}">
            查看
          </button>
          
          <!-- 编辑按钮 -->
          <button wx:if="{{(item.status === 'draft' || item.status === 'rejected') && (canEdit || item.user_id === userInfo.id)}}"
                  class="action-btn edit" 
                  bindtap="onEditRecord" 
                  data-id="{{item.id}}"
                  data-status="{{item.status}}"
                  data-user-id="{{item.user_id}}">
            编辑
          </button>
          
          <!-- 审批按钮 -->
          <button wx:if="{{item.status === 'pending' && canApprove}}"
                  class="action-btn approve" 
                  bindtap="onApproveRecord" 
                  data-id="{{item.id}}"
                  data-status="{{item.status}}">
            审批
          </button>
          
          <!-- 删除按钮 -->
          <button wx:if="{{item.status === 'draft' && (canEdit || item.user_id === userInfo.id)}}"
                  class="action-btn delete" 
                  bindtap="onDeleteRecord" 
                  data-id="{{item.id}}"
                  data-status="{{item.status}}"
                  data-user-id="{{item.user_id}}">
            删除
          </button>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{loadingMore}}" class="loading-more">
      <view class="loading-spinner small"></view>
      <text class="loading-text">加载更多...</text>
    </view>

    <!-- 没有更多数据 -->
    <view wx:if="{{!hasMore && records.length > 0}}" class="no-more">
      <text class="no-more-text">没有更多数据了</text>
    </view>
  </view>
</view>`;

// JSON配置模板
const jsonTemplate = (module) => `{
  "navigationBarTitleText": "${module.title}记录",
  "navigationBarBackgroundColor": "${module.color}",
  "navigationBarTextStyle": "white",
  "backgroundColor": "#f5f5f5",
  "enablePullDownRefresh": true,
  "onReachBottomDistance": 50
}`;

// 生成文件的函数
function generateFiles() {
  modules.forEach(module => {
    const dirPath = path.join(__dirname, '..', 'pages', 'workspace', module.name, 'records');
    
    // 创建目录
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    
    // 生成JS文件
    fs.writeFileSync(
      path.join(dirPath, 'records.js'),
      jsTemplate(module)
    );
    
    // 生成WXML文件
    fs.writeFileSync(
      path.join(dirPath, 'records.wxml'),
      wxmlTemplate(module)
    );
    
    // 生成JSON文件
    fs.writeFileSync(
      path.join(dirPath, 'records.json'),
      jsonTemplate(module)
    );
    
    // 复制WXSS文件（使用费用报销的样式）
    const wxssSource = path.join(__dirname, '..', 'pages', 'workspace', 'expense', 'records', 'records.wxss');
    const wxssTarget = path.join(dirPath, 'records.wxss');
    
    if (fs.existsSync(wxssSource)) {
      fs.copyFileSync(wxssSource, wxssTarget);
    }
    
    console.log(`Generated records pages for ${module.title}`);
  });
}

// 执行生成
if (require.main === module) {
  generateFiles();
  console.log('All records pages generated successfully!');
}

module.exports = { generateFiles };`;

// 由于我们在微信小程序环境中，我将直接创建剩余的records页面文件
