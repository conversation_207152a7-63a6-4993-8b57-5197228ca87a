#!/usr/bin/env node
/**
 * 简单的WXML语法测试
 */

const fs = require('fs');

function testWXMLSyntax(filePath) {
  console.log(`🔍 测试文件: ${filePath}`);
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 基本检查
    const openTags = (content.match(/<[^\/][^>]*>/g) || []).length;
    const closeTags = (content.match(/<\/[^>]*>/g) || []).length;
    const selfCloseTags = (content.match(/<[^>]*\/>/g) || []).length;
    
    console.log(`📊 标签统计:`);
    console.log(`  - 开始标签: ${openTags}`);
    console.log(`  - 结束标签: ${closeTags}`);
    console.log(`  - 自闭合标签: ${selfCloseTags}`);
    
    // 检查是否有明显的语法错误
    const hasUnclosedTags = content.includes('<view') && !content.includes('</view>');
    const hasUnclosedButtons = content.includes('<button') && !content.includes('</button>');
    const hasUnclosedPickers = content.includes('<picker') && !content.includes('</picker>');
    
    if (hasUnclosedTags || hasUnclosedButtons || hasUnclosedPickers) {
      console.log('❌ 发现可能的未闭合标签');
      return false;
    }
    
    // 检查基本结构
    if (!content.includes('<view class="container">')) {
      console.log('❌ 缺少容器结构');
      return false;
    }
    
    console.log('✅ 基本语法检查通过');
    return true;
    
  } catch (error) {
    console.log(`❌ 读取文件失败: ${error.message}`);
    return false;
  }
}

// 测试采购申请页面
const filePath = 'pages/workspace/purchase/apply/apply.wxml';
const isValid = testWXMLSyntax(filePath);

if (isValid) {
  console.log('\n🎉 采购申请页面WXML基本语法正确！');
} else {
  console.log('\n💥 采购申请页面WXML可能存在语法问题。');
}
