#!/usr/bin/env node

/**
 * 工作台功能测试脚本
 * 测试工作台的核心功能是否正常工作
 */

const axios = require('axios');
const { hasPermission } = require('../backend/config/workspace-permissions');

// 测试配置
const TEST_CONFIG = {
  baseURL: 'http://localhost:3000/api/v1',
  timeout: 5000,
  testUser: {
    id: 1,
    role: 'admin',
    name: '测试管理员'
  }
};

// 创建axios实例
const api = axios.create({
  baseURL: TEST_CONFIG.baseURL,
  timeout: TEST_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json'
  }
});

/**
 * 测试权限系统
 */
async function testPermissionSystem() {
  console.log('🔐 测试权限系统...');
  
  const testCases = [
    { role: 'admin', permission: 'workspace_access', expected: true },
    { role: 'admin', permission: 'approval_batch', expected: true },
    { role: 'employee', permission: 'approval_batch', expected: false },
    { role: 'finance', permission: 'finance_view', expected: true },
    { role: 'employee', permission: 'finance_export', expected: false }
  ];
  
  let passed = 0;
  let failed = 0;
  
  testCases.forEach(({ role, permission, expected }) => {
    const result = hasPermission(role, permission);
    if (result === expected) {
      console.log(`   ✅ ${role} - ${permission}: ${result}`);
      passed++;
    } else {
      console.log(`   ❌ ${role} - ${permission}: 期望 ${expected}, 实际 ${result}`);
      failed++;
    }
  });
  
  console.log(`   权限测试结果: ${passed} 通过, ${failed} 失败\n`);
  return failed === 0;
}

/**
 * 测试API端点
 */
async function testAPIEndpoints() {
  console.log('🌐 测试API端点...');
  
  const endpoints = [
    { method: 'GET', path: '/workspace/dashboard', description: '工作台首页' },
    { method: 'GET', path: '/workspace/statistics', description: '财务统计' },
    { method: 'GET', path: '/workspace/applications', description: '申请列表' },
    { method: 'GET', path: '/workspace/my-approvals', description: '我的审批' }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const endpoint of endpoints) {
    try {
      const response = await api.request({
        method: endpoint.method,
        url: endpoint.path,
        headers: {
          'Authorization': 'Bearer test-token',
          'X-User-ID': TEST_CONFIG.testUser.id,
          'X-User-Role': TEST_CONFIG.testUser.role
        }
      });
      
      if (response.status === 200) {
        console.log(`   ✅ ${endpoint.method} ${endpoint.path} - ${endpoint.description}`);
        passed++;
      } else {
        console.log(`   ⚠️  ${endpoint.method} ${endpoint.path} - 状态码: ${response.status}`);
        failed++;
      }
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`   ⚠️  ${endpoint.method} ${endpoint.path} - 服务器未启动`);
      } else if (error.response && error.response.status === 401) {
        console.log(`   ⚠️  ${endpoint.method} ${endpoint.path} - 需要认证 (正常)`);
      } else {
        console.log(`   ❌ ${endpoint.method} ${endpoint.path} - ${error.message}`);
        failed++;
      }
    }
  }
  
  console.log(`   API测试结果: ${passed} 通过, ${failed} 失败\n`);
  return failed === 0;
}

/**
 * 测试数据模型
 */
async function testDataModels() {
  console.log('📊 测试数据模型...');
  
  try {
    const { 
      FinanceApplication, 
      ApprovalTemplate,
      FinanceRecord 
    } = require('../backend/models/workspace');
    
    // 测试模型定义
    const models = [
      { name: 'FinanceApplication', model: FinanceApplication },
      { name: 'ApprovalTemplate', model: ApprovalTemplate },
      { name: 'FinanceRecord', model: FinanceRecord }
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const { name, model } of models) {
      try {
        // 检查模型是否正确定义
        if (model && typeof model.findAll === 'function') {
          console.log(`   ✅ ${name} 模型定义正确`);
          passed++;
        } else {
          console.log(`   ❌ ${name} 模型定义错误`);
          failed++;
        }
      } catch (error) {
        console.log(`   ❌ ${name} 模型测试失败: ${error.message}`);
        failed++;
      }
    }
    
    console.log(`   模型测试结果: ${passed} 通过, ${failed} 失败\n`);
    return failed === 0;
    
  } catch (error) {
    console.log(`   ❌ 数据模型测试失败: ${error.message}\n`);
    return false;
  }
}

/**
 * 测试业务逻辑
 */
async function testBusinessLogic() {
  console.log('💼 测试业务逻辑...');
  
  const tests = [
    {
      name: '申请编号生成',
      test: () => {
        const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
        const applicationNo = `EXP${date}0001`;
        return applicationNo.length === 15; // EXP + 8位日期 + 4位序号
      }
    },
    {
      name: '金额计算',
      test: () => {
        const amount = 1234.56;
        const rounded = Math.round(amount * 100) / 100;
        return rounded === 1234.56;
      }
    },
    {
      name: '状态验证',
      test: () => {
        const validStatuses = ['draft', 'pending', 'approved', 'rejected', 'cancelled'];
        return validStatuses.includes('pending');
      }
    }
  ];
  
  let passed = 0;
  let failed = 0;
  
  tests.forEach(({ name, test }) => {
    try {
      if (test()) {
        console.log(`   ✅ ${name}`);
        passed++;
      } else {
        console.log(`   ❌ ${name}`);
        failed++;
      }
    } catch (error) {
      console.log(`   ❌ ${name}: ${error.message}`);
      failed++;
    }
  });
  
  console.log(`   业务逻辑测试结果: ${passed} 通过, ${failed} 失败\n`);
  return failed === 0;
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🧪 开始工作台功能测试...\n');
  
  const results = [];
  
  // 运行各项测试
  results.push(await testPermissionSystem());
  results.push(await testDataModels());
  results.push(await testBusinessLogic());
  results.push(await testAPIEndpoints());
  
  // 汇总结果
  const passedTests = results.filter(result => result).length;
  const totalTests = results.length;
  
  console.log('📋 测试汇总:');
  console.log(`   总测试项: ${totalTests}`);
  console.log(`   通过: ${passedTests}`);
  console.log(`   失败: ${totalTests - passedTests}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！工作台功能正常。');
    process.exit(0);
  } else {
    console.log('\n⚠️  部分测试失败，请检查相关功能。');
    process.exit(1);
  }
}

// 执行测试
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('❌ 测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testPermissionSystem,
  testAPIEndpoints,
  testDataModels,
  testBusinessLogic,
  runAllTests
};
