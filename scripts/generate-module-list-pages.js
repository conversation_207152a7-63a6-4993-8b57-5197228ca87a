#!/usr/bin/env node
/**
 * 为剩余的财务子模块生成列表页面
 */

const fs = require('fs');
const path = require('path');

// 剩余需要生成的模块
const modules = [
  {
    name: 'contract',
    title: '合同申请',
    subtitle: '管理和查看合同申请记录',
    color: '#722ed1',
    colorLight: '#9254de'
  },
  {
    name: 'activity',
    title: '活动经费',
    subtitle: '管理和查看活动经费申请',
    color: '#eb2f96',
    colorLight: '#f759ab'
  },
  {
    name: 'reserve',
    title: '备用金',
    subtitle: '管理和查看备用金申请记录',
    color: '#fa8c16',
    colorLight: '#ffa940'
  }
];

// 生成JS文件模板
function generateJSTemplate(module) {
  return `// pages/workspace/${module.name}/list/list.js
const { API } = require('../../../../constants/index.js');
const request = require('../../../../utils/request.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    ${module.name}List: [],
    loading: true,
    refreshing: false,
    hasMore: true,
    page: 1,
    limit: 10,
    
    // 状态映射
    statusMap: {
      'draft': '草稿',
      'pending': '待审批',
      'processing': '审批中',
      'approved': '已通过',
      'rejected': '已拒绝',
      'cancelled': '已取消'
    },
    
    // 状态颜色映射
    statusColorMap: {
      'draft': '#d9d9d9',
      'pending': '#faad14',
      'processing': '#1890ff',
      'approved': '#52c41a',
      'rejected': '#ff4d4f',
      'cancelled': '#d9d9d9'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.load${module.name.charAt(0).toUpperCase() + module.name.slice(1)}List();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.refreshData();
  },

  /**
   * 加载${module.title}列表
   */
  async load${module.name.charAt(0).toUpperCase() + module.name.slice(1)}List() {
    if (!this.data.hasMore && this.data.page > 1) return;
    
    try {
      this.setData({ loading: true });
      
      const response = await request.get(API.API_ENDPOINTS.WORKSPACE.APPLICATIONS.LIST, {
        type: '${module.name}',
        page: this.data.page,
        limit: this.data.limit
      });
      
      if (response.success) {
        const newList = this.data.page === 1 ? response.data.list : [...this.data.${module.name}List, ...response.data.list];
        
        this.setData({
          ${module.name}List: newList,
          hasMore: response.data.pagination.page * response.data.pagination.limit < response.data.pagination.total,
          loading: false
        });
      } else {
        throw new Error(response.message || '加载失败');
      }
    } catch (error) {
      console.error('加载${module.title}列表失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({
      page: 1,
      hasMore: true,
      refreshing: true
    });
    
    await this.load${module.name.charAt(0).toUpperCase() + module.name.slice(1)}List();
    this.setData({ refreshing: false });
  },

  /**
   * 查看详情
   */
  onViewDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: \`/pages/workspace/${module.name}/detail/detail?id=\${id}\`
    });
  },

  /**
   * 新建申请
   */
  onCreate${module.name.charAt(0).toUpperCase() + module.name.slice(1)}() {
    wx.navigateTo({
      url: '/pages/workspace/${module.name}/apply/apply'
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.setData({
        page: this.data.page + 1
      });
      this.load${module.name.charAt(0).toUpperCase() + module.name.slice(1)}List();
    }
  }
})`;
}

// 生成WXML文件模板
function generateWXMLTemplate(module) {
  return `<!--pages/workspace/${module.name}/list/list.wxml-->
<view class="container">
  <!-- 头部标题 -->
  <view class="header">
    <text class="title">${module.title}</text>
    <text class="subtitle">${module.subtitle}</text>
  </view>

  <!-- 新建按钮 -->
  <view class="create-btn-container">
    <button class="create-btn" bindtap="onCreate${module.name.charAt(0).toUpperCase() + module.name.slice(1)}">
      <text class="create-btn-text">+ 新建${module.title}</text>
    </button>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading && ${module.name}List.length === 0}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view wx:elif="{{!loading && ${module.name}List.length === 0}}" class="empty-container">
    <image class="empty-icon" src="/assets/icons/${module.name}.svg" mode="aspectFit"></image>
    <text class="empty-text">暂无${module.title}记录</text>
    <text class="empty-desc">点击上方按钮创建第一个${module.title}</text>
  </view>

  <!-- ${module.title}列表 -->
  <view wx:else class="${module.name}-list">
    <view 
      wx:for="{{${module.name}List}}" 
      wx:key="id" 
      class="${module.name}-item"
      bindtap="onViewDetail"
      data-id="{{item.id}}"
    >
      <!-- 申请信息 -->
      <view class="item-header">
        <view class="item-title">
          <text class="${module.name}-title">{{item.title || '${module.title}'}}</text>
          <view class="status-tag" style="background-color: {{statusColorMap[item.status]}}">
            {{statusMap[item.status]}}
          </view>
        </view>
        <text class="item-amount">¥{{item.amount}}</text>
      </view>

      <view class="item-content">
        <text class="item-desc">{{item.description || item.category}}</text>
        <view class="item-meta">
          <text class="apply-time">申请时间：{{item.created_at}}</text>
          <text class="applicant" wx:if="{{item.applicant_name}}">申请人：{{item.applicant_name}}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="item-actions" wx:if="{{item.status === 'draft'}}">
        <button class="action-btn edit-btn" size="mini" catchtap="onEdit${module.name.charAt(0).toUpperCase() + module.name.slice(1)}" data-id="{{item.id}}">
          编辑
        </button>
        <button class="action-btn submit-btn" size="mini" catchtap="onSubmit${module.name.charAt(0).toUpperCase() + module.name.slice(1)}" data-id="{{item.id}}">
          提交
        </button>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view wx:if="{{loading && ${module.name}List.length > 0}}" class="load-more">
    <text>加载中...</text>
  </view>
  
  <view wx:elif="{{!hasMore && ${module.name}List.length > 0}}" class="load-more">
    <text>没有更多了</text>
  </view>
</view>`;
}

// 生成WXSS文件模板
function generateWXSSTemplate(module) {
  return `/* pages/workspace/${module.name}/list/list.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background: linear-gradient(135deg, ${module.color}, ${module.colorLight});
  padding: 40rpx 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  color: white;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  margin-top: 10rpx;
  display: block;
}

.create-btn-container {
  margin-bottom: 20rpx;
}

.create-btn {
  background: ${module.color};
  color: white;
  border-radius: 25rpx;
  border: none;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.create-btn-text {
  color: white;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid ${module.color};
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.5;
}

.empty-text {
  margin-top: 20rpx;
  font-size: 32rpx;
  color: #999;
}

.empty-desc {
  margin-top: 10rpx;
  font-size: 26rpx;
  color: #ccc;
}

.${module.name}-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.${module.name}-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.${module.name}-item:active {
  transform: scale(0.98);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.item-title {
  display: flex;
  align-items: center;
  gap: 15rpx;
  flex: 1;
}

.${module.name}-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status-tag {
  color: white;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: normal;
}

.item-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: ${module.color};
}

.item-content {
  margin-bottom: 20rpx;
}

.item-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  display: block;
  margin-bottom: 15rpx;
}

.item-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  font-size: 24rpx;
  color: #999;
}

.item-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.action-btn {
  padding: 12rpx 24rpx !important;
  font-size: 26rpx !important;
  border-radius: 20rpx !important;
  border: none !important;
}

.edit-btn {
  background: #faad14 !important;
  color: white !important;
}

.submit-btn {
  background: ${module.color} !important;
  color: white !important;
}

.load-more {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 26rpx;
}`;
}

// 生成所有模块的列表页面
modules.forEach(module => {
  const basePath = `pages/workspace/${module.name}/list`;
  
  // 生成JS文件
  const jsContent = generateJSTemplate(module);
  fs.writeFileSync(`${basePath}/list.js`, jsContent);
  console.log(`✅ 生成 ${module.name} JS 文件`);
  
  // 生成WXML文件
  const wxmlContent = generateWXMLTemplate(module);
  fs.writeFileSync(`${basePath}/list.wxml`, wxmlContent);
  console.log(`✅ 生成 ${module.name} WXML 文件`);
  
  // 生成WXSS文件
  const wxssContent = generateWXSSTemplate(module);
  fs.writeFileSync(`${basePath}/list.wxss`, wxssContent);
  console.log(`✅ 生成 ${module.name} WXSS 文件`);
});

console.log('\n🎉 所有财务子模块列表页面生成完成！');
console.log('现在用户点击快捷操作中的任何模块都能看到完整的列表页面。');
