/**
 * 用户角色管理工具
 * 用于创建测试用户和管理用户角色
 */

const { ROLES } = require('../utils/permission-checker');

// 测试用户数据模板
const testUsersTemplate = [
  {
    name: '张三',
    email: 'z<PERSON><PERSON>@company.com',
    role: ROLES.EMPLOYEE,
    department: '销售部',
    description: '普通员工，只能管理自己的申请'
  },
  {
    name: '李四',
    email: '<EMAIL>',
    role: ROLES.FINANCE,
    department: '财务部',
    description: '财务人员，可以查看和审批财务相关申请'
  },
  {
    name: '王五',
    email: '<EMAIL>',
    role: ROLES.MANAGER,
    department: '管理部',
    description: '经理，可以查看和管理大部分申请'
  },
  {
    name: '赵六',
    email: '<EMAIL>',
    role: ROLES.ADMIN,
    department: 'IT部',
    description: '管理员，拥有最高权限'
  }
];

/**
 * 创建测试用户的SQL语句
 */
function generateCreateUsersSQL() {
  console.log('=== 创建测试用户SQL语句 ===\n');
  
  const sql = `
-- 创建测试用户表（如果不存在）
CREATE TABLE IF NOT EXISTS test_users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  role ENUM('employee', 'finance', 'manager', 'admin') NOT NULL DEFAULT 'employee',
  department VARCHAR(100),
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入测试用户数据
INSERT INTO test_users (name, email, role, department, description) VALUES
${testUsersTemplate.map(user => 
  `('${user.name}', '${user.email}', '${user.role}', '${user.department}', '${user.description}')`
).join(',\n')};

-- 查询创建的测试用户
SELECT * FROM test_users ORDER BY 
  CASE role 
    WHEN 'admin' THEN 1 
    WHEN 'manager' THEN 2 
    WHEN 'finance' THEN 3 
    WHEN 'employee' THEN 4 
  END;
`;

  console.log(sql);
  return sql;
}

/**
 * 生成用户权限测试数据
 */
function generatePermissionTestData() {
  console.log('\n=== 权限测试数据 ===\n');
  
  const testData = {
    users: testUsersTemplate,
    permissions: {
      pages: {
        'finance/overview': {
          employee: false,
          finance: true,
          manager: true,
          admin: true
        },
        'expense/records': {
          employee: true,
          finance: true,
          manager: true,
          admin: true
        },
        'contract/records': {
          employee: true,
          finance: false,
          manager: true,
          admin: true
        }
      },
      operations: {
        'view:own': ['employee', 'finance', 'manager', 'admin'],
        'view:all': ['finance', 'manager', 'admin'],
        'edit:own': ['employee', 'finance', 'manager', 'admin'],
        'delete:own': ['employee', 'finance', 'manager', 'admin'],
        'approve:finance_related': ['finance', 'manager', 'admin'],
        'approve:all': ['manager', 'admin']
      }
    }
  };
  
  console.log(JSON.stringify(testData, null, 2));
  return testData;
}

/**
 * 生成微信小程序测试用户存储数据
 */
function generateWechatTestUsers() {
  console.log('\n=== 微信小程序测试用户数据 ===\n');
  
  const wechatUsers = testUsersTemplate.map((user, index) => ({
    id: index + 1,
    openid: `test_openid_${index + 1}`,
    unionid: `test_unionid_${index + 1}`,
    ...user,
    avatar: '/images/default-avatar.png',
    phone: `1380000000${index + 1}`,
    status: 'active',
    last_login: new Date().toISOString()
  }));
  
  console.log('// 可以存储到微信小程序的本地存储中用于测试');
  console.log('wx.setStorageSync("testUsers", ', JSON.stringify(wechatUsers, null, 2), ');');
  
  return wechatUsers;
}

/**
 * 生成角色切换测试代码
 */
function generateRoleSwitchTestCode() {
  console.log('\n=== 角色切换测试代码 ===\n');
  
  const code = `
// 在微信小程序中添加角色切换功能（仅用于测试）
function switchUserRole(userId, newRole) {
  const testUsers = wx.getStorageSync('testUsers') || [];
  const currentUser = testUsers.find(u => u.id === userId);
  
  if (currentUser) {
    currentUser.role = newRole;
    wx.setStorageSync('testUsers', testUsers);
    wx.setStorageSync('userInfo', currentUser);
    
    wx.showToast({
      title: \`已切换为\${getRoleName(newRole)}\`,
      icon: 'success'
    });
    
    // 重新加载页面以应用新权限
    setTimeout(() => {
      wx.reLaunch({
        url: '/pages/workspace/workspace'
      });
    }, 1500);
  }
}

function getRoleName(role) {
  const roleNames = {
    'employee': '普通员工',
    'finance': '财务人员',
    'manager': '经理',
    'admin': '管理员'
  };
  return roleNames[role] || '未知角色';
}

// 在页面中添加角色切换按钮（开发环境）
if (process.env.NODE_ENV === 'development') {
  // 添加到页面数据中
  this.setData({
    showRoleSwitch: true,
    availableRoles: [
      { value: 'employee', label: '普通员工' },
      { value: 'finance', label: '财务人员' },
      { value: 'manager', label: '经理' },
      { value: 'admin', label: '管理员' }
    ]
  });
}
`;
  
  console.log(code);
  return code;
}

/**
 * 生成权限测试检查清单
 */
function generatePermissionChecklist() {
  console.log('\n=== 权限测试检查清单 ===\n');
  
  const checklist = `
## RBAC权限控制测试检查清单

### 1. 普通员工 (employee) 权限测试
- [ ] 只能查看自己的申请记录
- [ ] 无法访问财务概览页面
- [ ] 可以创建所有类型的申请
- [ ] 只能编辑草稿状态的申请
- [ ] 无法审批任何申请
- [ ] 无法查看其他用户的数据

### 2. 财务人员 (finance) 权限测试
- [ ] 可以访问财务概览页面
- [ ] 可以查看所有财务相关申请（费用报销、付款申请、活动经费、备用金）
- [ ] 无法查看合同申请和采购申请
- [ ] 可以审批财务相关申请
- [ ] 无法审批合同和采购申请
- [ ] 可以查看所有用户的财务相关数据

### 3. 经理 (manager) 权限测试
- [ ] 可以访问所有页面
- [ ] 可以查看所有类型的申请
- [ ] 可以审批所有申请
- [ ] 可以查看所有用户的数据
- [ ] 可以导出报表数据
- [ ] 具有用户管理权限

### 4. 管理员 (admin) 权限测试
- [ ] 拥有最高权限，可以访问所有功能
- [ ] 可以删除任何状态的申请
- [ ] 可以管理用户角色
- [ ] 可以查看系统日志
- [ ] 可以修改系统配置

### 5. 页面访问控制测试
- [ ] 财务概览页面：employee❌, finance✅, manager✅, admin✅
- [ ] 申请记录页面：所有角色✅（但数据范围不同）
- [ ] 审批页面：employee❌, finance✅（部分）, manager✅, admin✅
- [ ] 报表页面：employee❌, finance✅, manager✅, admin✅

### 6. 数据过滤测试
- [ ] 普通员工只能看到自己的数据
- [ ] 财务人员只能看到财务相关的申请
- [ ] 经理和管理员可以看到所有数据
- [ ] API返回的数据符合权限要求

### 7. 操作权限测试
- [ ] 创建申请：所有角色✅
- [ ] 编辑申请：所有者✅（草稿/被拒绝状态）
- [ ] 删除申请：所有者✅（草稿状态），admin✅（所有状态）
- [ ] 审批申请：finance✅（财务相关），manager/admin✅（所有）
- [ ] 导出数据：finance/manager/admin✅

### 8. 边界情况测试
- [ ] 无效角色处理
- [ ] 权限升级/降级后的页面刷新
- [ ] 跨角色数据访问尝试
- [ ] API权限验证失败处理
- [ ] 前端权限检查绕过尝试

### 9. 用户体验测试
- [ ] 权限不足时的友好提示
- [ ] 角色徽章显示正确
- [ ] 功能模块根据权限动态显示
- [ ] 快速操作根据权限过滤
- [ ] 页面加载性能正常
`;
  
  console.log(checklist);
  return checklist;
}

/**
 * 运行所有生成器
 */
function runAllGenerators() {
  console.log('🚀 生成RBAC测试相关文件和数据...\n');
  
  generateCreateUsersSQL();
  generatePermissionTestData();
  generateWechatTestUsers();
  generateRoleSwitchTestCode();
  generatePermissionChecklist();
  
  console.log('\n✅ 所有测试数据和代码生成完成！');
  console.log('\n📋 生成内容包括:');
  console.log('- 测试用户SQL创建语句');
  console.log('- 权限测试数据结构');
  console.log('- 微信小程序测试用户数据');
  console.log('- 角色切换测试代码');
  console.log('- 权限测试检查清单');
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllGenerators();
}

module.exports = {
  runAllGenerators,
  generateCreateUsersSQL,
  generatePermissionTestData,
  generateWechatTestUsers,
  generateRoleSwitchTestCode,
  generatePermissionChecklist,
  testUsersTemplate
};
