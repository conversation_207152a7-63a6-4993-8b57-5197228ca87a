#!/usr/bin/env node

/**
 * 验证 app.json 中 preloadRule 配置的页面路径是否存在
 */

const fs = require('fs');
const path = require('path');

function validatePreloadRules() {
  console.log('🔍 验证 app.json 预加载规则...\n');
  
  try {
    // 读取 app.json
    const appJsonPath = path.join(__dirname, '../app.json');
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    
    if (!appJson.preloadRule) {
      console.log('✅ 没有找到 preloadRule 配置');
      return true;
    }
    
    const preloadRule = appJson.preloadRule;
    let allValid = true;
    let totalRules = 0;
    let validRules = 0;
    
    console.log('📋 检查预加载规则:');
    
    // 检查每个预加载规则
    for (const [pagePath, config] of Object.entries(preloadRule)) {
      totalRules++;
      console.log(`\n🔸 规则: ${pagePath}`);
      
      // 检查主页面是否存在
      const mainPageExists = checkPageExists(pagePath);
      console.log(`   主页面: ${mainPageExists ? '✅' : '❌'} ${pagePath}`);
      
      if (!mainPageExists) {
        allValid = false;
        continue;
      }
      
      // 检查预加载包中的页面
      if (config.packages && Array.isArray(config.packages)) {
        let packageValid = true;
        
        for (const packagePath of config.packages) {
          const packageExists = checkPackageExists(packagePath);
          console.log(`   预加载包: ${packageExists ? '✅' : '❌'} ${packagePath}`);
          
          if (!packageExists) {
            packageValid = false;
            allValid = false;
          }
        }
        
        if (packageValid) {
          validRules++;
        }
      } else {
        validRules++;
      }
      
      // 显示网络配置
      console.log(`   网络要求: ${config.network || 'all'}`);
    }
    
    // 显示总结
    console.log('\n📊 验证结果:');
    console.log(`   总规则数: ${totalRules}`);
    console.log(`   有效规则: ${validRules}`);
    console.log(`   无效规则: ${totalRules - validRules}`);
    
    if (allValid) {
      console.log('\n🎉 所有预加载规则验证通过！');
    } else {
      console.log('\n⚠️  发现无效的预加载规则，请修复后重试。');
    }
    
    return allValid;
    
  } catch (error) {
    console.error('❌ 验证预加载规则失败:', error.message);
    return false;
  }
}

/**
 * 检查页面是否存在
 */
function checkPageExists(pagePath) {
  const possibleExtensions = ['.js', '.wxml', '.wxss', '.json'];
  const basePath = path.join(__dirname, '..', pagePath);
  
  // 检查是否存在任何相关文件
  return possibleExtensions.some(ext => {
    return fs.existsSync(basePath + ext);
  });
}

/**
 * 检查包（页面目录）是否存在
 */
function checkPackageExists(packagePath) {
  const fullPath = path.join(__dirname, '..', packagePath);
  
  try {
    const stat = fs.statSync(fullPath);
    return stat.isDirectory();
  } catch (error) {
    return false;
  }
}

/**
 * 生成修复建议
 */
function generateFixSuggestions() {
  console.log('\n💡 修复建议:');
  console.log('1. 删除不存在页面的预加载规则');
  console.log('2. 更新页面路径到正确的位置');
  console.log('3. 确保所有引用的页面都已创建');
  console.log('4. 检查页面路径的拼写是否正确');
}

// 执行验证
if (require.main === module) {
  const isValid = validatePreloadRules();
  
  if (!isValid) {
    generateFixSuggestions();
    process.exit(1);
  }
  
  process.exit(0);
}

module.exports = {
  validatePreloadRules,
  checkPageExists,
  checkPackageExists
};
