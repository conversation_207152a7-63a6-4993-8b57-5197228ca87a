#!/usr/bin/env node
/**
 * 验证WXML文件的标签结构
 */

const fs = require('fs');

function validateWXMLStructure(filePath) {
  console.log(`🔍 验证文件: ${filePath}`);
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    const stack = [];
    const errors = [];
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      
      // 匹配开始标签
      const openTagMatches = line.match(/<(\w+)(?:\s[^>]*)?(?<!\/)\s*>/g);
      if (openTagMatches) {
        openTagMatches.forEach(match => {
          const tagName = match.match(/<(\w+)/)[1];
          // 排除自闭合标签
          if (!match.includes('/>')) {
            stack.push({ tag: tagName, line: lineNumber });
          }
        });
      }
      
      // 匹配结束标签
      const closeTagMatches = line.match(/<\/(\w+)>/g);
      if (closeTagMatches) {
        closeTagMatches.forEach(match => {
          const tagName = match.match(/<\/(\w+)>/)[1];
          
          if (stack.length === 0) {
            errors.push(`第${lineNumber}行: 找到多余的结束标签 </${tagName}>`);
          } else {
            const lastTag = stack.pop();
            if (lastTag.tag !== tagName) {
              errors.push(`第${lineNumber}行: 标签不匹配，期望 </${lastTag.tag}>，实际 </${tagName}>`);
            }
          }
        });
      }
    });
    
    // 检查未闭合的标签
    if (stack.length > 0) {
      stack.forEach(tag => {
        errors.push(`第${tag.line}行: 标签 <${tag.tag}> 未闭合`);
      });
    }
    
    if (errors.length === 0) {
      console.log('✅ WXML文件结构正确');
      return true;
    } else {
      console.log('❌ 发现以下错误:');
      errors.forEach(error => console.log(`  - ${error}`));
      return false;
    }
    
  } catch (error) {
    console.log(`❌ 读取文件失败: ${error.message}`);
    return false;
  }
}

// 验证采购申请页面
const filePath = 'pages/workspace/purchase/apply/apply.wxml';
const isValid = validateWXMLStructure(filePath);

if (isValid) {
  console.log('\n🎉 采购申请页面WXML结构验证通过！');
} else {
  console.log('\n💥 采购申请页面WXML结构存在问题，请检查并修复。');
  process.exit(1);
}
