#!/usr/bin/env node

/**
 * 智慧养鹅SAAS平台 - API重构验证脚本
 * Smart Goose SAAS Platform - API Restructure Verification Script
 * 
 * 验证统一API路由系统是否正常工作
 */

const http = require('http');

const API_BASE = 'http://localhost:3000';

// 测试用例配置
const TEST_CASES = [
  {
    name: 'API版本信息',
    method: 'GET',
    path: '/api/v1',
    expectedStatus: 200,
    expectedFields: ['errcode', 'errmsg', 'data', 'timestamp'],
    expectedData: {
      errcode: 0,
      errmsg: 'ok'
    }
  },
  {
    name: 'API详细版本信息',
    method: 'GET', 
    path: '/api/v1/version',
    expectedStatus: 200,
    expectedFields: ['errcode', 'errmsg', 'data'],
    expectedData: {
      errcode: 0,
      errmsg: 'ok'
    }
  },
  {
    name: '404错误处理',
    method: 'GET',
    path: '/api/v1/nonexistent',
    expectedStatus: 401, // 非认证端点会被认证中间件拦截
    expectedFields: ['success', 'message'],
    expectedData: {
      success: false,
      message: '访问令牌缺失'
    }
  },
  {
    name: '认证端点访问（无Token）',
    method: 'GET',
    path: '/api/v1/users',
    expectedStatus: 401,
    expectedFields: ['success', 'message'],
    expectedData: {
      success: false,
      message: '访问令牌缺失'
    }
  }
];

// 执行HTTP请求
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, API_BASE);
    const options = {
      method: method,
      hostname: url.hostname,
      port: url.port,
      path: url.pathname,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'API-Verification-Script/1.0'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: responseData,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// 验证测试结果
function validateTestCase(testCase, result) {
  const errors = [];

  // 检查状态码
  if (result.status !== testCase.expectedStatus) {
    errors.push(`状态码错误: 期望 ${testCase.expectedStatus}, 实际 ${result.status}`);
  }

  // 检查响应解析
  if (result.parseError) {
    errors.push(`响应解析错误: ${result.parseError}`);
    return errors;
  }

  // 检查必需字段
  if (testCase.expectedFields) {
    for (const field of testCase.expectedFields) {
      if (!(field in result.data)) {
        errors.push(`缺少必需字段: ${field}`);
      }
    }
  }

  // 检查数据值
  if (testCase.expectedData) {
    for (const [key, expectedValue] of Object.entries(testCase.expectedData)) {
      if (result.data[key] !== expectedValue) {
        errors.push(`字段值错误: ${key} 期望 ${expectedValue}, 实际 ${result.data[key]}`);
      }
    }
  }

  return errors;
}

// 主执行函数
async function runVerification() {
  console.log('🚀 开始验证智慧养鹅SAAS平台API重构结果...\n');
  
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;

  for (const testCase of TEST_CASES) {
    totalTests++;
    console.log(`📋 测试: ${testCase.name}`);
    console.log(`   请求: ${testCase.method} ${testCase.path}`);

    try {
      const result = await makeRequest(testCase.method, testCase.path);
      const errors = validateTestCase(testCase, result);

      if (errors.length === 0) {
        console.log(`   ✅ 通过 (${result.status})`);
        passedTests++;
      } else {
        console.log(`   ❌ 失败 (${result.status})`);
        errors.forEach(error => console.log(`      - ${error}`));
        failedTests++;
      }
    } catch (error) {
      console.log(`   ❌ 请求失败: ${error.message}`);
      failedTests++;
    }

    console.log('');
  }

  // 输出总结
  console.log('📊 验证结果总结:');
  console.log(`   总测试数: ${totalTests}`);
  console.log(`   通过: ${passedTests} ✅`);
  console.log(`   失败: ${failedTests} ❌`);
  console.log(`   通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

  if (failedTests === 0) {
    console.log('\n🎉 所有测试通过! API重构成功完成。');
    console.log('\n📋 重构成果:');
    console.log('   • 统一API版本控制 (/api/v1)');
    console.log('   • 微信小程序兼容响应格式');
    console.log('   • RESTful API设计规范');
    console.log('   • 标准化错误处理');
    console.log('   • 认证中间件正确应用');
    process.exit(0);
  } else {
    console.log('\n⚠️  部分测试失败，请检查API配置。');
    process.exit(1);
  }
}

// 运行验证
if (require.main === module) {
  runVerification().catch(error => {
    console.error('❌ 验证脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { makeRequest, validateTestCase, runVerification };